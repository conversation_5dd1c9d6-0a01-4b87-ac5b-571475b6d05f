<svg viewBox="0 0 78 78" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.9" filter="url(#filter0_bd_0_1)">
<circle cx="21" cy="21" r="21" transform="matrix(-1 0 0 1 60 14)" fill="#F0F0F0"/>
</g>
<path d="M40.3125 30.8732L36.5823 34.6034C36.1418 35.044 36.1418 35.7648 36.5823 36.2054L40.3125 39.9355" stroke="#292D32" stroke-width="3" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<defs>
<filter id="filter0_bd_0_1" x="0" y="0" width="78" height="78" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_0_1"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="9"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_0_1" result="effect2_dropShadow_0_1"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_0_1" result="shape"/>
</filter>
</defs>
</svg>

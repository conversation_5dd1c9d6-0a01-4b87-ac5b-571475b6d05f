<svg xmlns="http://www.w3.org/2000/svg" width="78" height="78" viewBox="0 0 78 78" fill="none">
  <g opacity="0.9" filter="url(#filter0_bd_0_57643)">
    <circle cx="39" cy="35" r="21" fill="#F0F0F0"/>
  </g>
  <defs>
    <filter id="filter0_bd_0_57643" x="0" y="0" width="78" height="78" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
      <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_0_57643"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset dy="4"/>
      <feGaussianBlur stdDeviation="9"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
      <feBlend mode="normal" in2="effect1_backgroundBlur_0_57643" result="effect2_dropShadow_0_57643"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_0_57643" result="shape"/>
    </filter>
  </defs>
</svg>
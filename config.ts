export const ENVIRONMENT: 'local' | 'dev' | 'beta' | 'prod' =
  process.env.NEXT_PUBLIC_ENVIRONMENT || ('local' as any);

export const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

// EmailJS configuration
export const TEMPLATE_ID = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID || process.env.NEXT_EMAILJS_TEMPLATE_ID || '';
export const PUBLIC_KEY = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || process.env.NEXT_EMAILJS_PUBLIC_KEY || '';
export const SERVICE_ID = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || process.env.NEXT_EMAILJS_SERVICE_ID || '';

export let APP_URL: string;
if (ENVIRONMENT === 'local') {
  APP_URL = 'http://localhost:3000';
} else if (ENVIRONMENT === 'dev') {
  APP_URL = 'icb-app-dev.buildverse.app';
} else if (ENVIRONMENT === 'beta') {
  APP_URL = 'indiancashback.com';
} else {
  APP_URL = 'indiancashback.com';
}

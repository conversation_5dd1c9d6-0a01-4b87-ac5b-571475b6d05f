{"openapi": "3.0.0", "paths": {"/context/banner": {"get": {"operationId": "BannerController_getBanners", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BannerResponse"}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BannerResponse"}}}}}, "tags": ["Context"]}}, "/context/quick-access": {"get": {"operationId": "QuickAccessController_getQuickAccesses", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/QuickAccessResponseItem"}}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/QuickAccessResponseItem"}}}}}}, "tags": ["Context"]}}, "/context/quick-access/hero": {"get": {"operationId": "QuickAccessController_getHeroQuickAccesses", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HeroResponseItem"}}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HeroResponseItem"}}}}}}, "tags": ["Context"]}}, "/context/stories": {"get": {"operationId": "StoriesController_getStories", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ResponseMobileStories"}}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ResponseMobileStories"}}}}}}, "tags": ["Context"]}}, "/context/offers": {"get": {"operationId": "OffersController_getLandingOffers", "parameters": [], "responses": {"200": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategorizedOffers"}}}}}, "tags": ["Context"], "security": [{"cookie": []}]}}, "/context/offers/all-on-going-offers": {"get": {"operationId": "OffersController_getOnGoingSaleOffers", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetAllOnGoingOffersResponse"}}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetAllOnGoingOffersResponse"}}}}}}, "tags": ["Context"]}}, "/context/category": {"get": {"operationId": "CategoryController_getAllCategories", "parameters": [{"name": "trending", "required": true, "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": ""}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryResponse"}}}}}}, "tags": ["Context"]}}, "/context/category/sub-category{id}": {"get": {"operationId": "CategoryController_getSubCategoryByCategoryId", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubCategoriesByCategoryResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubCategoriesByCategoryResponse"}}}}}, "tags": ["Context"], "security": [{"cookie": []}]}}, "/context/category/search{value}": {"get": {"operationId": "CategoryController_getSubCategorySearch", "parameters": [{"name": "value", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SubCategoriesByCategory"}}}}}}, "tags": ["Context"], "security": [{"cookie": []}]}}, "/context/category/all-categories-details": {"get": {"operationId": "CategoryController_getAllCategoriesDetails", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AllCategoriesResponse"}}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AllCategoriesResponse"}}}}}}, "tags": ["Context"]}}, "/context/stores-by-cb-percent": {"get": {"operationId": "StoresController_getContextStoresByCb", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoresByCbContextResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoresByCbContextResponse"}}}}}, "tags": ["Context"], "security": [{"cookie": []}]}}, "/context/stores-by-cb-percent/find-by-category": {"get": {"operationId": "StoresController_getAllCategories", "parameters": [], "responses": {"200": {"description": ""}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryStoresResponse"}}}}}}, "tags": ["Context"]}}, "/context/search": {"get": {"operationId": "SearchController_getSearchResults", "parameters": [{"name": "text", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResponseItem"}}}}}, "tags": ["Context"]}}, "/context/terms-and-privacy": {"get": {"operationId": "TermsAndPrivacyController_getAllTermsAndConditions", "parameters": [{"name": "type", "required": true, "in": "query", "schema": {"example": "terms", "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TermsAndPrivacyResponseItem"}}}}}, "tags": ["Context"]}}, "/context/testimonials": {"get": {"operationId": "TestimonialController_getAllTestimonials", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TestimonialResponseType"}}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TestimonialResponseType"}}}}}}, "tags": ["Context"]}}, "/auth/register": {"post": {"operationId": "AuthController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "tags": ["<PERSON><PERSON>"]}}, "/auth/login": {"post": {"operationId": "AuthController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}}, "tags": ["<PERSON><PERSON>"]}}, "/auth/verify-user": {"post": {"operationId": "AuthController_verifyOtp", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyUserDto"}}}}, "responses": {"201": {"description": ""}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}}}, "tags": ["<PERSON><PERSON>"]}}, "/auth/resend-otp": {"post": {"operationId": "AuthController_resendOtp", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}}}, "tags": ["<PERSON><PERSON>"]}}, "/auth/logout": {"delete": {"operationId": "AuthController_logout", "parameters": [], "responses": {"200": {"description": ""}, "401": {"description": "Unauthorized"}}, "tags": ["<PERSON><PERSON>"], "security": [{"cookie": []}]}}, "/auth/check": {"get": {"operationId": "AuthController_check", "parameters": [], "responses": {"200": {"description": ""}, "401": {"description": "Unauthorized"}}, "tags": ["<PERSON><PERSON>"], "security": [{"cookie": []}]}}, "/auth/token": {"get": {"operationId": "AuthController_getCsrfToken", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}}, "tags": ["<PERSON><PERSON>"]}}, "/auth/google": {"get": {"operationId": "AuthController_googleAuth", "parameters": [{"name": "referralCode", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}, "default": {"description": "Initiates Google OAuth authentication flow"}}, "tags": ["<PERSON><PERSON>"]}}, "/auth/google/callback": {"get": {"operationId": "AuthController_googleAuthCallback", "parameters": [{"name": "redirect", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "state", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}, "default": {"description": "Handles the callback from Google OAuth authentication"}}, "tags": ["<PERSON><PERSON>"]}}, "/auth/verify-token": {"get": {"operationId": "AuthController_verifyToken", "parameters": [{"name": "token", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["<PERSON><PERSON>"]}}, "/users/get-bank-details": {"get": {"operationId": "UserController_getBankDetails", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetBankAccountDataResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetBankAccountDataResponse"}}}}}, "tags": ["Users"], "security": [{"cookie": []}]}}, "/users/update-bank-details": {"post": {"operationId": "UserController_updateBankDetails", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBankAccountDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}}}, "tags": ["Users"], "security": [{"cookie": []}]}}, "/users/update-upi": {"patch": {"operationId": "UserController_updateUpiId", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUpiDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}}}, "tags": ["Users"], "security": [{"cookie": []}]}}, "/users/me": {"get": {"operationId": "UserController_getProfileDetails", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserProfileResponseItem"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserProfileResponseItem"}}}}}, "tags": ["Users"], "security": [{"cookie": []}]}}, "/users/overview": {"get": {"operationId": "UserController_getUsersOverViewDetails", "parameters": [], "responses": {"200": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserOverviewResponse"}}}}}, "tags": ["Users"], "security": [{"cookie": []}]}}, "/users/referral-history": {"get": {"operationId": "UserController_getUsersByReferralCode", "parameters": [{"name": "searchParam", "required": false, "in": "query", "schema": {"example": "a", "type": "string"}}, {"name": "startDate", "required": false, "in": "query", "schema": {"example": "2021-10-10", "type": "string"}}, {"name": "endDate", "required": false, "in": "query", "schema": {"example": "2021-10-10", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/ReferralTypes"}}, {"name": "status", "required": false, "in": "query", "schema": {"default": "active", "enum": ["active", "inactive", "blocked"], "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUsersByReferralCodeResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUsersByReferralCodeResponse"}}}}}, "tags": ["Users"], "security": [{"cookie": []}]}}, "/users/cashback-history": {"get": {"operationId": "UserController_getCashbackHistory", "parameters": [{"name": "searchParam", "required": true, "in": "query", "schema": {"example": "a", "type": "string"}}, {"name": "startDate", "required": true, "in": "query", "schema": {"default": "2023-10-01", "type": "string"}}, {"name": "endDate", "required": true, "in": "query", "schema": {"default": "2024-12-31", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/CashbackSortTypes"}}, {"name": "stores", "required": false, "in": "query", "schema": {"example": "1,2,3", "type": "string"}}, {"name": "status", "required": false, "in": "query", "schema": {"example": "cancelled,pending,confirmed", "enum": ["pending", "confirmed", "cancelled"], "type": "string"}}, {"name": "type", "required": false, "in": "query", "description": "Filter by earnings type (comma-separated values: click,missing,referral,share)", "schema": {"example": "click,missing,referral", "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCbHistoryResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCbHistoryResponse"}}}}}, "tags": ["Users"], "security": [{"cookie": []}]}}, "/users/update-profile": {"post": {"operationId": "UserController_updateProfile", "parameters": [], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/UpdateProfileWithImageDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}}}, "tags": ["Users"], "security": [{"cookie": []}]}}, "/users/update-credentials": {"post": {"operationId": "UserController_sendOtpToUpdateUserCredentials", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCredentialsDto"}}}}, "responses": {"201": {"description": ""}, "401": {"description": "Unauthorized"}}, "tags": ["Users"], "security": [{"cookie": []}]}}, "/users/verify-credentials-otp": {"post": {"operationId": "UserController_verifyOtpToUpdateCredentials", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OtpDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "401": {"description": "Unauthorized"}}, "tags": ["Users"], "security": [{"cookie": []}]}}, "/users/get-personal-interest": {"get": {"operationId": "UserController_getAllPersonalInterestData", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Users"]}}, "/users/update-referral-code": {"post": {"operationId": "UserController_updateAllUsersReferralCode", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Users"]}}, "/users/list": {"get": {"operationId": "UserController_listUsers", "parameters": [], "responses": {"default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserListResponseDto"}}}}}, "tags": ["Users"]}}, "/campaign/referral-earnings-leaderboard": {"get": {"operationId": "ReferralCampaignController_getReferralEarningsLeaderboard", "parameters": [{"name": "timeFrame", "required": true, "in": "query", "schema": {"enum": ["weekly", "monthly", "all"], "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetReferralLeaderboardResponse"}}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GetReferralLeaderboardResponse"}}}}}}, "tags": ["ReferralCampaign"]}}, "/stores": {"get": {"operationId": "StoreController_getAllStores", "parameters": [{"name": "searchParam", "required": true, "in": "query", "schema": {"example": "and", "type": "string"}}, {"name": "sortType", "required": true, "in": "query", "schema": {"$ref": "#/components/schemas/SortTypes"}}, {"name": "minPercent", "required": true, "in": "query", "schema": {"example": "10", "type": "number"}}, {"name": "maxPercent", "required": true, "in": "query", "schema": {"example": "60", "type": "number"}}, {"name": "subCategories", "required": false, "in": "query", "schema": {"example": "2,4,6,7", "type": "string"}}, {"name": "saved", "required": true, "in": "query", "schema": {}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAllStoresResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAllStoresResponse"}}}}}, "tags": ["Stores"], "security": [{"cookie": []}]}}, "/stores/store-details{name}": {"get": {"operationId": "StoreController_getStoreDetailsByName", "parameters": [{"name": "name", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetStoreDetailsResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetStoreDetailsResponse"}}}}}, "tags": ["Stores"], "security": [{"cookie": []}]}}, "/stores/cashback-rates-by-store{id}": {"get": {"operationId": "StoreController_getCashbackRatesByStoreId", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCashbackRatesByStoreResponse"}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCashbackRatesByStoreResponse"}}}}}, "tags": ["Stores"]}}, "/stores/save": {"post": {"operationId": "StoreController_saveItem", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveOfferDto"}}}}, "responses": {"201": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveItemResponse"}}}}}, "tags": ["Stores"], "security": [{"cookie": []}]}}, "/stores/remove": {"post": {"operationId": "StoreController_removeSavedItem", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveOfferDto"}}}}, "responses": {"201": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveItemResponse"}}}}}, "tags": ["Stores"], "security": [{"cookie": []}]}}, "/gift-cards": {"get": {"operationId": "GiftCardController_getAllGiftCards", "parameters": [{"name": "searchParam", "required": true, "in": "query", "schema": {"example": "a", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/SortTypes"}}, {"name": "subCategories", "required": false, "in": "query", "schema": {"example": "2,4,6,7", "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetGiftCardListResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetGiftCardListResponse"}}}}}, "tags": ["GiftCards"], "security": [{"cookie": []}]}}, "/gift-cards/banners": {"get": {"operationId": "GiftCardController_getAllGiftCardBanners", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GiftCardBannersResponse"}}}}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GiftCardBannersResponse"}}}}}, "tags": ["GiftCards"]}}, "/gift-cards/gift-card{id}": {"get": {"operationId": "GiftCardController_getGiftCardDetails", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetGiftCardResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetGiftCardResponse"}}}}}, "tags": ["GiftCards"], "security": [{"cookie": []}]}}, "/gift-cards/save": {"post": {"operationId": "GiftCardController_saveItem", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveOfferDto"}}}}, "responses": {"201": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveItemResponse"}}}}}, "tags": ["GiftCards"], "security": [{"cookie": []}]}}, "/gift-cards/remove": {"post": {"operationId": "GiftCardController_removeSavedItem", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveOfferDto"}}}}, "responses": {"201": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveItemResponse"}}}}}, "tags": ["GiftCards"], "security": [{"cookie": []}]}}, "/gift-cards/create-order": {"post": {"operationId": "GiftCardController_orderGiftCard", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderGiftCardDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderResponse"}}}}}, "tags": ["GiftCards"], "security": [{"cookie": []}]}}, "/gift-cards/verify-payment": {"put": {"operationId": "GiftCardController_verifyPayment", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentVerifyDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "401": {"description": "Unauthorized"}}, "tags": ["GiftCards"], "security": [{"cookie": []}]}}, "/gift-cards/redeem": {"post": {"operationId": "GiftCardController_redeemIcbGiftCard", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RedeemIcbGiftCardDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveItemResponse"}}}}}, "tags": ["GiftCards"], "security": [{"cookie": []}]}, "get": {"operationId": "GiftCardController_redeemGiftCardHistory", "parameters": [{"name": "searchParam", "required": true, "in": "query", "schema": {"example": "ICB123456", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/RedeemHistoryTypes"}}, {"name": "date", "required": true, "in": "query", "schema": {"default": "2024-02-25T18:19:26.808Z", "type": "string"}}], "responses": {"200": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetRedeemGiftCardListResponse"}}}}}, "tags": ["GiftCards"], "security": [{"cookie": []}]}}, "/gift-cards/icb-card": {"get": {"operationId": "GiftCardController_getIcbCard", "parameters": [], "responses": {"200": {"description": ""}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IcbCardTypeResponse"}}}}}, "tags": ["GiftCards"]}}, "/offers/deals-and-coupons": {"get": {"operationId": "OfferController_getAllCategories", "parameters": [{"name": "searchParam", "required": true, "in": "query", "schema": {"example": "and", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/SortTypes"}}, {"name": "userType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/UserTypes"}}, {"name": "offerType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/OfferTypes"}}, {"name": "subCategories", "required": false, "in": "query", "schema": {"example": "2,4,6,7", "type": "string"}}, {"name": "storeId", "required": false, "in": "query", "schema": {"example": "2", "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DealAndCouponsResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DealAndCouponsResponse"}}}}}, "tags": ["Offers"], "security": [{"cookie": []}]}}, "/offers/ongoing-offers": {"get": {"operationId": "OfferController_getOngoingOffers", "parameters": [{"name": "searchParam", "required": true, "in": "query", "schema": {"example": "and", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/SortTypes"}}, {"name": "offerType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/OfferTypes"}}, {"name": "subCategories", "required": false, "in": "query", "schema": {"example": "2,4,6,7", "type": "string"}}, {"name": "sales", "required": false, "in": "query", "schema": {"example": "2,4,6,7", "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OngoingOffersResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OngoingOffersResponse"}}}}}, "tags": ["Offers"], "security": [{"cookie": []}]}}, "/offers/offer{uid}": {"get": {"operationId": "OfferController_getOfferById", "parameters": [{"name": "uid", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetOfferByIdResponse"}}}}}, "tags": ["Offers"], "security": [{"cookie": []}]}}, "/offers/offer/title/{title}": {"get": {"operationId": "OfferController_getOfferByTitle", "parameters": [{"name": "title", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSimilarOffers"}}}}}, "tags": ["Offers"], "security": [{"cookie": []}]}}, "/offers/save": {"post": {"operationId": "OfferController_saveItem", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveOfferDto"}}}}, "responses": {"201": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveItemResponse"}}}}}, "tags": ["Offers"], "security": [{"cookie": []}]}}, "/offers/remove": {"post": {"operationId": "OfferController_removeSavedItem", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RemoveOfferDto"}}}}, "responses": {"201": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SaveItemResponse"}}}}}, "tags": ["Offers"], "security": [{"cookie": []}]}}, "/click": {"post": {"operationId": "ClickController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateClickDto"}}}}, "responses": {"201": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClickCreateResponse"}}}}}, "tags": ["Click"], "security": [{"cookie": []}]}, "get": {"operationId": "ClickController_getClicks", "parameters": [{"name": "searchParam", "required": false, "in": "query", "schema": {"example": "and", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/SortTypes"}}, {"name": "stores", "required": false, "in": "query", "schema": {"example": "1,2,3", "type": "string"}}, {"name": "status", "required": false, "in": "query", "schema": {"example": "Pending,Tracked,Confirmed,Cancelled,Report Missing CB", "type": "string"}}, {"name": "startDate", "required": false, "in": "query", "schema": {"example": "2021-10-10", "type": "string"}}, {"name": "endDate", "required": false, "in": "query", "schema": {"example": "2021-10-10", "type": "string"}}], "responses": {"200": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClicksResponse"}}}}}, "tags": ["Click"], "security": [{"cookie": []}]}}, "/click/no-auth": {"post": {"operationId": "ClickController_createNoAuth", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateClickDto"}}}}, "responses": {"201": {"description": ""}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClickCreateResponse"}}}}}, "tags": ["Click"]}}, "/click/stores": {"get": {"operationId": "ClickController_getClicksByStores", "parameters": [{"name": "searchParam", "required": false, "in": "query", "schema": {"example": "and", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/SortTypes"}}, {"name": "stores", "required": false, "in": "query", "schema": {"example": "1,2,3", "type": "string"}}, {"name": "status", "required": false, "in": "query", "schema": {"example": "Pending,Tracked,Confirmed,Cancelled,Report Missing CB", "type": "string"}}, {"name": "date", "required": false, "in": "query", "schema": {"example": "2021-10-10", "type": "string"}}], "responses": {"200": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClicksByStoreResponse"}}}}}, "tags": ["Click"], "security": [{"cookie": []}]}}, "/click/clicked_stores": {"get": {"operationId": "ClickController_getClickedStores", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}}}}}, "401": {"description": "Unauthorized"}}, "tags": ["Click"], "security": [{"cookie": []}]}}, "/click/missing-cashback/report": {"post": {"operationId": "MissingCashbackController_reportMissingCashback", "parameters": [], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/ReportMissingCashbackDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}}, "tags": ["Click"], "security": [{"cookie": []}]}}, "/click/missing-cashback/list": {"get": {"operationId": "MissingCashbackController_listMissingCashback", "parameters": [{"name": "searchParam", "required": false, "in": "query", "schema": {"example": "and", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/SortTypes"}}, {"name": "stores", "required": false, "in": "query", "schema": {"example": "1,2,3", "type": "string"}}, {"name": "status", "required": false, "in": "query", "schema": {"example": "not-solved,solved,rejected,forwarded", "type": "string"}}, {"name": "startDate", "required": false, "in": "query", "schema": {"example": "2021-10-10", "type": "string"}}, {"name": "endDate", "required": false, "in": "query", "schema": {"example": "2021-10-10", "type": "string"}}], "responses": {"200": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MissingCashbackResponse"}}}}}, "tags": ["Click"], "security": [{"cookie": []}]}}, "/payment/withdraw": {"post": {"operationId": "PaymentController_requestPayments", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequestDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "boolean"}}}}}, "tags": ["PaymentRequest"], "security": [{"cookie": []}]}}, "/payment": {"get": {"operationId": "PaymentController_getAllPaymentRequestedUser", "parameters": [{"name": "searchParam", "required": false, "in": "query", "schema": {"example": "a", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/PaymentSortTypes"}}, {"name": "paymentType", "required": true, "in": "query", "schema": {"$ref": "#/components/schemas/PaymentTypes"}}, {"name": "status", "required": false, "in": "query", "schema": {"example": "Requested,Paid,Cancelled", "type": "string"}}, {"name": "startDate", "required": false, "in": "query", "schema": {"default": "2024-03-27T07:00:30.418Z", "type": "string"}}, {"name": "endDate", "required": false, "in": "query", "schema": {"default": "2024-03-27T07:00:31.418Z", "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPaymentListResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPaymentListResponse"}}}}}, "tags": ["PaymentRequest"], "security": [{"cookie": []}]}}, "/saved-items/deals": {"get": {"operationId": "SavedItemController_getAllSavedDeals", "parameters": [{"name": "searchParam", "required": true, "in": "query", "schema": {"example": "and", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/SortTypes"}}, {"name": "userType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/UserTypes"}}, {"name": "subCategories", "required": false, "in": "query", "schema": {"example": "2,4,6,7", "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavedDealsResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavedDealsResponse"}}}}}, "tags": ["SavedItems"], "security": [{"cookie": []}]}}, "/saved-items/saved-offer-ids": {"get": {"operationId": "SavedItemController_getAllSavedOfferUids", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "number"}}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "number"}}}}}}, "tags": ["SavedItems"], "security": [{"cookie": []}]}}, "/saved-items/coupons": {"get": {"operationId": "SavedItemController_getAllSavedCoupons", "parameters": [{"name": "searchParam", "required": true, "in": "query", "schema": {"example": "and", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/SortTypes"}}, {"name": "userType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/UserTypes"}}, {"name": "subCategories", "required": false, "in": "query", "schema": {"example": "2,4,6,7", "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavedCouponsResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavedCouponsResponse"}}}}}, "tags": ["SavedItems"], "security": [{"cookie": []}]}}, "/saved-items/stores": {"get": {"operationId": "SavedItemController_getAllSavedStores", "parameters": [{"name": "searchParam", "required": true, "in": "query", "schema": {"example": "and", "type": "string"}}, {"name": "sortType", "required": true, "in": "query", "schema": {"$ref": "#/components/schemas/SortTypes"}}, {"name": "minPercent", "required": true, "in": "query", "schema": {"example": "10", "type": "number"}}, {"name": "maxPercent", "required": true, "in": "query", "schema": {"example": "60", "type": "number"}}, {"name": "subCategories", "required": false, "in": "query", "schema": {"example": "2,4,6,7", "type": "string"}}, {"name": "saved", "required": true, "in": "query", "schema": {}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAllStoresResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAllStoresResponse"}}}}}, "tags": ["SavedItems"], "security": [{"cookie": []}]}}, "/saved-items/gift-cards": {"get": {"operationId": "SavedItemController_getAllSavedGiftCards", "parameters": [{"name": "searchParam", "required": true, "in": "query", "schema": {"example": "and", "type": "string"}}, {"name": "sortType", "required": false, "in": "query", "schema": {"$ref": "#/components/schemas/SortTypes"}}, {"name": "subCategories", "required": false, "in": "query", "schema": {"example": "2,4,6,7", "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetGiftCardListResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetGiftCardListResponse"}}}}}, "tags": ["SavedItems"], "security": [{"cookie": []}]}}, "/links/generate": {"post": {"operationId": "LinkController_generateLink", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateLinkDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinkResponseDto"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinkResponseDto"}}}}}, "tags": ["Links"], "security": [{"cookie": []}]}}, "/links": {"get": {"operationId": "LinkController_getUserLinks", "parameters": [{"name": "storeId", "required": false, "in": "query", "description": "Filter by store ID", "schema": {"type": "string"}}, {"name": "startDate", "required": false, "in": "query", "description": "Filter by start date", "schema": {"format": "date-time", "type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "Filter by end date", "schema": {"format": "date-time", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"default": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page", "schema": {"default": 10, "type": "number"}}], "responses": {"200": {"description": ""}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginationResponseDto"}}}}}, "tags": ["Links"], "security": [{"cookie": []}]}}, "/links/analytics": {"get": {"operationId": "LinkController_getUserAnalytics", "parameters": [{"name": "storeId", "required": false, "in": "query", "description": "Filter by store ID", "schema": {"example": "60a12d7c9f47e32b54e3c59a", "type": "string"}}, {"name": "startDate", "required": false, "in": "query", "description": "Start date for analysis period", "schema": {"format": "date-time", "example": "2023-01-01", "type": "string"}}, {"name": "endDate", "required": false, "in": "query", "description": "End date for analysis period", "schema": {"format": "date-time", "example": "2023-12-31", "type": "string"}}, {"name": "periodType", "required": false, "in": "query", "description": "Period type for comparison (day, week, month, year)", "schema": {"default": "month", "example": "month", "enum": ["day", "week", "month", "year"], "type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAnalyticsResponseDto"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAnalyticsResponseDto"}}}}}, "tags": ["Links"], "security": [{"cookie": []}]}}, "/links/{linkId}": {"get": {"operationId": "LinkController_getLinkDetails", "parameters": [{"name": "linkId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}, "default": {"description": "Redirect to the original URL"}}, "tags": ["Links"]}}, "/sitemap": {"get": {"operationId": "SitemapController_generateSitemap", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}}, "tags": ["Sitemap"]}}, "/bot/send-message": {"post": {"operationId": "BotController_sendMessage", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendMessageDto"}}}}, "responses": {"200": {"description": "Message sent successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendMessageResponseDto"}}}}, "201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendMessageResponseDto"}}}}, "400": {"description": "Invalid request parameters"}}, "tags": ["Bot"]}}, "/chat": {"post": {"operationId": "ChatController_chat", "summary": "Send a chat message and receive complete response", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatRequestDto"}}}}, "responses": {"200": {"description": "Chat response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatResponseDto"}}}}, "201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatResponseDto"}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Internal server error"}}, "tags": ["Cha<PERSON>"]}}, "/chat/embed": {"post": {"operationId": "ChatController_embedFile", "summary": "Upload and embed a TXT file for context", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmbedResponseDto"}}}}, "201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmbedResponseDto"}}}}, "400": {"description": "Invalid file or processing error"}}, "tags": ["Cha<PERSON>"]}}, "/": {"get": {"operationId": "AppController_get<PERSON><PERSON>", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}}, "tags": ["Health"]}}, "/stores/review/add-review": {"post": {"operationId": "ReviewController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReviewDto"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReviewResponse"}}}}, "401": {"description": "Unauthorized"}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReviewResponse"}}}}}, "tags": ["Review"], "security": [{"cookie": []}]}}, "/stores/review": {"get": {"operationId": "ReviewController_getAllGiftCards", "parameters": [{"name": "storeId", "required": true, "in": "query", "schema": {"example": "65eef6a14d2e4417e86b9ce5", "type": "string"}}, {"name": "sortType", "required": true, "in": "query", "schema": {"$ref": "#/components/schemas/ReviewTypes"}}], "responses": {"200": {"description": ""}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAllReviewsResponse"}}}}}, "tags": ["Review"]}}, "/card/wishlist/join": {"post": {"operationId": "WishListController_joinWishlist", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WishlistEmailDto"}}}}, "responses": {"201": {"description": ""}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WishlistResponseDto"}}}}}, "tags": ["WishList"]}}, "/card/auth/login": {"post": {"operationId": "CardAuthController_cardLogin", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CardLoginDto"}}}}, "responses": {"201": {"description": ""}, "default": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CardLoginResponseDto"}}}}}, "tags": ["<PERSON>"]}}}, "info": {"title": "ICB API", "description": "ICB API for frontend", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"cookie": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "cookie", "name": "accessToken"}}, "schemas": {"BannerResponse": {"type": "object", "properties": {"desktopBanners": {"type": "array", "items": {"type": "string"}}, "mobileBanners": {"type": "array", "items": {"type": "string"}}}, "required": ["desktopBanners", "mobileBanners"]}, "QuickAccessResponseItem": {"type": "object", "properties": {"imageUrl": {"type": "string"}, "redirectUrl": {"type": "string"}, "title": {"type": "string"}}, "required": ["imageUrl", "redirectUrl", "title"]}, "HeroResponseItem": {"type": "object", "properties": {"iconUrl": {"type": "string"}, "redirectUrl": {"type": "string"}, "title": {"type": "string"}}, "required": ["iconUrl", "redirectUrl", "title"]}, "MobileStoryResponse": {"type": "object", "properties": {"imageUrl": {"type": "string"}, "type": {"type": "string", "enum": ["image", "video"]}, "duration": {"type": "number"}, "timestamp": {"type": "number"}, "title": {"type": "string"}, "description": {"type": "string"}, "buttonText": {"type": "string"}, "redirectUrl": {"type": "string"}}, "required": ["imageUrl", "type", "duration", "timestamp", "title", "description", "buttonText", "redirectUrl"]}, "ResponseMobileStories": {"type": "object", "properties": {"storeName": {"type": "string"}, "storeLogo": {"type": "string"}, "storeBgColor": {"type": "string"}, "stories": {"type": "array", "items": {"$ref": "#/components/schemas/MobileStoryResponse"}}}, "required": ["storeName", "storeLogo", "storeBgColor", "stories"]}, "ContextOfferDealsType": {"type": "object", "properties": {"uid": {"type": "number"}, "productImage": {"type": "string"}, "storeLogoUrl": {"type": "string"}, "storeName": {"type": "string"}, "storeBgColor": {"type": "string"}, "endDate": {"type": "string"}, "offerTitle": {"type": "string"}, "offerCaption": {"type": "string"}, "salePrice": {"type": "number"}, "offerUrl": {"type": "string"}, "saved": {"type": "boolean"}, "isAutoGenerated": {"type": "boolean"}, "hideCbTag": {"type": "boolean"}}, "required": ["uid", "productImage", "storeLogoUrl", "storeName", "storeBgColor", "endDate", "offerTitle", "offerCaption", "salePrice", "offerUrl", "saved"]}, "ContextOfferCouponsType": {"type": "object", "properties": {"uid": {"type": "number"}, "productImage": {"type": "string"}, "storeLogoUrl": {"type": "string"}, "storeName": {"type": "string"}, "storeBgColor": {"type": "string"}, "endDate": {"type": "string"}, "offerTitle": {"type": "string"}, "offerCaption": {"type": "string"}, "salePrice": {"type": "number"}, "couponCode": {"type": "string"}, "offerUrl": {"type": "string"}, "saved": {"type": "boolean"}, "isAutoGenerated": {"type": "boolean"}, "hideCbTag": {"type": "boolean"}}, "required": ["uid", "productImage", "storeLogoUrl", "storeName", "storeBgColor", "endDate", "offerTitle", "offerCaption", "salePrice", "couponCode", "offerUrl", "saved"]}, "TrendingOfferResponseType": {"type": "object", "properties": {"deals": {"type": "array", "items": {"$ref": "#/components/schemas/ContextOfferDealsType"}}, "coupons": {"type": "array", "items": {"$ref": "#/components/schemas/ContextOfferCouponsType"}}}, "required": ["deals", "coupons"]}, "ContextOngoingOfferType": {"type": "object", "properties": {"uid": {"type": "number"}, "productImage": {"type": "string"}, "storeName": {"type": "string"}, "storeBgColor": {"type": "string"}, "storeLogoUrl": {"type": "string"}, "endDate": {"type": "string"}, "offerTitle": {"type": "string"}, "offerCaption": {"type": "string"}, "salePrice": {"type": "number"}, "saleLogoUrl": {"type": "string"}, "saleCaption": {"type": "string"}, "offerUrl": {"type": "string"}, "saved": {"type": "boolean"}, "isAutoGenerated": {"type": "boolean"}, "hideCbTag": {"type": "boolean"}}, "required": ["uid", "productImage", "storeName", "storeBgColor", "storeLogoUrl", "endDate", "offerTitle", "offerCaption", "salePrice", "saleLogoUrl", "saleCaption", "offerUrl", "saved"]}, "ContextMissedOfferType": {"type": "object", "properties": {"uid": {"type": "number"}, "productImage": {"type": "string"}, "storeName": {"type": "string"}, "storeBgColor": {"type": "string"}, "storeLogoUrl": {"type": "string"}, "offerTitle": {"type": "string"}, "currentAmount": {"type": "number"}}, "required": ["uid", "productImage", "storeName", "storeBgColor", "storeLogoUrl", "offerTitle", "currentAmount"]}, "CategorizedOffers": {"type": "object", "properties": {"trendingOffers": {"$ref": "#/components/schemas/TrendingOfferResponseType"}, "ongoingOffers": {"type": "array", "items": {"$ref": "#/components/schemas/ContextOngoingOfferType"}}, "expiredOffers": {"type": "array", "items": {"$ref": "#/components/schemas/ContextMissedOfferType"}}}, "required": ["trendingOffers", "ongoingOffers", "expiredOffers"]}, "GetAllOnGoingOffersResponse": {"type": "object", "properties": {"uid": {"type": "number"}, "name": {"type": "string"}}, "required": ["uid", "name"]}, "CategoryResponse": {"type": "object", "properties": {"id": {"type": "string"}, "uid": {"type": "number"}, "name": {"type": "string"}, "iconUrl": {"type": "string"}, "trendingPriority": {"type": "number"}}, "required": ["id", "uid", "name", "iconUrl"]}, "SubCategoriesByCategory": {"type": "object", "properties": {"uid": {"type": "number"}, "title": {"type": "string"}, "iconUrl": {"type": "string"}}, "required": ["uid", "title", "iconUrl"]}, "SubCategoryStore": {"type": "object", "properties": {"uid": {"type": "number"}, "storeName": {"type": "string"}, "imageUrl": {"type": "string"}, "caption": {"type": "string"}, "bgColor": {"type": "string"}, "saved": {"type": "boolean"}}, "required": ["uid", "storeName", "imageUrl", "caption", "bgColor", "saved"]}, "SubCategoryOffer": {"type": "object", "properties": {"uid": {"type": "number"}, "storeName": {"type": "string"}, "offerImage": {"type": "string"}, "offerTitle": {"type": "string"}, "offerUrl": {"type": "string"}}, "required": ["uid", "storeName", "offerImage", "offerTitle"]}, "SubCategoriesByCategoryResponse": {"type": "object", "properties": {"subCategories": {"type": "array", "items": {"$ref": "#/components/schemas/SubCategoriesByCategory"}}, "stores": {"type": "array", "items": {"$ref": "#/components/schemas/SubCategoryStore"}}, "latestOffer": {"$ref": "#/components/schemas/SubCategoryOffer"}}, "required": ["subCategories", "stores", "latestOffer"]}, "AllSubCategory": {"type": "object", "properties": {"uid": {"type": "number"}, "name": {"type": "string"}}, "required": ["uid", "name"]}, "AllCategoriesResponse": {"type": "object", "properties": {"uid": {"type": "number"}, "name": {"type": "string"}, "subCategories": {"type": "array", "items": {"$ref": "#/components/schemas/AllSubCategory"}}}, "required": ["uid", "name", "subCategories"]}, "ContextStore": {"type": "object", "properties": {"uid": {"type": "number"}, "bgColor": {"type": "string"}, "caption": {"type": "string"}, "offerCount": {"type": "number"}, "imageUrl": {"type": "string"}, "storeName": {"type": "string"}, "saved": {"type": "boolean"}}, "required": ["uid", "bgColor", "caption", "offerCount", "imageUrl", "storeName", "saved"]}, "StoresByCbContextResponse": {"type": "object", "properties": {"100pc": {"type": "array", "items": {"$ref": "#/components/schemas/ContextStore"}}, "50pc": {"type": "array", "items": {"$ref": "#/components/schemas/ContextStore"}}, "25pc": {"type": "array", "items": {"$ref": "#/components/schemas/ContextStore"}}}, "required": ["100pc", "50pc", "25pc"]}, "CategoryStoresResponse": {"type": "object", "properties": {"categoryId": {"type": "string"}, "categoryName": {"type": "string"}, "stores": {"type": "array", "items": {"$ref": "#/components/schemas/ContextStore"}}}, "required": ["categoryId", "categoryName", "stores"]}, "SearchResponseType": {"type": "object", "properties": {"uid": {"type": "number"}, "count": {"type": "number"}, "url": {"type": "string"}, "title": {"type": "string"}, "name": {"type": "string"}, "newUserOffer": {"type": "string"}, "oldUserOffer": {"type": "string"}, "caption": {"type": "string"}, "couponCode": {"type": "string"}}, "required": ["uid", "count", "url", "title", "name", "newUserOffer", "oldUser<PERSON>ffer", "caption", "couponCode"]}, "SearchStoreResponse": {"type": "object", "properties": {"storeList": {"type": "array", "items": {"$ref": "#/components/schemas/SearchResponseType"}}, "total": {"type": "number"}}, "required": ["storeList", "total"]}, "SearchDealResponse": {"type": "object", "properties": {"dealList": {"type": "array", "items": {"$ref": "#/components/schemas/SearchResponseType"}}, "total": {"type": "number"}}, "required": ["dealList", "total"]}, "SearchCouponResponse": {"type": "object", "properties": {"couponList": {"type": "array", "items": {"$ref": "#/components/schemas/SearchResponseType"}}, "total": {"type": "number"}}, "required": ["couponList", "total"]}, "SearchGiftCardResponse": {"type": "object", "properties": {"giftCardList": {"type": "array", "items": {"$ref": "#/components/schemas/SearchResponseType"}}, "total": {"type": "number"}}, "required": ["giftCardList", "total"]}, "SearchSuggestion": {"type": "object", "properties": {"suggestion": {"type": "string", "description": "Suggested corrected search term (supports multi-word phrases)"}, "confidence": {"type": "number", "description": "Confidence score between 0 and 1 (higher is better)"}}, "required": ["suggestion", "confidence"]}, "SearchResponseItem": {"type": "object", "properties": {"stores": {"$ref": "#/components/schemas/SearchStoreResponse"}, "deals": {"$ref": "#/components/schemas/SearchDealResponse"}, "coupons": {"$ref": "#/components/schemas/SearchCouponResponse"}, "giftCards": {"$ref": "#/components/schemas/SearchGiftCardResponse"}, "suggestions": {"description": "Spelling suggestions for misspelled search terms (supports multi-word corrections)", "type": "array", "items": {"$ref": "#/components/schemas/SearchSuggestion"}}}, "required": ["stores", "deals", "coupons", "giftCards"]}, "TermsAndPrivacyResponseType": {"type": "object", "properties": {"uid": {"type": "number"}, "content": {"type": "string"}, "type": {"type": "string"}}, "required": ["uid", "content", "type"]}, "TermsAndPrivacyResponseItem": {"type": "object", "properties": {"termsAndPrivacy": {"$ref": "#/components/schemas/TermsAndPrivacyResponseType"}}, "required": ["termsAndPrivacy"]}, "TestimonialResponseType": {"type": "object", "properties": {"reviewerName": {"type": "string"}, "reviewerAvatar": {"type": "string"}, "review": {"type": "string"}, "rating": {"type": "number"}}, "required": ["reviewerName", "reviewer<PERSON><PERSON><PERSON>", "review", "rating"]}, "CreateUserDto": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>", "description": "User Name"}, "email": {"type": "string", "example": "<EMAIL>", "description": "User Email"}, "mobile": {"type": "number", "example": *********0, "description": "Mobile Number"}, "referralCode": {"type": "string", "example": "Some referral code", "description": "Referral Code"}}, "required": ["name", "email"]}, "LoginResponse": {"type": "object", "properties": {"message": {"type": "string"}, "email": {"type": "string"}, "mobile": {"type": "string"}}, "required": ["message"]}, "UserResponse": {"type": "object", "properties": {"message": {"type": "string"}, "credential": {"type": "string"}}, "required": ["message"]}, "LoginDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "string", "example": "*********"}}}, "VerifyUserDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>", "description": "User Email"}, "mobile": {"type": "string", "example": "**********", "description": "User's mobile"}, "otp": {"type": "number", "example": 789876, "description": "User verification otp"}}, "required": ["otp"]}, "GetBankAccountDataResponse": {"type": "object", "properties": {"holderName": {"type": "string"}, "bankName": {"type": "string"}, "branchName": {"type": "string"}, "postcode": {"type": "string"}, "accountNumber": {"type": "string"}, "ifsc": {"type": "string"}, "upi": {"type": "string"}, "address": {"type": "string"}}, "required": ["<PERSON><PERSON><PERSON>", "bankName", "branchName", "postcode", "accountNumber", "ifsc", "upi", "address"]}, "UpdateBankAccountDto": {"type": "object", "properties": {"holderName": {"type": "string", "example": "<PERSON>", "description": "Full name as per the bank account holder"}, "bankName": {"type": "string", "example": "HDFC", "description": "Bank Name"}, "ifsc": {"type": "string", "example": "Hdfc@0koramangala", "description": "Bank IFSC code"}, "branchName": {"type": "number", "example": "HDFC Bank, Koramangala", "description": "Branch Name"}, "accountNumber": {"type": "string", "example": "************** ", "description": "Account Number"}, "postcode": {"type": "number", "example": "560068", "description": "Postcode/Zipcode of the bank account holder"}, "address": {"type": "string", "example": "2393 15B Cross Road\n\t Building Sector 1\n\t Bengaluru\n\t Bangalore South\n\t Karnataka\n\t India ", "description": "Address of the bank account holder"}}, "required": ["<PERSON><PERSON><PERSON>", "bankName", "ifsc", "branchName", "accountNumber", "postcode", "address"]}, "UpdateUpiDto": {"type": "object", "properties": {"upi": {"type": "string", "example": "Doe1232@okhdfcJohn", "description": "Upi id"}}, "required": ["upi"]}, "PersonalInterestTypes": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "GetUserProfileResponseItem": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "avatar": {"type": "string"}, "mobile": {"type": "number"}, "balance": {"type": "number"}, "pendingCount": {"type": "number"}, "confirmedCount": {"type": "number"}, "cancelledCount": {"type": "number"}, "referralCode": {"type": "string"}, "personalInterest": {"type": "array", "items": {"$ref": "#/components/schemas/PersonalInterestTypes"}}, "sendNotification": {"type": "boolean"}}, "required": ["name", "email", "balance", "pendingCount", "confirmedCount", "cancelledCount", "referralCode", "personalInterest", "sendNotification"]}, "UserOverviewResponse": {"type": "object", "properties": {"totalCashback": {"type": "array", "items": {"type": "number"}}, "totalClicks": {"type": "array", "items": {"type": "number"}}, "totalOrderAmount": {"type": "array", "items": {"type": "number"}}, "readyToWithdraw": {"type": "number"}, "totalPendingCount": {"type": "number"}, "totalApprovedCount": {"type": "number"}, "totalCancelledCount": {"type": "number"}, "flipkartRewardPoints": {"type": "number"}, "totalCashbackEarned": {"type": "number"}, "totalReferralCommission": {"type": "number"}, "shareAndEarnCashback": {"type": "number"}, "shareAndEarnRewards": {"type": "number"}}, "required": ["totalCashback", "totalClicks", "totalOrderAmount", "readyToWithdraw", "totalPendingCount", "totalApprovedCount", "totalCancelledCount", "flipkartRewardPoints", "totalCashbackEarned", "totalReferralCommission", "shareAndEarnCashback", "shareAndEarnRewards"]}, "ReferralTypes": {"type": "string", "enum": ["newest", "oldest", "highToLow", "lowToHigh", "noOfOrders"]}, "UserData": {"type": "object", "properties": {"totalOrders": {"type": "number"}, "uid": {"type": "number"}, "name": {"type": "string"}, "joined": {"type": "string"}, "avatar": {"type": "string"}, "status": {"type": "string", "enum": ["active", "inactive", "blocked"]}, "totalPendingReferralCommission": {"type": "number"}, "totalConfirmedReferralCommission": {"type": "number"}}, "required": ["totalOrders", "uid", "name", "joined", "avatar", "status", "totalPendingReferralCommission", "totalConfirmedReferralCommission"]}, "PaginationResponseType": {"type": "object", "properties": {"page": {"type": "number", "example": 1}, "pageSize": {"type": "number"}, "total": {"type": "number"}}, "required": ["page", "pageSize", "total"]}, "GetUsersByReferralCodeResponse": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/UserData"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["users", "pagination"]}, "CashbackSortTypes": {"type": "string", "enum": ["newest", "oldest", "cashbackAmount"]}, "CbItem": {"type": "object", "properties": {"orderAmount": {"type": "number"}, "cashbackAmount": {"type": "number"}, "storeLogo": {"type": "string"}, "storeBgColor": {"type": "string"}, "referenceId": {"type": "string"}, "orderDate": {"type": "string"}, "approxConfirmDate": {"type": "string"}, "remarks": {"type": "string"}, "earningsType": {"type": "string", "enum": ["click", "missing", "referral"]}, "isShareAndEarn": {"type": "boolean"}, "status": {"type": "string", "enum": ["confirmed", "pending", "cancelled"]}}, "required": ["orderAmount", "cashbackAmount", "storeLogo", "referenceId", "orderDate", "approxConfirmDate", "remarks", "earningsType", "isShareAndEarn", "status"]}, "GetCbHistoryResponse": {"type": "object", "properties": {"cbItems": {"type": "array", "items": {"$ref": "#/components/schemas/CbItem"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["cbItems", "pagination"]}, "ObjectId": {"type": "object", "properties": {}}, "UpdateProfileDto": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>", "description": "Name of the user"}, "personalInterest": {"example": "6f9d88f7d6f3e3b3f8f3f8f3", "description": "Select multiple categories you are interested from this list", "allOf": [{"$ref": "#/components/schemas/ObjectId"}]}, "sendNotification": {"type": "boolean", "example": true, "description": "select true or false"}}, "required": ["name"]}, "UpdateProfileWithImageDto": {"type": "object", "properties": {"profileImage": {"type": "string", "format": "binary"}, "profileData": {"$ref": "#/components/schemas/UpdateProfileDto"}}}, "UpdateCredentialsDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "number", "example": *********0, "description": "Mobile Number"}, "type": {"type": "string", "example": "email", "enum": ["mobile", "email"], "description": "Please add the type mobile or email"}}, "required": ["type"]}, "OtpDto": {"type": "object", "properties": {"otp": {"type": "number", "example": 123413, "description": "user otp"}}, "required": ["otp"]}, "UserListItemDto": {"type": "object", "properties": {"uid": {"type": "number"}, "name": {"type": "string"}, "email": {"type": "string"}, "mobile": {"type": "number"}, "referralCode": {"type": "string"}, "userNotes": {"type": "string"}, "mobileVerified": {"type": "boolean"}, "status": {"type": "string"}, "migrated": {"type": "boolean"}, "lastLoggedDate": {"format": "date-time", "type": "string"}, "referredByEmail": {"type": "string"}}, "required": ["uid", "name", "email", "referralCode", "mobileVerified", "status", "migrated"]}, "UserListResponseDto": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/UserListItemDto"}}}, "required": ["users"]}, "GetReferralLeaderboardResponse": {"type": "object", "properties": {"name": {"type": "string"}, "referralCount": {"type": "number"}, "totalReferralCommission": {"type": "number"}}, "required": ["name", "referralCount", "totalReferralCommission"]}, "SortTypes": {"type": "string", "enum": ["newest", "discount", "highestCbPercent", "highestCbAmount", "alphabetical", "popular"]}, "StoreCard": {"type": "object", "properties": {"uid": {"type": "number"}, "bgColor": {"type": "string"}, "caption": {"type": "string"}, "imageUrl": {"type": "string"}, "storeName": {"type": "string"}, "saved": {"type": "boolean"}}, "required": ["uid", "bgColor", "caption", "imageUrl", "storeName", "saved"]}, "GetAllStoresResponse": {"type": "object", "properties": {"stores": {"type": "array", "items": {"$ref": "#/components/schemas/StoreCard"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["stores", "pagination"]}, "StoreGiftCard": {"type": "object", "properties": {"uid": {"type": "number"}, "name": {"type": "string"}}, "required": ["uid", "name"]}, "GetStoreDetails": {"type": "object", "properties": {"id": {"type": "string"}, "uid": {"type": "number"}, "name": {"type": "string"}, "logo": {"type": "string"}, "description": {"type": "string"}, "offerWarning": {"type": "string"}, "missingAccepted": {"type": "boolean"}, "trackingTime": {"type": "string"}, "confirmationTime": {"type": "string"}, "minimumAmount": {"type": "number"}, "cashbackAmount": {"type": "number"}, "cashbackPercent": {"type": "number"}, "cashbackType": {"type": "string", "enum": ["cashback", "reward"]}, "offerType": {"type": "string", "enum": ["flat", "upto"]}, "importantPoints": {"type": "string"}, "ratingAverage": {"type": "number"}, "ratingsCount": {"type": "number"}, "giftCard": {"$ref": "#/components/schemas/StoreGiftCard"}, "active": {"type": "boolean"}, "storePopUpWarning": {"type": "string"}, "storeWarning": {"type": "string"}, "isAppSaleTrackable": {"type": "boolean"}}, "required": ["id", "uid", "name", "logo", "description", "offerWarning", "missingAccepted", "trackingTime", "confirmationTime", "minimumAmount", "cashbackAmount", "cashbackP<PERSON><PERSON>", "cashbackType", "offerType", "importantPoints", "ratingAverage", "ratingsCount", "giftCard", "active", "storePopUpWarning", "storeWarning", "isAppSaleTrackable"]}, "GetStoreDetailsResponse": {"type": "object", "properties": {"store": {"$ref": "#/components/schemas/GetStoreDetails"}, "similarStores": {"type": "array", "items": {"$ref": "#/components/schemas/ContextStore"}}}, "required": ["store", "similarStores"]}, "CashbackRateType": {"type": "object", "properties": {"uid": {"type": "number"}, "name": {"type": "string"}, "description": {"type": "string"}, "oldUserRate": {"type": "number"}, "newUserRate": {"type": "number"}, "type": {"type": "string", "enum": ["percent", "amount"]}}, "required": ["uid", "name", "description", "oldUserRate", "newUserRate", "type"]}, "GetCashbackRatesByStoreResponse": {"type": "object", "properties": {"cashbackRates": {"type": "array", "items": {"$ref": "#/components/schemas/CashbackRateType"}}}, "required": ["cashbackRates"]}, "SaveOfferDto": {"type": "object", "properties": {"itemUid": {"type": "number", "example": 23}}, "required": ["itemUid"]}, "SaveItemResponse": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}, "RemoveOfferDto": {"type": "object", "properties": {"itemUid": {"type": "number", "example": 23}}, "required": ["itemUid"]}, "GetGiftCardType": {"type": "object", "properties": {"uid": {"type": "number"}, "id": {"type": "string"}, "name": {"type": "string"}, "imageUrl": {"type": "string"}, "caption": {"type": "string"}, "saved": {"type": "boolean"}}, "required": ["uid", "id", "name", "imageUrl", "caption", "saved"]}, "GetGiftCardListResponse": {"type": "object", "properties": {"giftCards": {"type": "array", "items": {"$ref": "#/components/schemas/GetGiftCardType"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["giftCards", "pagination"]}, "GiftCardBannerType": {"type": "object", "properties": {"imageUrl": {"type": "string"}, "redirectUrl": {"type": "string"}, "termsContent": {"type": "string"}, "termsTitle": {"type": "string"}}, "required": ["imageUrl", "redirectUrl", "termsContent", "termsTitle"]}, "GiftCardBannersResponse": {"type": "object", "properties": {"desktopBanners": {"type": "array", "items": {"$ref": "#/components/schemas/GiftCardBannerType"}}, "mobileBanners": {"type": "array", "items": {"$ref": "#/components/schemas/GiftCardBannerType"}}}, "required": ["desktopBanners", "mobileBanners"]}, "GiftCardDetails": {"type": "object", "properties": {"id": {"type": "string"}, "uid": {"type": "number"}, "name": {"type": "string"}, "storeName": {"type": "string"}, "storeUid": {"type": "number"}, "storeLogo": {"type": "string"}, "storeBgColor": {"type": "string"}, "imageUrl": {"type": "string"}, "description": {"type": "string"}, "discountGetting": {"type": "number"}, "showCustomAmount": {"type": "boolean"}, "cashbackGiving": {"type": "number"}, "denominations": {"type": "array", "items": {"type": "number"}}, "terms": {"type": "string"}, "howToUse": {"type": "array", "items": {"type": "string"}}}, "required": ["id", "uid", "name", "storeName", "storeUid", "storeLogo", "storeBgColor", "imageUrl", "description", "discountGetting", "showCustomAmount", "cashbackGiving", "denominations", "terms", "howToUse"]}, "GetGiftCardResponse": {"type": "object", "properties": {"giftCard": {"$ref": "#/components/schemas/GiftCardDetails"}, "similarGiftCards": {"type": "array", "items": {"$ref": "#/components/schemas/GetGiftCardType"}}}, "required": ["giftCard", "similarGiftCards"]}, "Card": {"type": "object", "properties": {"amount": {"type": "number", "example": 1000}, "quantity": {"type": "number", "example": 1}}, "required": ["amount", "quantity"]}, "OrderGiftCardDto": {"type": "object", "properties": {"paymentMethods": {"type": "array", "description": "The types of payment", "example": ["balance", "razorpay"], "items": {"type": "string", "enum": ["balance", "razorpay"]}}, "giftcardId": {"type": "string", "example": "60f5f7e8d3c9e8d5e4e3f3b1"}, "name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "mobile": {"type": "number", "example": 9876543210}, "msg": {"type": "string"}, "cards": {"example": [{"amount": 1000, "quantity": 1}, {"amount": 100, "quantity": 2}, {"amount": 4000, "quantity": 3}], "type": "array", "items": {"$ref": "#/components/schemas/Card"}}}, "required": ["paymentMethods", "giftcardId", "name", "email", "cards"]}, "OrderResponse": {"type": "object", "properties": {"orderId": {"type": "string"}, "amountToPay": {"type": "number"}, "paymentMethod": {"type": "string", "enum": ["balance", "razorpay"]}, "currency": {"type": "string"}, "fullPaymentDone": {"type": "boolean"}}, "required": ["orderId", "amountToPay", "paymentMethod", "currency", "fullPaymentDone"]}, "PaymentVerifyDto": {"type": "object", "properties": {"paymentId": {"type": "string", "description": "RazorPay payment ID"}, "orderId": {"type": "string", "description": "RazorPay order ID"}, "signature": {"type": "string", "description": "RazorPay payment signature"}}, "required": ["paymentId", "orderId", "signature"]}, "RedeemIcbGiftCardDto": {"type": "object", "properties": {"giftCardNumber": {"type": "number"}, "giftCardPin": {"type": "number"}}, "required": ["giftCardNumber", "giftCardPin"]}, "RedeemHistoryTypes": {"type": "string", "enum": ["date", "amount"]}, "RedeemGiftCard": {"type": "object", "properties": {"giftCardId": {"type": "string"}, "uid": {"type": "number"}, "amount": {"type": "string"}, "date": {"type": "string"}, "time": {"type": "string"}, "paid": {"type": "boolean"}}, "required": ["giftCardId", "uid", "amount", "date", "time", "paid"]}, "GetRedeemGiftCardListResponse": {"type": "object", "properties": {"redeemGiftCards": {"type": "array", "items": {"$ref": "#/components/schemas/RedeemGiftCard"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["redeemGiftCards", "pagination"]}, "IcbCardTypeResponse": {"type": "object", "properties": {"uid": {"type": "string"}, "name": {"type": "string"}}, "required": ["uid", "name"]}, "UserTypes": {"type": "string", "enum": ["new", "existing", "both"]}, "OfferTypes": {"type": "string", "enum": ["coupons", "deals", "trending", "both"]}, "OfferCouponsType": {"type": "object", "properties": {"uid": {"type": "number"}, "productImage": {"type": "string"}, "storeLogoUrl": {"type": "string"}, "storeName": {"type": "string"}, "endDate": {"type": "string"}, "offerTitle": {"type": "string"}, "offerCaption": {"type": "string"}, "salePrice": {"type": "number"}, "couponCode": {"type": "string"}, "repeatBy": {"type": "string"}, "offerUrl": {"type": "string"}, "saved": {"type": "boolean"}, "hideCbTag": {"type": "boolean"}, "isAutoGenerated": {"type": "boolean"}, "cashbackType": {"type": "string"}, "offerType": {"type": "string"}, "offerAmount": {"type": "number"}, "offerPercent": {"type": "number"}}, "required": ["uid", "productImage", "storeLogoUrl", "storeName", "endDate", "offerTitle", "salePrice", "couponCode", "repeatBy"]}, "AppliedFiltersType": {"type": "object", "properties": {"subCategoryIds": {"description": "All subcategory IDs applied (explicit + keyword-based)", "type": "array", "items": {"type": "number"}}, "keywordBasedSubcategoryIds": {"description": "Subcategory IDs detected from search keywords", "type": "array", "items": {"type": "number"}}}, "required": ["subCategoryIds", "keywordBasedSubcategoryIds"]}, "DealAndCouponsResponse": {"type": "object", "properties": {"offers": {"type": "array", "items": {"$ref": "#/components/schemas/OfferCouponsType"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}, "appliedFilters": {"$ref": "#/components/schemas/AppliedFiltersType"}}, "required": ["offers", "pagination"]}, "OngoingOfferType": {"type": "object", "properties": {"uid": {"type": "number"}, "productImage": {"type": "string"}, "storeName": {"type": "string"}, "storeLogoUrl": {"type": "string"}, "endDate": {"type": "string"}, "offerTitle": {"type": "string"}, "offerCaption": {"type": "string"}, "salePrice": {"type": "number", "nullable": true}, "saleLogoUrl": {"type": "string"}, "saleCaption": {"type": "string"}, "offerUrl": {"type": "string"}, "saved": {"type": "boolean"}, "repeatBy": {"type": "string"}, "hideCbTag": {"type": "boolean"}}, "required": ["uid", "productImage", "storeName", "storeLogoUrl", "endDate", "offerTitle"]}, "OngoingOffersResponse": {"type": "object", "properties": {"offers": {"type": "array", "items": {"$ref": "#/components/schemas/OngoingOfferType"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["offers", "pagination"]}, "GetOfferType": {"type": "object", "properties": {"uid": {"type": "number"}, "productImage": {"type": "string"}, "storeName": {"type": "string"}, "storeLogoUrl": {"type": "string"}, "endDate": {"type": "string"}, "offerTitle": {"type": "string"}, "offerCaption": {"type": "string"}, "salePrice": {"type": "number", "nullable": true}, "saleLogoUrl": {"type": "string"}, "saleCaption": {"type": "string"}, "offerUrl": {"type": "string"}, "saved": {"type": "boolean"}, "repeatBy": {"type": "string"}, "hideCbTag": {"type": "boolean"}, "storeBgColor": {"type": "string"}, "newUserRate": {"type": "number"}, "oldUserRate": {"type": "number"}, "itemPrice": {"type": "number"}, "offerPercent": {"type": "number"}, "offerAmount": {"type": "number"}, "minimumAmount": {"type": "number"}, "confirmationTime": {"type": "string"}, "missingAccepted": {"type": "boolean"}, "trackingTime": {"type": "string"}, "termsAndConditions": {"type": "string"}, "offerDescription": {"type": "string"}, "offerWarning": {"type": "string"}, "cashbackType": {"type": "string", "enum": ["cashback", "reward"]}, "storeId": {"$ref": "#/components/schemas/ObjectId"}, "keySpecs": {"type": "string"}, "importantUpdate": {"type": "string"}, "offerType": {"type": "string", "enum": ["upto", "flat"]}, "couponCode": {"type": "string"}, "isExpired": {"type": "boolean"}, "isAutoGenerated": {"type": "boolean"}}, "required": ["uid", "productImage", "storeName", "storeLogoUrl", "endDate", "offerTitle", "storeBgColor", "offerAmount", "minimumAmount", "confirmationTime", "trackingTime", "termsAndConditions", "offerDescription", "cashbackType", "storeId", "offerType"]}, "GetOfferByIdResponse": {"type": "object", "properties": {"offer": {"$ref": "#/components/schemas/GetOfferType"}, "similarOffers": {"type": "array", "items": {"$ref": "#/components/schemas/OfferCouponsType"}}}, "required": ["offer", "similarOffers"]}, "GetSimilarOffers": {"type": "object", "properties": {"similarOffers": {"type": "array", "items": {"$ref": "#/components/schemas/OfferCouponsType"}}}, "required": ["similarOffers"]}, "CreateClickDto": {"type": "object", "properties": {"type": {"type": "string", "enum": ["offer", "express", "rates"]}, "uid": {"type": "number"}}, "required": ["type", "uid"]}, "ClickCreateResponse": {"type": "object", "properties": {"referenceId": {"type": "string"}, "url": {"type": "string"}, "logo": {"type": "string"}, "offer": {"type": "string"}, "isActive": {"type": "string"}}, "required": ["referenceId", "url", "logo"]}, "ClickType": {"type": "object", "properties": {"id": {"type": "string"}, "uid": {"type": "number"}, "referenceId": {"type": "string"}, "status": {"type": "string", "enum": ["Pending", "Confirmed", "Cancelled", "Tracked", "Report Missing CB"]}, "type": {"type": "string", "enum": ["offer", "express", "rates"]}, "canReport": {"type": "boolean"}, "fromPreviousMonth": {"type": "boolean"}, "name": {"type": "string"}, "date": {"type": "string"}, "time": {"type": "string"}, "logo": {"type": "string"}, "createdAt": {"type": "string"}}, "required": ["id", "uid", "referenceId", "status", "type", "canReport", "fromPreviousMonth", "name", "date", "time", "logo", "createdAt"]}, "ClicksResponse": {"type": "object", "properties": {"clicks": {"type": "array", "items": {"$ref": "#/components/schemas/ClickType"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["clicks", "pagination"]}, "ClickByStoreType": {"type": "object", "properties": {"id": {"type": "string"}, "uid": {"type": "number"}, "referenceId": {"type": "string"}, "status": {"type": "string", "enum": ["Pending", "Confirmed", "Cancelled", "Tracked", "Report Missing CB"]}, "type": {"type": "string", "enum": ["offer", "express", "rates"]}, "canReport": {"type": "boolean"}, "fromPreviousMonth": {"type": "boolean"}, "name": {"type": "string"}, "date": {"type": "string"}, "time": {"type": "string"}}, "required": ["id", "uid", "referenceId", "status", "type", "canReport", "fromPreviousMonth", "name", "date", "time"]}, "StoreType": {"type": "object", "properties": {"uid": {"type": "number"}, "name": {"type": "string"}, "logo": {"type": "string"}, "clicks": {"type": "array", "items": {"$ref": "#/components/schemas/ClickByStoreType"}}, "count": {"type": "number"}}, "required": ["uid", "name", "logo", "clicks", "count"]}, "ClicksByStoreResponse": {"type": "object", "properties": {"stores": {"type": "array", "items": {"$ref": "#/components/schemas/StoreType"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["stores", "pagination"]}, "ReportMissingCashbackType": {"type": "object", "properties": {"click": {"example": "65eef72b4d2e4417e86ba45f", "allOf": [{"$ref": "#/components/schemas/ObjectId"}]}, "userType": {"type": "string", "enum": ["new", "old"]}, "coupon": {"type": "boolean"}, "orderId": {"type": "string"}, "message": {"type": "string"}, "paidAmount": {"type": "number"}, "platform": {"type": "string", "enum": ["web", "mobile"]}}, "required": ["click", "userType", "coupon", "orderId", "message", "paidAmount", "platform"]}, "ReportMissingCashbackDto": {"type": "object", "properties": {"invoice": {"type": "string", "format": "binary", "description": "Invoice support only PNG, JPG, JPEG, WebP and PDF. File format with max size of 2MB"}, "reportData": {"$ref": "#/components/schemas/ReportMissingCashbackType"}}, "required": ["invoice", "reportData"]}, "MissingCashbackType": {"type": "object", "properties": {"uid": {"type": "number"}, "storeLogo": {"type": "string"}, "storeBgColor": {"type": "string"}, "clickedTime": {"format": "date-time", "type": "string"}, "amount": {"type": "number"}, "title": {"type": "string"}, "referenceId": {"type": "string"}, "status": {"type": "string"}, "createdAt": {"format": "date-time", "type": "string"}}, "required": ["uid", "storeLogo", "storeBgColor", "clickedTime", "amount", "title", "referenceId", "status", "createdAt"]}, "MissingCashbackResponse": {"type": "object", "properties": {"missingCashbacks": {"type": "array", "items": {"$ref": "#/components/schemas/MissingCashbackType"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["missingCashbacks", "pagination"]}, "PaymentTypes": {"type": "string", "description": "The type of payment", "enum": ["bank", "upi"]}, "PaymentRequestDto": {"type": "object", "properties": {"withdrawAmount": {"type": "number", "minimum": 100, "description": "The amount to withdraw"}, "paymentType": {"example": "bank", "$ref": "#/components/schemas/PaymentTypes"}}, "required": ["withdrawAmount", "paymentType"]}, "PaymentSortTypes": {"type": "string", "enum": ["newest", "oldest", "amount"]}, "PaymentType": {"type": "object", "properties": {"referenceId": {"type": "string"}, "withdrawAmount": {"type": "number"}, "paymentType": {"type": "string", "enum": ["Bank", "UPI", "Gift Voucher", "Recharge", "Wallet"]}, "status": {"type": "string", "enum": ["Paid", "Requested", "Cancelled"]}, "paymentDate": {"type": "number"}}, "required": ["referenceId", "withdrawAmount", "paymentType", "status", "paymentDate"]}, "GetPaymentListResponse": {"type": "object", "properties": {"payments": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentType"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["payments", "pagination"]}, "SavedDealsResponse": {"type": "object", "properties": {"deals": {"type": "array", "items": {"$ref": "#/components/schemas/ContextOfferDealsType"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["deals", "pagination"]}, "SavedCouponsResponse": {"type": "object", "properties": {"coupons": {"type": "array", "items": {"$ref": "#/components/schemas/ContextOfferCouponsType"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["coupons", "pagination"]}, "GenerateLinkDto": {"type": "object", "properties": {"url": {"type": "string", "description": "The original URL to generate a link for", "example": "https://www.flipkart.com/product/123456"}}, "required": ["url"]}, "LinkResponseDto": {"type": "object", "properties": {"linkId": {"type": "string", "description": "The unique ID of the generated link", "example": "ICBLABCD1234"}, "originalUrl": {"type": "string", "description": "The original URL", "example": "https://www.flipkart.com/product/123456"}, "generatedUrl": {"type": "string", "description": "The generated affiliate URL", "example": "https://dl.flipkart.com/dl/product/123456?affid=connectin"}, "storeName": {"type": "string", "description": "The store name", "example": "<PERSON><PERSON><PERSON><PERSON>"}, "createdAt": {"type": "string", "description": "The creation date", "example": "2023-07-01T12:00:00.000Z"}, "shortUrl": {"type": "string", "description": "The short URL", "example": "https://icashbk.in/ICBLABCD1234"}}, "required": ["linkId", "originalUrl", "generatedUrl", "storeName", "createdAt", "shortUrl"]}, "PaginationResponseDto": {"type": "object", "properties": {"items": {"description": "List of items", "type": "array", "items": {"type": "string"}}, "total": {"type": "number", "description": "Total number of items"}, "page": {"type": "number", "description": "Current page"}, "pages": {"type": "number", "description": "Number of pages"}, "limit": {"type": "number", "description": "Items per page"}}, "required": ["items", "total", "page", "pages", "limit"]}, "MetricWithChangeDto": {"type": "object", "properties": {"value": {"type": "number", "description": "Current value"}, "percentageChange": {"type": "number", "description": "Percentage change from previous period"}}, "required": ["value", "percentageChange"]}, "UserAnalyticsResponseDto": {"type": "object", "properties": {"totalCashbackEarned": {"description": "Total cashback earned in the current period", "allOf": [{"$ref": "#/components/schemas/MetricWithChangeDto"}]}, "conversionRate": {"description": "Conversion rate in the current period", "allOf": [{"$ref": "#/components/schemas/MetricWithChangeDto"}]}, "totalClicks": {"description": "Total clicks in the current period", "allOf": [{"$ref": "#/components/schemas/MetricWithChangeDto"}]}}, "required": ["totalCashbackEarned", "conversionRate", "totalClicks"]}, "SendMessageDto": {"type": "object", "properties": {"threadId": {"type": "string", "example": "1262", "description": "The unique identifier for the conversation thread"}, "msg": {"type": "string", "example": "Hello, this is a test message from the API", "description": "The message content to send to the bot"}}, "required": ["threadId", "msg"]}, "SendMessageResponseDto": {"type": "object", "properties": {"message": {"type": "string", "example": "Message sent successfully", "description": "Response message indicating success"}, "threadId": {"type": "string", "example": "1262", "description": "The thread ID where the message was sent"}}, "required": ["message", "threadId"]}, "ChatRequestDto": {"type": "object", "properties": {"message": {"type": "string"}, "sessionId": {"type": "string"}}, "required": ["message"]}, "ChatResponseDto": {"type": "object", "properties": {"sessionId": {"type": "string"}, "message": {"type": "string"}}, "required": ["sessionId", "message"]}, "EmbedResponseDto": {"type": "object", "properties": {"message": {"type": "string"}, "source": {"type": "string"}, "chunksProcessed": {"type": "number"}, "documentsStored": {"type": "number"}, "processingTime": {"type": "number"}, "metadata": {"type": "object", "properties": {"originalLength": {"required": true, "type": "number"}, "chunkSize": {"required": true, "type": "number"}, "chunkOverlap": {"required": true, "type": "number"}, "processedAt": {"format": "date-time", "required": true, "type": "string"}}}}, "required": ["message", "source", "chunksProcessed", "documentsStored", "processingTime", "metadata"]}, "CreateReviewDto": {"type": "object", "properties": {"storeUid": {"type": "number", "example": 32}, "review": {"type": "string", "example": "This is a review"}, "rating": {"type": "number", "example": 3, "enum": [1, 2, 3, 4, 5]}}, "required": ["storeUid", "review", "rating"]}, "CreateReviewResponse": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}, "ReviewTypes": {"type": "string", "enum": ["newest", "rating"]}, "GetAllReviewsType": {"type": "object", "properties": {"uid": {"type": "number"}, "name": {"type": "string"}, "avatar": {"type": "string"}, "review": {"type": "string"}, "rating": {"type": "number"}, "createdDate": {"type": "string"}}, "required": ["uid", "name", "avatar", "review", "rating", "createdDate"]}, "GetAllReviewsResponse": {"type": "object", "properties": {"reviews": {"type": "array", "items": {"$ref": "#/components/schemas/GetAllReviewsType"}}, "pagination": {"$ref": "#/components/schemas/PaginationResponseType"}}, "required": ["reviews", "pagination"]}, "WishlistEmailDto": {"type": "object", "properties": {"email": {"type": "string", "example": "<EMAIL>", "description": "User Email for wishlist"}}}, "WishlistResponseDto": {"type": "object", "properties": {"message": {"type": "string", "example": "Added to wishlist", "description": "Response message"}}, "required": ["message"]}, "CardLoginDto": {"type": "object", "properties": {"mobile": {"type": "string", "example": "*********0", "description": "User number for login"}}}, "CardLoginResponseDto": {"type": "object", "properties": {"message": {"type": "string", "example": "Logged in successfully", "description": "Response message"}}, "required": ["message"]}}}}
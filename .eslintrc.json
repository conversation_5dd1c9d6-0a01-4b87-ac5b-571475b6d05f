{
  "env": {
    "browser": true,
    "es2021": true
  },
  "extends": [
    "plugin:react/recommended",
    "prettier",
    "next/core-web-vitals",
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module"
    // "project": "./tsconfig.json"
  },
  "plugins": [
    "react",
    "eslint-plugin-validate-jsx-nesting",
    "@typescript-eslint"
  ],
  "rules": {
    "validate-jsx-nesting/no-invalid-jsx-nesting": "error",
    // "quotes": [2, "single"],
    "eqeqeq": "warn",
    "comma-dangle": "off",
    "curly": "warn",
    "no-useless-escape": "off",
    "react/jsx-pascal-case": "warn",
    "react/jsx-key": "warn",
    "react/jsx-sort-props": "warn",
    "react/react-in-jsx-scope": "off",
    "react/no-children-prop": "error",
    "react/jsx-fragments": "error",
    "react/void-dom-elements-no-children": "error",
    "react/self-closing-comp": "warn",
    "react/no-unescaped-entities": "off",
    "react/no-unstable-nested-components": "warn",
    "dot-notation": "warn",
    "react/display-name": "off",
    "semi": "off",
    "@typescript-eslint/semi": "off",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "@typescript-eslint/ban-ts-comment": "warn"
  }
}

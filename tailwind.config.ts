import type { Config } from 'tailwindcss';

const config: Config = {
  darkMode: 'class',
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      screens: {
        sm: '390px',
      },
      fontFamily: {
        nexa: ['var(--font-nexa)'],
        pat: ['var(--font-patuaOne)'],
        pop: ['var(--font-poppins)'],
      },
      colors: {
        primary: 'var(--primary)',
        primaryDark: 'var(--primaryDark)',
        body: 'var(--body)',
        container: 'var(--container)',
        content: 'var(--content)',
        trendOffer: 'var(--trendOffer)',
        heading: 'var(--heading)',
        mainCard: 'var(--mainCard)',
        blackWhite: 'var(--blackWhite)',
        blackWhite2: 'var(--blackWhite2)',
      },
      animation: {
        fadeIn: 'fadeIn 0.3s ease-in-out',
        scroll: 'scroll 20s linear infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        scroll: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-66.666%)' },
        },
      },
      animationPlayState: {
        'paused': 'paused',
      },
    },
  },
  plugins: [],
};
export default config;

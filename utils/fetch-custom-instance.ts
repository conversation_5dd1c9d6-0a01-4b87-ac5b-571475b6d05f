export const customInstance = async <T>({
  url,
  method,
  params,
  data,
  headers,
  signal,
}: {
  url: string;
  method:
    | 'DELETE'
    | 'delete'
    | 'GET'
    | 'get'
    | 'PATCH'
    | 'patch'
    | 'POST'
    | 'post'
    | 'PUT'
    | 'put';
  params?: any;
  data?: any;
  responseType?: string;
  headers?: any;
  signal?: any;
}): Promise<T> => {
  let url_ = `${process.env.Base_URL}${url}`;
  if (params) {
    url_ = `${url_}?${new URLSearchParams(params)}`;
  }
  const _req_headers = await getHeaders();

  let body_data = data;
  if (headers) {
    for (const key in headers) {
      if (key === 'Content-Type') {
        const _content_type = headers[key];
        if (_content_type === 'multipart/form-data') {
          _req_headers.delete('Content-Type');
        }
        continue;
      }
      _req_headers.set(key, headers[key]);
    }
  }

  if (data && data instanceof FormData) {
    _req_headers.delete('Content-Type');
    body_data = data;
  } else if (data) {
    body_data = JSON.stringify(data);
  }
  if (method === 'patch') {
    method = 'PATCH';
  }
  const response = await fetch(url_, {
    method,
    body: body_data,
    headers: _req_headers,
    signal,
  });
  // if (response.status === 401) {
  //   handle_logout();
  // }
  if (response.status >= 400) {
    console.log(response.text());
    console.log(url_, response.status);
    throw new Error();
  }
  const response_data = response.json();
  return response_data;
};

export async function getHeaders() {
  const myHeaders = new Headers();
  myHeaders.append('content-type', 'application/json');
  if (!(typeof window === 'undefined')) {
    const access_token = localStorage.getItem('access_token');
    if (access_token) {
      myHeaders.append('Authorization', 'Bearer ' + access_token);
    }
  }
  return myHeaders;
}

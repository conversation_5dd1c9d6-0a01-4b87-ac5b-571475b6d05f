import { toast } from 'react-toastify';

interface FetchOptions extends RequestInit {
  token?: string; // Optional token for when you're explicitly passing a token
  toastMessage?: string; // Custom message for the toast
  responseType?: 'json' | 'text'; // Specify response type
  suppressToast?: boolean; // New option to control toast display
  contentType?: string;
  deleteContentType?: boolean;
  excludeCredentials?: boolean;
  suppressAuthErrorHandling?: boolean; // Option to disable automatic auth error handling
}

// Global auth error handler callback - will be set by the app
let globalAuthErrorHandler: ((status: number) => void) | null = null;

/**
 * Sets the global auth error handler callback
 * This should be called once during app initialization
 */
export const setGlobalAuthErrorHandler = (handler: (status: number) => void) => {
  globalAuthErrorHandler = handler;
};

const fetchWrapper = async <T>(
  url: string,
  options: FetchOptions = {}
): Promise<T> => {
  const {
    token,
    toastMessage,
    responseType = 'json',
    suppressToast,
    contentType,
    deleteContentType,
    excludeCredentials,
    suppressAuthErrorHandling,
    ...restOptions
  } = options;

  const headers = new Headers(restOptions.headers);

  // Set default Content-Type header if not provided
  if (!headers.has('Content-Type') && !deleteContentType) {
    headers.set('Content-Type', contentType || 'application/json');
  }

  // Delete Content-Type header if specified
  if (deleteContentType) {
    headers.delete('Content-Type');
  }

  // Set credentials to 'include' for cross-origin requests to ensure cookies are sent and received
  const credentials = excludeCredentials ? undefined : 'include';

  // Note: Setting Cookie header manually doesn't work for browser-to-server requests
  // The browser will automatically include cookies in the request if credentials is 'include'
  // This is only useful for server-side requests
  if (token && typeof window === 'undefined') {
    headers.set('Cookie', `accessToken=${token}`);
  }

  try {
    const response = await fetch(url, {
      ...restOptions,
      headers,
      credentials,
    });

    const result =
      responseType === 'json' ? await response.json() : await response.text();

    if (!response.ok) {
      // Check for authentication errors (401 Unauthorized, 403 Forbidden)
      const isAuthError = response.status === 401 || response.status === 403;

      if (isAuthError && !suppressAuthErrorHandling && globalAuthErrorHandler && typeof window !== 'undefined') {
        // Call global auth error handler for session expiration
        globalAuthErrorHandler(response.status);
        // Don't show additional error toast as the auth handler will show appropriate message
        const authError = new Error('Authentication failed');
        (authError as any).status = response.status;
        throw authError;
      }

      const errorMessage =
        responseType === 'json' && result.message
          ? result.message
          : isAuthError
            ? 'Your session has expired. Please log in again.'
            : 'HTTP Error';

      const error = new Error(errorMessage);
      (error as any).status = response.status;
      throw error;
    }

    return result as T;
  } catch (error) {
    // Only show toast if it's not an auth error and not suppressed
    const isAuthError = (error as any)?.status === 401 || (error as any)?.status === 403;
    const shouldShowToast = typeof window !== 'undefined' &&
      !suppressToast &&
      !isAuthError; // Never show toast for auth errors

    if (shouldShowToast) {
      const errorMessage =
        toastMessage || (error as any)?.message || 'Something went wrong!';
      toast.error(errorMessage);
    }
    throw error;
  }
};

export default fetchWrapper;

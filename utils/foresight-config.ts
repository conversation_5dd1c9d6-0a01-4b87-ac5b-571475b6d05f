/**
 * ForesightJS Configuration and Initialization
 * 
 * This module provides centralized configuration for ForesightJS integration
 * with optimal settings for the ICB application.
 */

import { ForesightManager } from 'js.foresight';

/**
 * ForesightJS configuration interface
 */
export interface ForesightConfig {
  enableMousePrediction: boolean;
  positionHistorySize: number;
  trajectoryPredictionTime: number;
  defaultHitSlop: number | { top: number; left: number; right: number; bottom: number };
  resizeScrollThrottleDelay: number;
  debug: boolean;
  debuggerSettings?: {
    isControlPanelDefaultMinimized: boolean;
  };
}

/**
 * Default ForesightJS configuration optimized for ICB application
 */
export const DEFAULT_FORESIGHT_CONFIG: ForesightConfig = {
  enableMousePrediction: true,
  positionHistorySize: 8,
  trajectoryPredictionTime: 80, // milliseconds
  defaultHitSlop: {
    top: 20,
    left: 20,
    right: 20,
    bottom: 30, // Slightly larger bottom for better mobile experience
  },
  resizeScrollThrottleDelay: 50,
  debug: process.env.NODE_ENV === 'development',
  debuggerSettings: {
    isControlPanelDefaultMinimized: true,
  },
};

/**
 * Production-optimized configuration
 */
export const PRODUCTION_FORESIGHT_CONFIG: ForesightConfig = {
  ...DEFAULT_FORESIGHT_CONFIG,
  debug: false,
  trajectoryPredictionTime: 60, // Slightly more aggressive for production
  positionHistorySize: 6, // Reduced for better performance
};

/**
 * Development configuration with enhanced debugging
 */
export const DEVELOPMENT_FORESIGHT_CONFIG: ForesightConfig = {
  ...DEFAULT_FORESIGHT_CONFIG,
  debug: true,
  trajectoryPredictionTime: 100, // More conservative for debugging
  debuggerSettings: {
    isControlPanelDefaultMinimized: false, // Show debug panel in development
  },
};

/**
 * Mobile-optimized configuration
 */
export const MOBILE_FORESIGHT_CONFIG: ForesightConfig = {
  ...DEFAULT_FORESIGHT_CONFIG,
  defaultHitSlop: {
    top: 30,
    left: 30,
    right: 30,
    bottom: 40, // Larger hit areas for mobile
  },
  trajectoryPredictionTime: 120, // More time for mobile interactions
};

/**
 * Get the appropriate configuration based on environment and device
 */
export const getForesightConfig = (isMobile = false): ForesightConfig => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (isDevelopment) {
    return isMobile ? { ...DEVELOPMENT_FORESIGHT_CONFIG, ...MOBILE_FORESIGHT_CONFIG } : DEVELOPMENT_FORESIGHT_CONFIG;
  }
  
  return isMobile ? { ...PRODUCTION_FORESIGHT_CONFIG, ...MOBILE_FORESIGHT_CONFIG } : PRODUCTION_FORESIGHT_CONFIG;
};

/**
 * Initialize ForesightJS with the appropriate configuration
 * This should be called once at the application startup
 */
export const initializeForesight = (customConfig?: Partial<ForesightConfig>): void => {
  // Check if already initialized to prevent multiple initializations
  if (typeof window !== 'undefined' && (window as any).__foresightInitialized) {
    console.warn('ForesightJS already initialized');
    return;
  }

  // Detect if mobile device (basic detection)
  const isMobile = typeof window !== 'undefined' && 
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  const config = {
    ...getForesightConfig(isMobile),
    ...customConfig,
  };

  try {
    ForesightManager.initialize(config);
    
    // Mark as initialized
    if (typeof window !== 'undefined') {
      (window as any).__foresightInitialized = true;
    }

    console.log('ForesightJS initialized successfully', { config });
  } catch (error) {
    console.error('Failed to initialize ForesightJS:', error);
  }
};

/**
 * Get the ForesightManager instance
 * Ensures initialization before returning the instance
 */
export const getForesightManager = () => {
  if (typeof window === 'undefined') {
    return null; // SSR safety
  }

  // Initialize if not already done
  if (!(window as any).__foresightInitialized) {
    initializeForesight();
  }

  return ForesightManager.instance;
};

/**
 * Utility function to check if ForesightJS is available and initialized
 */
export const isForesightAvailable = (): boolean => {
  return typeof window !== 'undefined' && 
         (window as any).__foresightInitialized === true &&
         ForesightManager.instance !== undefined;
};

/**
 * Reset ForesightJS initialization (useful for testing)
 */
export const resetForesightInitialization = (): void => {
  if (typeof window !== 'undefined') {
    (window as any).__foresightInitialized = false;
  }
};

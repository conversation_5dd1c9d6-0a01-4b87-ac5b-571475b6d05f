'use client';

/**
 * Obfuscates an email address to protect it from spam harvesters
 *
 * This function converts an email address into a format that's harder for bots to scrape
 * but still allows users to click and use the email.
 *
 * @param email The email address to obfuscate
 * @returns A function that can be used in onClick handlers
 */
export const protectEmail = (email: string) => {
  return (event?: React.MouseEvent<Element, MouseEvent>) => {
    if (event) {
      event.preventDefault();
    }

    // Split the email into parts to make it harder for bots to recognize
    const [username, domain] = email.split('@');

    // Reconstruct the email when the user clicks
    const protectedEmail = `${username}@${domain}`;

    // Create a mailto link dynamically
    window.location.href = `mailto:${protectedEmail}`;
  };
};

/**
 * Renders an obfuscated email address as text
 *
 * This function displays an email in a way that's harder for bots to scrape
 *
 * @param email The email address to display
 * @returns The obfuscated email string
 */
export const displayProtectedEmail = (email: string): string => {
  // Replace @ with [at] and . with [dot] to make it harder for bots to recognize
  return email.replace('@', ' [at] ').replace(/\./g, ' [dot] ');
};

<%
const { route, utils, config } = it;
const { _, pascalCase, require } = utils;
const { query, payload, pathParams, headers } = route.request;

const routeDocs = includeFile("./route-docs", { config, route, utils });
const isValidIdentifier = (name) => /^[A-Za-z_$][A-Za-z0-9_$]*$/.test(name);
const routeNamespace = pascalCase(route.routeName.usage);

%>

/**
<%~ routeDocs.description %>

<%~ routeDocs.lines %>

*/
export namespace <% if (isValidIdentifier(routeNamespace)) { %><%~ routeNamespace %><% } else { %>"<%~ routeNamespace %>"<% } %> {
  export type RequestParams = <%~ (pathParams && pathParams.type) || '{}' %>;
  export type RequestQuery = <%~ (query && query.type) || '{}' %>;
  export type RequestBody = <%~ (payload && payload.type) || 'never' %>;
  export type RequestHeaders = <%~ (headers && headers.type) || '{}' %>;
  export type ResponseBody = <%~ route.response.type %>;
}

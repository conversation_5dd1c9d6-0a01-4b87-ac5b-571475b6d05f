import { BASE_URL } from '@/config';
import IndexClients from './index-clients';
import { GetGiftCardResponse } from '@/services/api/data-contracts';
import fetchWrapper from '@/utils/fetch-wrapper';
import { Metadata } from 'next';
import { generateProductSchema } from '@/utils/schema';

export async function generateMetadata({
  params,
  searchParams,
}: {
  params: { slug: string };
  searchParams: { uid: string };
}): Promise<Metadata> {
  try {
    if (!searchParams.uid) {
      return {
        title: 'Gift Card Not Found',
        description: 'The requested gift card could not be found.',
      };
    }

    const giftCardData = await getGiftcardData(Number(searchParams.uid));

    return {
      title: `${
        giftCardData.giftCard?.name || 'Gift Card'
      } - IndianCashback.com`,
      description:
        giftCardData.giftCard?.description ||
        'Get the best gift cards and cashbacks with IndianCashback.com',
      alternates: {
        canonical: `https://www.indiancashback.com/giftcards/${params.slug}?uid=${searchParams.uid}`,
      },
      openGraph: {
        url: `https://www.indiancashback.com/giftcards/${params.slug}?uid=${searchParams.uid}`,
        title: giftCardData.giftCard?.name || 'Gift Card',
        description:
          giftCardData.giftCard?.description ||
          'Get the best gift cards and cashbacks with IndianCashback.com',
      },
    };
  } catch (err) {
    console.error(err);
    return {
      title: 'Gift Card Not Found',
      description: 'The requested gift card could not be found.',
    };
  }
}

async function getGiftcardData(giftCardUid: number) {
  return fetchWrapper<GetGiftCardResponse>(
    `${BASE_URL}/gift-cards/gift-card${giftCardUid}`
  );
}

const Page = async ({ searchParams }: { searchParams: { uid: string } }) => {
  let resData: GetGiftCardResponse;
  try {
    resData = await getGiftcardData(Number(searchParams.uid)); //eslint-disable-line
  } catch (err: any) {
    console.log({ err });
    return (
      <div className='error-container'>
        <h1>Error loading page</h1>
        <p>Please try again later.</p>
      </div>
    );
  }

  // Generate JSON-LD structured data for the gift card as a product
  let giftCardSchema = null;
  if (resData?.giftCard) {
    const giftCard = resData.giftCard;

    // Determine the price if available from denominations
    const priceValue = giftCard.denominations?.length
      ? giftCard.denominations[0].toString()
      : '';

    giftCardSchema = generateProductSchema({
      name: giftCard.name,
      description:
        giftCard.description ||
        `${giftCard.name} Gift Card - Buy with cashback at IndianCashback.com`,
      image: giftCard.imageUrl || '',
      offers: {
        price: priceValue,
        priceCurrency: 'INR',
        url: `https://www.indiancashback.com/giftcards/${giftCard.name}?uid=${searchParams.uid}`,
        // We don't have availability info, so we'll assume it's in stock
        availability: 'https://schema.org/InStock',
      },
      brand: {
        name: giftCard.storeName || 'Indian Cashback',
      },
    });
  }

  return (
    <>
      {giftCardSchema && (
        <script
          dangerouslySetInnerHTML={{ __html: giftCardSchema }}
          type='application/ld+json'
        />
      )}
      <IndexClients data={resData} />
    </>
  );
};

export default Page;

// Dynamic Configuration - using searchParams makes this page dynamic
export const dynamic = 'force-dynamic';

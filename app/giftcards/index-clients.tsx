'use client';
import React, { useState } from 'react';
import CommonHeader from '../components/headers/common-header';
import CommonToolbar from '../components/common-toolbar';
import CommonFilterMobile from '../components/misc/common-filter-mobile';
import Giftcard from '../components/cards/giftcard';
// import BreadcrumbSaveShare from '../components/atoms/breadcrumb-container';
import AsidebarGiftcard from './asidebar-giftcard';
import {
  GetGiftCardListResponse,
  GiftCardBannersResponse,
} from '@/services/api/data-contracts';
import HeroSlider from '../components/landing/hero-slider';
import { PromiseStatus } from '@/types/global-types';
import { ConfigProvider, Pagination, theme } from 'antd';
import { useTheme } from 'next-themes';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCreateMultiQueryString } from '@/utils/custom-hooks';

const IndexClientsGiftcards = ({
  data,
  banners,
}: {
  data: GetGiftCardListResponse;
  banners: GiftCardBannersResponse;
  bannersPromiseStatus?: PromiseStatus;
}) => {
  const [isShowFilterModal, setShowFilterModal] = useState(false);
  const { resolvedTheme } = useTheme();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);
  return (
    <>
      <CommonHeader backRoute='/stores' headline='Flipkart' showMenuBtn />
      <div className='flex justify-center h-full lg:min-h-[calc(100svh-112px)] lg:m-[8px] !mb-0 gap-x-[8px]'>
        <AsidebarGiftcard />
        <div className='w-full lg:w-auto'>
          {/* <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'Flipkart' },
            ]}
            rootClass='sticky top-[104px]'
          /> */}
          <HeroSlider heroSliderData={banners} promiseStatus={'fulfilled'} />
          <section>
            <>
              <CommonToolbar
                onClickFilterBtn={() => setShowFilterModal(!isShowFilterModal)}
                rootClassName='sticky top-[64px] lg:top-[0]'
              />
              <div className='bg-container mx-[6px] px-[8px] lg:mx-0 lg:pt-[16px] lg:px-[40px] pb-[18px]'>
                <h4 className='hidden lg:block text-[14px] font-pat font-normal text-blackWhite'>
                  Results{' '}
                  <span className='text-xs font-nexa font-[800]'>
                    ({data.pagination.total})
                  </span>
                </h4>
                {/* <div className='grid grid-cols-2 gap-x-[8px] pt-[23px] gap-y-[16px] md:grid-cols-3 lg:grid-cols-3 lg:gap-[24px] xl:grid-cols-4 min-[1440px]:grid-cols-5 ' /> */}
                <div className='grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-[8px] justify-items-center pt-[23px]'>
                  {data.giftCards.map((giftcard) => (
                    <Giftcard
                      caption={giftcard.caption}
                      imgUrl={giftcard.imageUrl}
                      key={giftcard.uid}
                      saved={giftcard.saved}
                      title={giftcard.name}
                      uid={giftcard.uid}
                    />
                  ))}
                </div>
                {data.pagination.pageSize > 0 && (
                  <div className='flex-center w-full my-[50px]'>
                    <ConfigProvider
                      theme={{
                        algorithm:
                          resolvedTheme === 'dark'
                            ? theme.darkAlgorithm
                            : theme.defaultAlgorithm,
                      }}
                    >
                      <Pagination
                        defaultCurrent={1}
                        defaultPageSize={15}
                        onChange={(pageNumber, pageSize) =>
                          replace(
                            pathname +
                              '?' +
                              createMultiQueryString([
                                { name: 'page', value: pageNumber.toString() },
                                {
                                  name: 'pageSize',
                                  value: pageSize.toString(),
                                },
                              ])
                          )
                        }
                        total={data.pagination.total}
                      />
                    </ConfigProvider>
                  </div>
                )}
              </div>
            </>
          </section>
        </div>
      </div>

      <CommonFilterMobile
        filterProps={[]}
        isShowFilterModal={isShowFilterModal}
        setShowFilterModal={() => setShowFilterModal(!isShowFilterModal)}
      />
    </>
  );
};

export default IndexClientsGiftcards;

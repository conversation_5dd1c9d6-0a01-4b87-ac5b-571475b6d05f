'use client';
import React, { useState } from 'react';
import CommonFilterSidebar from '@/app/components/misc/common-filter-sidebar';

const AsidebarGiftcard = () => {
  //eslint-disable-next-line
  const [value, setValue] = useState('');
  return (
    <aside className='shrink-0 w-[270px] lg:max-h-[calc(100svh-112px)] sticky top-[112px] overflow-hidden  hidden lg:block scrollbarNone'>
      {/* <div className='h-[155px] sticky top-0 bg-container flex flex-col gap-y-[25px] items-center justify-center pl-[20px] pr-[10px]'>
        <div className='relative w-[88px] h-[49px] shrink-0'>
          <Image alt='flipkart' fill src={'/temp/Flipkart.png'} />
        </div>

        <SlidingButton
          buttonDetails={[
            { title: 'Offers', value: 'Offers' },
            { title: 'Giftcard', value: 'Giftcard' },
          ]}
          defaultSelectedBtn={1}
          onChange={(e) => setValue(e.target.value)}
          rootClassName='!my-0'
          uniqueId='storeByCbSidebar'
        />
      </div> */}

      <CommonFilterSidebar
        filterProps={[]}
        rootClass='sticky !mt-0 top-[0] !h-[calc(100%-10px)]'
      />
    </aside>
  );
};

export default AsidebarGiftcard;

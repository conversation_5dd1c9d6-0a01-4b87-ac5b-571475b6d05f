'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import CommonHeader from '@/app/components/headers/common-header';
import React from 'react';
import { motion } from 'framer-motion';
import SmartLink from '../components/common/smart-link';
import { LinkType } from '@/utils/link-utils';

// --- Animation Variants ---
const pageContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.5,
    },
  },
};

const sectionContainerVariants = {
  hidden: { opacity: 1 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
    },
  },
};

const sectionItemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: 'easeOut',
    },
  },
};

const teamMemberVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: 'easeOut',
    },
  },
};

// Team data
const teamMembers = [
  {
    name: '<PERSON><PERSON><PERSON>',
    linkedin: 'https://www.linkedin.com/in/jes<PERSON>-jacob',
    role: 'Co-Founder & COO',
    description:
      'Strategic operations leader driving operational excellence and business growth across all verticals.',
    gradient: 'from-blue-500 to-purple-600',
  },
  {
    name: 'Delbin Thomas',
    linkedin: 'https://www.linkedin.com/in/delbinthomas',
    role: 'Co-Founder & CFO',
    description:
      'Financial strategist managing company finances, investments, and ensuring sustainable growth.',
    gradient: 'from-green-500 to-blue-500',
  },
  {
    name: 'Mekha Krishnan M',
    linkedin: 'https://www.linkedin.com/in/mekhakrishnanm/',
    role: 'CEO',
    description:
      "Visionary leader steering IndianCashback's mission to revolutionize the cashback industry in India.",
    gradient: 'from-pink-500 to-red-500',
  },
  {
    name: 'Eswin Paul',
    linkedin: 'https://www.linkedin.com/in/eswinpaul',
    role: 'CMO',
    description:
      'Marketing maestro crafting compelling campaigns and building strong brand presence across India.',
    gradient: 'from-orange-500 to-red-500',
  },
  {
    name: 'Dennis Sam',
    linkedin: 'https://www.linkedin.com/in/denniarems',
    role: 'CTO',
    description:
      'Technology visionary architecting robust platforms and leading digital transformation initiatives.',
    gradient: 'from-purple-500 to-pink-500',
  },
  {
    name: 'Abin Baby',
    linkedin: 'https://www.linkedin.com/in/abin-baby',
    role: 'Creative Head',
    description:
      'Creative visionary designing compelling user experiences and brand aesthetics that resonate with users.',
    gradient: 'from-indigo-500 to-blue-500',
  },
  {
    name: 'Anas KVM',
    linkedin: 'https://www.linkedin.com/in/kvmanas',
    role: 'Lead Developer',
    description:
      'Technical expert leading development teams and delivering innovative cashback solutions.',
    gradient: 'from-teal-500 to-green-500',
  },
];

const IndexClientsTeam = () => {
  // Get initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((word) => word[0])
      .join('')
      .toUpperCase();
  };

  return (
    <>
      <CommonHeader headline='Meet Our Team' />
      <motion.section
        animate='visible'
        className='header-container'
        initial='hidden'
        variants={pageContainerVariants}
      >
        <BreadcrumbSaveShare
          breadCrumbs={[
            { title: 'Cashback Home', link: '/' },
            { title: 'Meet Our Team' },
          ]}
        />

        <div className='mx-[6px] px-[8px] lg:mx-0 lg:px-[40px] py-[26px] text-blackWhite bg-container lg:bg-[#E0E0E0] lg:bg-container rounded-b-[10px]'>
          <motion.div
            initial='hidden'
            variants={sectionContainerVariants}
            viewport={{ once: true, amount: 0.1 }}
            whileInView='visible'
          >
            {/* Hero Section */}
            <motion.div
              className='text-center mb-[40px] lg:mb-[60px]'
              variants={sectionItemVariants}
            >
              <h1 className='text-[20px] lg:text-[32px] font-pat font-bold text-primary mb-[16px]'>
                Meet the Minds Behind IndianCashback
              </h1>
              <p className='text-[12px] lg:text-[16px] font-medium text-gray-600 dark:text-gray-300 max-w-[600px] mx-auto leading-relaxed'>
                Our passionate team of innovators, developers, and visionaries
                work tirelessly to bring you the best cashback experience. Get
                to know the people who make saving money simple and rewarding.
              </p>
            </motion.div>

            {/* Team Stats */}
            <motion.div
              className='grid grid-cols-3 gap-[20px] lg:gap-[40px] mb-[40px] lg:mb-[60px] max-w-[600px] mx-auto'
              variants={sectionItemVariants}
            >
              <div className='text-center bg-white dark:bg-[#2A2D35] p-[16px] lg:p-[24px] rounded-[12px] shadow-md'>
                <div className='text-[20px] lg:text-[32px] font-bold text-primary'>
                  {teamMembers.length}
                </div>
                <div className='text-[10px] lg:text-[14px] font-medium text-gray-600 dark:text-gray-300'>
                  Core Team Members
                </div>
              </div>
              <div className='text-center bg-white dark:bg-[#2A2D35] p-[16px] lg:p-[24px] rounded-[12px] shadow-md'>
                <div className='text-[20px] lg:text-[32px] font-bold text-primary'>
                  10+
                </div>
                <div className='text-[10px] lg:text-[14px] font-medium text-gray-600 dark:text-gray-300'>
                  Years Experience
                </div>
              </div>
              <div className='text-center bg-white dark:bg-[#2A2D35] p-[16px] lg:p-[24px] rounded-[12px] shadow-md'>
                <div className='text-[20px] lg:text-[32px] font-bold text-primary'>
                  24/7
                </div>
                <div className='text-[10px] lg:text-[14px] font-medium text-gray-600 dark:text-gray-300'>
                  Dedicated Support
                </div>
              </div>
            </motion.div>

            {/* Team Members Grid */}
            <motion.div
              className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[20px] lg:gap-[30px] mb-[40px]'
              variants={sectionContainerVariants}
            >
              {teamMembers.map((member, index) => (
                <motion.div
                  className='group bg-white dark:bg-[#2A2D35] rounded-[16px] shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden'
                  key={index}
                  variants={teamMemberVariants}
                  whileHover={{
                    y: -8,
                    transition: { duration: 0.3, ease: 'easeOut' },
                  }}
                >
                  {/* Card Header with Gradient */}
                  <div
                    className={`h-[80px] lg:h-[100px] bg-gradient-to-r ${member.gradient} relative`}
                  >
                    <div className='absolute inset-0 bg-black bg-opacity-10' />
                  </div>

                  {/* Avatar */}
                  <div className='relative -mt-[40px] lg:-mt-[50px] text-center mb-[20px]'>
                    <div
                      className={`w-[80px] h-[80px] lg:w-[100px] lg:h-[100px] mx-auto rounded-full bg-gradient-to-r ${member.gradient} flex items-center justify-center shadow-lg border-4 border-white dark:border-[#2A2D35]`}
                    >
                      <span className='text-white font-bold text-[24px] lg:text-[32px]'>
                        {getInitials(member.name)}
                      </span>
                    </div>
                  </div>

                  {/* Card Content */}
                  <div className='px-[20px] pb-[24px]'>
                    <h3 className='text-[14px] lg:text-[18px] font-pat font-bold text-center mb-[4px]'>
                      {member.name}
                    </h3>
                    <p className='text-[10px] lg:text-[12px] font-medium text-primary text-center mb-[12px]'>
                      {member.role}
                    </p>
                    <p className='text-[9px] lg:text-[11px] text-gray-600 dark:text-gray-300 text-center leading-relaxed mb-[16px]'>
                      {member.description}
                    </p>

                    {/* LinkedIn Button */}
                    <div className='text-center'>
                      <SmartLink
                        className='inline-flex items-center gap-[8px] bg-[#0077B5] hover:bg-[#005582] text-white px-[16px] py-[8px] rounded-[8px] text-[10px] lg:text-[12px] font-medium transition-colors duration-300'
                        href={member.linkedin}
                        linkType={LinkType.EXTERNAL}
                      >
                        <svg
                          className='w-[14px] h-[14px]'
                          fill='currentColor'
                          viewBox='0 0 20 20'
                        >
                          <path
                            clipRule='evenodd'
                            d='M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z'
                            fillRule='evenodd'
                          />
                        </svg>
                        Connect on LinkedIn
                      </SmartLink>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            {/* Call to Action Section */}
            <motion.div
              className='text-center bg-gradient-to-r from-primary to-blue-600 text-white p-[30px] lg:p-[40px] rounded-[16px] mb-[40px]'
              variants={sectionItemVariants}
            >
              <h2 className='text-[16px] lg:text-[24px] font-pat font-bold mb-[12px]'>
                Join Our Mission to Save Money for India
              </h2>
              <p className='text-[11px] lg:text-[14px] font-medium mb-[20px] opacity-90 max-w-[500px] mx-auto'>
                We're always looking for passionate individuals to join our
                team. Help us revolutionize the cashback experience for millions
                of Indian shoppers.
              </p>
              <SmartLink
                className='inline-flex items-center gap-[8px] bg-white text-primary hover:bg-gray-100 px-[20px] py-[12px] rounded-[8px] text-[12px] lg:text-[14px] font-medium transition-colors duration-300'
                href='/contact'
                linkType={LinkType.INTERNAL}
              >
                Get in Touch
                <svg
                  className='w-[16px] h-[16px]'
                  fill='none'
                  stroke='currentColor'
                  viewBox='0 0 24 24'
                >
                  <path
                    d='M17 8l4 4m0 0l-4 4m4-4H3'
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth={2}
                  />
                </svg>
              </SmartLink>
            </motion.div>

            {/* Company Values */}
            <motion.div
              className='grid grid-cols-1 md:grid-cols-3 gap-[20px] mb-[40px]'
              variants={sectionContainerVariants}
            >
              <motion.div
                className='text-center bg-white dark:bg-[#2A2D35] p-[24px] rounded-[12px] shadow-md'
                variants={sectionItemVariants}
                whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
              >
                <div className='w-[60px] h-[60px] bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-[16px]'>
                  <svg
                    className='w-[30px] h-[30px] text-white'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      d='M13 10V3L4 14h7v7l9-11h-7z'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                    />
                  </svg>
                </div>
                <h4 className='text-[12px] lg:text-[16px] font-pat font-bold mb-[8px]'>
                  Innovation
                </h4>
                <p className='text-[9px] lg:text-[12px] text-gray-600 dark:text-gray-300'>
                  Constantly pushing boundaries to deliver cutting-edge cashback
                  solutions.
                </p>
              </motion.div>

              <motion.div
                className='text-center bg-white dark:bg-[#2A2D35] p-[24px] rounded-[12px] shadow-md'
                variants={sectionItemVariants}
                whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
              >
                <div className='w-[60px] h-[60px] bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-[16px]'>
                  <svg
                    className='w-[30px] h-[30px] text-white'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      d='M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                    />
                  </svg>
                </div>
                <h4 className='text-[12px] lg:text-[16px] font-pat font-bold mb-[8px]'>
                  Teamwork
                </h4>
                <p className='text-[9px] lg:text-[12px] text-gray-600 dark:text-gray-300'>
                  Collaborating seamlessly to achieve extraordinary results
                  together.
                </p>
              </motion.div>

              <motion.div
                className='text-center bg-white dark:bg-[#2A2D35] p-[24px] rounded-[12px] shadow-md'
                variants={sectionItemVariants}
                whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
              >
                <div className='w-[60px] h-[60px] bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-[16px]'>
                  <svg
                    className='w-[30px] h-[30px] text-white'
                    fill='none'
                    stroke='currentColor'
                    viewBox='0 0 24 24'
                  >
                    <path
                      d='M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z'
                      strokeLinecap='round'
                      strokeLinejoin='round'
                      strokeWidth={2}
                    />
                  </svg>
                </div>
                <h4 className='text-[12px] lg:text-[16px] font-pat font-bold mb-[8px]'>
                  Customer Focus
                </h4>
                <p className='text-[9px] lg:text-[12px] text-gray-600 dark:text-gray-300'>
                  Putting our users first in everything we design and develop.
                </p>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </motion.section>
    </>
  );
};

export default IndexClientsTeam;

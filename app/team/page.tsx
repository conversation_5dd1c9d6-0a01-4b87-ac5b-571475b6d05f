import React from 'react';
import IndexClientsTeam from './index-clients';
import { Metadata } from 'next';
import { generateOrganizationSchema } from '@/utils/schema';

export const metadata: Metadata = {
  title: 'Meet Our Team - IndianCashback Leadership & Experts',
  description:
    'Meet the talented team behind IndianCashback. Our dedicated professionals work tirelessly to bring you the best cashback deals and savings opportunities across India.',
  alternates: {
    canonical: 'https://www.indiancashback.com/team',
  },
  openGraph: {
    url: 'https://www.indiancashback.com/team',
    title: 'Meet Our Team - IndianCashback Leadership & Experts',
    description:
      'Meet the talented team behind IndianCashback. Our dedicated professionals work tirelessly to bring you the best cashback deals and savings opportunities across India.',
  },
};

const Page = () => {
  // Generate detailed organization schema for Team page
  const organizationSchema = generateOrganizationSchema({
    name: 'IndianCashback.com',
    url: 'https://www.indiancashback.com',
    logo: 'https://www.indiancashback.com/img/logo.png',
    description:
      "Meet the passionate team behind IndianCashback.com, India's leading cashback platform. Our experienced professionals are dedicated to helping users save money while shopping online.",
    sameAs: [
      'https://www.facebook.com/indiancashback',
      'https://twitter.com/indiancashback',
      'https://www.instagram.com/indiancashback',
      'https://www.linkedin.com/company/indiancashback',
      'https://www.youtube.com/channel/indiancashback',
    ],
  });

  return (
    <>
      <script
        dangerouslySetInnerHTML={{ __html: organizationSchema }}
        type='application/ld+json'
      />
      <IndexClientsTeam />
    </>
  );
};

export default Page;

// ISR Configuration
// Revalidate every 24 hours (86400 seconds)
export const revalidate = 86400;

'use client';
import React, { useState } from 'react';
import CommonHeader from '../../components/headers/common-header';
import BreadcrumbSaveShare from '../../components/atoms/breadcrumb-container';
import Image from 'next/image';
import {
  InputFieldNormal,
  TextAreaNormal,
} from '../../components/atoms/form-inputs';

import { GetBankAccountDataResponse } from '@/services/api/data-contracts';
import { useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import fetchWrapper from '@/utils/fetch-wrapper';
import { motion } from 'framer-motion';
import ThemeButton from '@/app/components/atoms/theme-btn';
import { EditIcon } from 'lucide-react';

const IndexClientsBankDetails = ({
  data,
}: {
  data: GetBankAccountDataResponse;
}) => {
  const [isFormDisabled, setFormDisabled] = useState(true);

  const {
    reset: resetBankDetails,
    register: registerBankDetails,
    handleSubmit: submitBankDetails,
    formState: {
      errors: updateBankDetailsError,
      isDirty: isBankDetailFormModified,
    },
  } = useForm({
    defaultValues: {
      holderName: data?.holderName,
      accountNumber: data?.accountNumber,
      bankName: data?.bankName,
      branchName: data?.branchName,
      postcode: data?.postcode,
      address: data?.address,
      ifsc: data?.ifsc,
    },
  });

  async function updateBankDetails(params: any) {
    if (isBankDetailFormModified) {
      await fetchWrapper('/api/proxy/users/update-bank-details', {
        method: 'POST',
        body: JSON.stringify(params),
      });
      setFormDisabled(true);
      resetBankDetails(params);
      return toast.info('Bank Details updated successfully.');
    }

    return toast.info('There are no changes to save.');
  }

  const handleEditButtonClick = () => {
    if (isFormDisabled) {
      setFormDisabled(false);
    } else {
      setFormDisabled(true);
      resetBankDetails(data);
    }
  };

  return (
    <>
      <CommonHeader
        headline='Update Bank Details'
        subHeading={<span>Update your bank details</span>}
      />
      <motion.section
        animate={{ opacity: 1 }}
        className='header-container'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='sticky top-[104px] z-[30]'
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            <BreadcrumbSaveShare
              breadCrumbs={[
                { title: 'Cashback Home', link: '/' },
                { title: 'Payments Page', link: '/payments' },
                { title: 'Update Bank Details' },
              ]}
            />
          </motion.div>
          <motion.div
            animate={{ opacity: 1 }}
            className='lg:bg-[#E0E0E0] lg:dark:bg-container pb-[60px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='bg-container lg:bg-transparent rounded-b-[10px] pb-[20px] lg:pb-0 mx-[6px] lg:mx-0 px-[8px] lg:px-[40px] xl:px-[50px]'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <motion.div
                animate={{ opacity: 1, scale: 1 }}
                className='flex-center w-full pt-[58px] lg:pt-[26px]'
                initial={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <Image
                  alt='bank image'
                  className='w-[120px] lg:w-[146px] h-auto'
                  height={349}
                  src='/img/bank.png'
                  width={510}
                />
              </motion.div>
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className='flex flex-col items-center mt-[28px] w-full lg:mt-[22px] lg:bg-container lg:dark:bg-[#2d2e32] lg:py-[21px] lg:px-[30px] lg:rounded-[10px]'
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                whileHover={{
                  boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)',
                  transition: { duration: 0.3 },
                }}
              >
                <div className='flex w-full items-center justify-center mt-5 px-8'>
                  <motion.h4
                    animate={{ opacity: 1 }}
                    className='hidden lg:inline-block text-blackWhite text-xs lg:text-xl font-normal font-pat'
                    initial={{ opacity: 0 }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                  >
                    Add Bank Info
                  </motion.h4>
                  {isFormDisabled && (
                    <ThemeButton
                      className='ml-5 max-w-[120px] !w-fit px-4 hover:bg-primaryDark active:bg-primary'
                      icon={<EditIcon className='w-3 h-3 ml-2' />}
                      onClick={handleEditButtonClick}
                      text={'Edit'}
                    />
                  )}
                </div>
                <motion.div
                  animate={{ opacity: 1 }}
                  className='w-full lg:flex lg:flex-wrap lg:justify-center px-4 gap-x-[30px] lg:mt-[16px] justify-center'
                  initial={{ opacity: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 }}
                >
                  <motion.div
                    animate={{ opacity: 1, x: 0 }}
                    className='flex flex-col gap-y-[15px] lg:gap-y-[20px] mx-auto lg:mx-0 w-full sm:w-[360px] md:w-[400px]'
                    initial={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                  >
                    <InputFieldNormal
                      defaultValue={data?.holderName}
                      isDisabled={isFormDisabled}
                      label='Full Name as per Bank details'
                      name='holderName'
                      placeholder='Full Name'
                      register={registerBankDetails}
                      validationSchema={{
                        required: 'Full Name is required',
                        pattern: {
                          value: /^[a-zA-Z\s,.'-]{3,}$/,
                          message: 'Invalid full name',
                        },
                      }}
                    />
                    {updateBankDetailsError.holderName?.message && (
                      <span className='text-[#F00] text-[10px] font-light'>
                        {updateBankDetailsError?.holderName?.message?.toString()}
                      </span>
                    )}

                    <InputFieldNormal
                      defaultValue={data?.bankName}
                      isDisabled={isFormDisabled}
                      label='Bank Name'
                      name='bankName'
                      placeholder='Bank Name'
                      register={registerBankDetails}
                      validationSchema={{
                        required: 'Bank Name is required',
                        pattern: {
                          value: /^[a-zA-Z\s,.'-]{3,}$/,
                          message: 'Invalid bank name',
                        },
                      }}
                    />
                    {updateBankDetailsError?.bankName?.message && (
                      <span className='text-[#F00] text-[10px] font-light'>
                        {updateBankDetailsError?.bankName?.message?.toString()}
                      </span>
                    )}

                    <InputFieldNormal
                      defaultValue={data?.branchName}
                      isDisabled={isFormDisabled}
                      label='Bank Branch'
                      name='branchName'
                      placeholder='Branch Name'
                      register={registerBankDetails}
                      validationSchema={{
                        required: 'Branch Name is required',
                        pattern: {
                          value: /^[a-zA-Z\s,.'-]{3,}$/,
                          message: 'Invalid branch name',
                        },
                      }}
                    />

                    {updateBankDetailsError?.branchName?.message && (
                      <span className='text-[#F00] text-[10px] font-light'>
                        {updateBankDetailsError?.branchName?.message?.toString()}
                      </span>
                    )}
                  </motion.div>

                  <motion.div
                    animate={{ opacity: 1, x: 0 }}
                    className='flex flex-col gap-y-[15px] lg:gap-y-[20px] mx-auto lg:mx-0 w-full sm:w-[360px] md:w-[400px]'
                    initial={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.5, delay: 0.9 }}
                  >
                    <InputFieldNormal
                      defaultValue={data?.accountNumber}
                      isDisabled={isFormDisabled}
                      label='Account Number'
                      name='accountNumber'
                      placeholder='Account Number'
                      register={registerBankDetails}
                      validationSchema={{
                        required: 'Account Number is required',
                        pattern: {
                          value: /^\d{8,18}$/,
                          message: 'Invalid account number',
                        },
                      }}
                    />
                    {updateBankDetailsError?.accountNumber?.message && (
                      <span className='text-[#F00] text-[10px] font-light'>
                        {updateBankDetailsError?.accountNumber?.message?.toString()}
                      </span>
                    )}

                    <InputFieldNormal
                      defaultValue={data?.ifsc}
                      isDisabled={isFormDisabled}
                      label='Branch IFS Code'
                      name='ifsc'
                      placeholder='IFSC'
                      register={registerBankDetails}
                      validationSchema={{
                        required: 'IFSC is required',
                        pattern: {
                          value: /^[A-Za-z]{4}0[A-Z0-9a-z]{6}$/,
                          message: 'Invalid IFSC',
                        },
                      }}
                    />

                    {updateBankDetailsError?.ifsc?.message && (
                      <span className='text-[#F00] text-[10px] font-light'>
                        {updateBankDetailsError?.ifsc?.message?.toString()}
                      </span>
                    )}

                    <InputFieldNormal
                      defaultValue={data?.postcode}
                      isDisabled={isFormDisabled}
                      label='Your Address PIN Code'
                      name='postcode'
                      placeholder='Pincode'
                      register={registerBankDetails}
                      validationSchema={{
                        required: 'Pincode is required',
                        pattern: {
                          value: /^\d{6}$/,
                          message: 'Invalid pincode',
                        },
                      }}
                    />
                    {updateBankDetailsError?.postcode?.message && (
                      <span className='text-[#F00] text-[10px] font-light'>
                        {updateBankDetailsError?.postcode?.message?.toString()}
                      </span>
                    )}
                  </motion.div>

                  <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    className='flex flex-col flex-center gap-y-[15px] lg:gap-y-[20px] w-full sm:max-w-[360px] md:max-w-[400px] lg:max-w-[830px] mx-auto lg:mx-0 mt-[18px]'
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.5, delay: 1 }}
                  >
                    <TextAreaNormal
                      defaultValue={data?.address}
                      inputClass='!w-full max-w-none !h-[120px]'
                      inputHeight={90}
                      isDisabled={isFormDisabled}
                      label='Your Address'
                      name='address'
                      placeholder='Your Address'
                      register={registerBankDetails}
                      rootClass='w-full'
                      validationSchema={{
                        required: 'Address is required',
                        pattern: {
                          value: /^[a-zA-Z0-9\s,.'-]+$/,
                          message: 'Invalid Address',
                        },
                      }}
                    />
                    {updateBankDetailsError?.address?.message && (
                      <span className='text-[#F00] text-[10px] font-light'>
                        {updateBankDetailsError?.address?.message?.toString()}
                      </span>
                    )}
                  </motion.div>
                </motion.div>

                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  className='mt-[26px] flex-center w-full'
                  initial={{ opacity: 0, y: 20 }}
                  transition={{ duration: 0.5, delay: 1.1 }}
                >
                  {isFormDisabled ? (
                    <div className='flex flex-col items-center gap-2'>
                      <ThemeButton
                        className='ml-5 max-w-[120px] px-2 hover:bg-primaryDark active:bg-primary'
                        onClick={handleEditButtonClick}
                        text={'Edit Details'}
                      />
                      <span className='text-xs text-gray-500 dark:text-gray-400'>
                        Update your bank details
                      </span>
                    </div>
                  ) : (
                    <motion.div
                      animate={{ opacity: 1, scale: 1 }}
                      className='w-full flex flex-col sm:flex-row justify-center items-center gap-3 pb-4'
                      initial={{ opacity: 0, scale: 0.9 }}
                      transition={{ duration: 0.3 }}
                    >
                      <ThemeButton
                        className='w-full max-w-[140px] px-2 hover:bg-primaryDark active:bg-primary'
                        isDisabled={!isBankDetailFormModified}
                        onClick={submitBankDetails(updateBankDetails)}
                        text={'Save Changes'}
                      />
                      <ThemeButton
                        className='max-w-[120px] px-2 bg-slate-500'
                        onClick={() => {
                          setFormDisabled(true);
                          resetBankDetails();
                        }}
                        text={'Cancel'}
                      />
                    </motion.div>
                  )}
                </motion.div>
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default IndexClientsBankDetails;

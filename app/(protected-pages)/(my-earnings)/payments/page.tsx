import React from 'react';
import IndexClientsPayments from './index-clients';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import {
  GetBankAccountDataResponse,
  GetPaymentListResponse,
} from '@/services/api/data-contracts';
import { BASE_URL } from '@/config';

const getBankDetails = async () => {
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return fetchWrapper<GetBankAccountDataResponse>(
    `${BASE_URL}/users/get-bank-details`,
    {
      token: token?.value,
    }
  );
};

const getPaymentHistory = async (searchParams: any) => {
  const {
    searchParam,
    sortType = 'newest',
    status,
    page,
    pageSize,
    startDate,
    endDate,
  } = searchParams;

  const queryParams = Object.entries({
    searchParam,
    sortType,
    status,
    page,
    pageSize,
    startDate,
    endDate,
  })
    .filter(([_, value]) => value) //eslint-disable-line
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return fetchWrapper<GetPaymentListResponse>(
    `${BASE_URL}/payment?${queryParams}`,
    {
      token: token?.value,
      suppressToast: true,
    }
  );
};

const Page = async ({ searchParams }: { searchParams: any }) => {
  let bankData: GetBankAccountDataResponse;
  let paymentHistoryData: GetPaymentListResponse | undefined;

  const tab = searchParams.tab;
  try {
    bankData = await getBankDetails();
  } catch (error) {
    return error;
  }

  try {
    paymentHistoryData = await getPaymentHistory(searchParams);
  } catch (error) {
    console.log('error', error);
    // Payment history is optional, continue without it
    paymentHistoryData = undefined;
  }

  return (
    <IndexClientsPayments
      bankData={bankData}
      paymentHistoryData={paymentHistoryData}
      tab={tab}
    />
  );
};

export default Page;

import { InputFieldNormal } from '@/app/components/atoms/form-inputs';
import UploadSVG from '@/app/components/svg/upload-icon';
import { useAppSelector } from '@/redux/hooks';
import {
  setIsCouponUsed,
  setReportingPlatform,
  setReportingUserType,
} from '@/redux/slices/report-missing-cb-slice';
import { ConfigProvider, Radio, Space, Switch, Upload, theme } from 'antd';
import React from 'react';
import { useDispatch } from 'react-redux';
import { ReportMissingErrorsState } from './index-clients';
import { useTheme } from 'next-themes';

const RmcStep2 = ({
  register,
  uploadProps,
  orderErrorDetails,
  validationErrors,
}: {
  register?: any;
  uploadProps?: any;
  orderErrorDetails?: any;
  validationErrors?: ReportMissingErrorsState;
}) => {
  const {
    reportMissingStep,
    reportingUserType,
    reportingPlatform,
    isCouponUsed,
    reportingOrderId,
  } = useAppSelector((state) => state.reportMissingCbStep);

  const dispatch = useDispatch();
  const { resolvedTheme } = useTheme();

  if (reportMissingStep !== 2) {
    return;
  }

  return (
    <div className='lg:grid lg:grid-cols-2'>
      <div className='pl-[8px]'>
        <h4 className='text-[11px] font-medium text-blackWhite'>
          User Type <span className='sup text-[#FF4141]'>*</span>
        </h4>

        <Radio.Group
          className='mt-[9px]'
          onChange={(e) => {
            dispatch(setReportingUserType(e.target.value));
          }}
          size='small'
          value={reportingUserType}
        >
          <Space direction='horizontal'>
            <Radio
              className='text-[12px] font-normal text-blackWhite'
              value={'new'}
            >
              New
            </Radio>
            <Radio
              className='text-[12px] font-normal text-blackWhite'
              value={'old'}
            >
              Old
            </Radio>
          </Space>
        </Radio.Group>
        {validationErrors?.userType && (
          <span className='text-[#F00] text-[10px] font-light  mt-2 block'>
            {validationErrors?.userType?.toString()}
          </span>
        )}
        <span className='text-[#4F4F4F] dark:text-[#a2a0a0] text-[8px] font-light mt-[9px] block'>
          If you are using the store for first time, select NEW User, else
          select OLD User
        </span>
      </div>
      <div className='mt-[25px] lg:mt-0'>
        <h4 className='text-[11px] pl-[8px] font-medium text-blackWhite'>
          Order ID<span className='sup text-[#FF4141]'>*</span>
        </h4>
        <InputFieldNormal
          defaultValue={reportingOrderId}
          name='orderId'
          placeholder='Enter Order ID'
          register={register}
          rootClass='mt-[8px]'
          validationSchema={{
            required: 'Order ID is required',
            pattern: {
              value: /^[a-zA-Z0-9\s,.'-]+$/,
              message: 'Invalid Order ID',
            },
          }}
        />
        {orderErrorDetails?.orderId?.message && (
          <span className='text-[#F00] text-[10px] font-light'>
            {orderErrorDetails?.orderId?.message?.toString()}
          </span>
        )}
        <span className='text-[#4F4F4F] dark:text-[#a2a0a0] pl-[8px] text-[8px] font-light mt-[9px] block'>
          If you are using the store for first time, select NEW User, else
          select OLD User
        </span>
      </div>
      <div className='mt-[25px] lg:mt-[10px]'>
        <h4 className='text-[11px] pl-[8px] font-medium text-blackWhite'>
          Did you use coupon ?<span className='sup text-[#FF4141]'>*</span>
        </h4>
        <div className='pl-[8px] mt-[8px] user-subscription'>
          <Switch
            checkedChildren={
              <span className='text-[9px] lg:text-[10px] translate-y-[-1px] font-medium text-blackWhite inline-block'>
                Yes
              </span>
            }
            className='!bg-white dark:!bg-[#555966] shadow-md'
            defaultChecked={isCouponUsed}
            onChange={(checked: boolean) => {
              dispatch(setIsCouponUsed(checked));
            }}
            unCheckedChildren={
              <span className='text-[9px] lg:text-[10px] translate-y-[-1px] font-medium text-blackWhite inline-block'>
                No
              </span>
            }
          />
        </div>
      </div>
      <div className='mt-[25px] lg:mt-[18px]'>
        <h4 className='text-[11px] pl-[8px] font-medium text-blackWhite'>
          Amount you Paid<span className='sup text-[#FF4141]'>*</span>
        </h4>
        <InputFieldNormal
          name='orderAmount'
          placeholder='Enter Amount you Paid'
          register={register}
          rootClass='mt-[8px]'
          validationSchema={{
            required: 'Amount is required',
            pattern: {
              value: /^\d*[1-9]\d*$/,
              message: 'Invalid Amount',
            },
          }}
        />

        {orderErrorDetails?.orderAmount?.message && (
          <span className='text-[#F00] text-[10px] font-light'>
            {orderErrorDetails?.orderAmount?.message?.toString()}
          </span>
        )}
      </div>
      <div className='mt-[25px] lg:mt-[10px]'>
        <h4 className='text-[11px] pl-[8px] font-medium text-blackWhite mb-[8px]'>
          Attach Invoice / Order Confirmation
          <span className='sup text-[#FF4141]'>*</span>
        </h4>

        <ConfigProvider
          theme={{
            algorithm:
              resolvedTheme === 'dark'
                ? theme.darkAlgorithm
                : theme.defaultAlgorithm,
          }}
        >
          <Upload
            {...uploadProps}
            maxCount={1}

            // onChange={handleFileChange}
          >
            <button className='flex-center gap-x-[5px] h-[40px] px-[10px] text-[10px] font-medium border-[1px] border-dashed border-primary rounded-[5px] ml-[5px] text-blackWhite'>
              <UploadSVG className='w-[18px] h-[18px] text-primary' />
              Click to Upload
            </button>
          </Upload>
        </ConfigProvider>
        <span className='mt-[8px] text-[8px] text-[#4F4F4F] dark:text-[#a2a0a0] ml-[8px]'>
          Max 2MB Files. (PDF, Docs and Images)
        </span>

        {validationErrors?.invoice && (
          <span className='text-[#F00] text-[10px] font-light  mt-2 block'>
            {validationErrors?.invoice.toString()}
          </span>
        )}
        <span className='mt-[5px] text-[8px] text-[#4F4F4F] dark:text-[#a2a0a0] ml-[8px] block'>
          Proof Of delivery or Invoice / Order confirmation details from store
          order history page.
        </span>
      </div>
      <div className='mt-[25px] pl-[8px]'>
        <h4 className='text-[11px] font-medium text-blackWhite'>
          Platform <span className='sup text-[#FF4141]'>*</span>
        </h4>
        <Radio.Group
          className='mt-[9px]'
          onChange={(e) => {
            dispatch(setReportingPlatform(e.target.value));
          }}
          size='small'
          value={reportingPlatform}
        >
          <Space direction='horizontal'>
            <Radio
              className='text-[12px] font-normal text-blackWhite'
              defaultChecked
              value={'mobile'}
            >
              Mobile
            </Radio>
            <Radio
              className='text-[12px] font-normal text-blackWhite'
              value={'web'}
            >
              Web
            </Radio>
          </Space>
        </Radio.Group>
        {validationErrors?.platform && (
          <span className='text-[#F00] text-[10px] font-light  mt-2 block'>
            {validationErrors?.platform?.toString()}
          </span>
        )}
        <span className='text-[#4F4F4F] dark:text-[#a2a0a0] text-[8px] font-light mt-[9px] block'>
          Platform you were used for purchasing
        </span>
      </div>
    </div>
  );
};

export default RmcStep2;

'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import CommonHeader from '@/app/components/headers/common-header';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import MyEarningsToolbar from '@/app/components/my-earnings/my-earnings-toolbar';
import RedeemHistoryCont from '@/app/components/my-earnings/redeem-history-cont';
import { useAppDispatch } from '@/redux/hooks';
import { setTitle } from '@/redux/slices/earnings-toolbar-slice';
import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
// import NoData from '@/app/components/no-data'; // Uncomment when needed

const IndexClientsRedeemHistory = () => {
  const dispatch = useAppDispatch();
  useEffect(() => {
    dispatch(setTitle('Redeem History'));
  }, [dispatch]);

  return (
    <>
      <CommonHeader
        headline='My Earnings'
        subHeading={<span>Redeem History</span>}
      />

      <motion.section
        animate={{ opacity: 1 }}
        className='header-container'
        initial={{ opacity: 0 }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='sticky top-[104px] z-[30]'
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/my-profile' },
              { title: 'Payment History', link: '/' },
            ]}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='w-full flex z-[0] relative'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <MyEarningsSidenav activeNavId={11} />
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className='bg-[#E0E0E0] dark:bg-[#1F222A] w-full ml-[2px] pb-[80px]'
            initial={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <MyEarningsToolbar onApply={() => {}} onClear={() => {}} />
            </motion.div>

            <motion.div
              animate={{ opacity: 1 }}
              className='pt-[12px] px-[6px] lg:px-[15px] xl:px-[30px] flex flex-col gap-y-[10px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              {/* Add empty state handling */}
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((item, index) => (
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  initial={{ opacity: 0, y: 20 }}
                  key={index}
                  transition={{ duration: 0.3, delay: 0.6 + index * 0.05 }}
                >
                  <RedeemHistoryCont
                    status={index === 1 ? 'Failed' : 'Success'}
                  />
                </motion.div>
              ))}

              {/* Empty state handling - commented out for now */}
              {/* Uncomment and use a state variable to conditionally show this
              {isEmpty && (
                <motion.div
                  className='mt-10'
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <NoData imgClass='!w-[200px] !h-[200px] lg:!w-[300px] lg:!h-[300px]' />
                  <motion.p
                    className='text-center text-blackWhite mt-4 text-sm'
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    You don't have any redeem history yet.
                  </motion.p>
                </motion.div>
              )} */}
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default IndexClientsRedeemHistory;

import React from 'react';
import IndexClientsClickHistory from './index-clients';
import { CustomSearchParamsTypes } from '@/types/global-types';
import { cookies } from 'next/headers';
import { BASE_URL } from '@/config';
import { ClicksResponse } from '@/services/api/data-contracts';
import fetchWrapper from '@/utils/fetch-wrapper';

// FIXME Take the data from the response data
export interface ClickedStores {
  uid: number;
  name: string;
  logo: string;
  clicks: number;
}

async function getClicks(searchParams: CustomSearchParamsTypes) {
  const {
    searchParam = '',
    sortType = 'newest',
    stores = '',
    status = '',
    page = '1',
    pageSize = '15',
    startDate = '',
    endDate = '',
  } = searchParams;

  const queryParams = Object.entries({
    searchParam,
    sortType,
    stores,
    status,
    page,
    pageSize,
    startDate,
    endDate,
  })
    .filter(([_, value]) => value) //eslint-disable-line
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return await fetchWrapper<ClicksResponse>(
    `${BASE_URL}/click?${queryParams}`,
    {
      token: token?.value,
      suppressToast: true,
    }
  );
}

async function getClickedStores() {
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return await fetchWrapper<ClickedStores[]>(
    `${BASE_URL}/click/clicked_stores`,
    {
      token: token?.value,
    }
  );
}

const Page = async ({
  searchParams,
}: {
  searchParams: CustomSearchParamsTypes;
}) => {
  let resData: ClicksResponse;
  //TO DO - add types for clickedStores later
  let clickedStores: ClickedStores[];
  try {
    resData = await getClicks(searchParams);
    clickedStores = await getClickedStores();
  } catch (err: any) {
    console.log({ err });
    return (
      <div className="error-container">
        <h1>Error loading page</h1>
        <p>Please try again later.</p>
      </div>
    );
  }

  return (
    <IndexClientsClickHistory clickedStores={clickedStores} data={resData} />
  );
};

export default Page;
export const dynamic = 'force-dynamic';

import React from 'react';
import IndexClientsProfileSetting from './index-clients';
import fetchWrapper from '@/utils/fetch-wrapper';
import { PersonalInterestTypes } from '@/services/api/data-contracts';
import { BASE_URL } from '@/config';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';

async function getUserPersonalInterest() {
  return fetchWrapper<PersonalInterestTypes[]>(
    `${BASE_URL}/users/get-personal-interest`
  );
}
const Page = async () => {
  const accessToken = getCookie('accessToken', { cookies }) as string;
  let personalInterest: PersonalInterestTypes[] = [];
  //only invoke get personal interest if user is login
  if (accessToken) {
    try {
      personalInterest = await getUserPersonalInterest();
    } catch (error) {
      console.log({ error });
    }
  }
  return <IndexClientsProfileSetting personalInterest={personalInterest} />;
};

export default Page;

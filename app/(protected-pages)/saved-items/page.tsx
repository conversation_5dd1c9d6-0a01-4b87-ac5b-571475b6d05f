import { cookies } from 'next/headers';
import { BASE_URL } from '@/config';
import IndexClientsSavedItems from './index-clients';
import { CustomSearchParamsTypes } from '@/types/global-types';
import fetchWrapper from '@/utils/fetch-wrapper';
import {
  GetAllStoresResponse,
  GetGiftCardListResponse,
  SavedCouponsResponse,
  SavedDealsResponse,
} from '@/services/api/data-contracts';

async function getSavedIDeals(searchParams: CustomSearchParamsTypes) {
  const {
    searchParam = '',
    sortType = 'newest',
    subCategories = '',
    userType = 'both',
    page = '1',
    pageSize = '15',
  } = searchParams;
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  const res = await fetchWrapper<SavedDealsResponse>(
    `${BASE_URL}/saved-items/deals?searchParam=${searchParam}&sortType=${sortType}&userType=${userType}&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}`,
    {
      token: token?.value,
      cache: 'no-store',
    }
  );
  return res;
}

async function getSavedCoupons(searchParams: CustomSearchParamsTypes) {
  const {
    searchParam = '',
    sortType = 'newest',
    subCategories = '',
    userType = 'both',
    page = '1',
    pageSize = '15',
  } = searchParams;

  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');

  const res = await fetchWrapper<SavedCouponsResponse>(
    `${BASE_URL}/saved-items/coupons?searchParam=${searchParam}&sortType=${sortType}&userType=${userType}&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}`,
    {
      token: token?.value,
      cache: 'no-store',
    }
  );
  return res;
}

async function getSavedStores(searchParams: CustomSearchParamsTypes) {
  const {
    searchParam = '',
    sortType = 'newest',
    subCategories = '',
    page = '1',
    pageSize = '15',
    minPercent = 0,
    maxPercent = 100,
  } = searchParams;

  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');

  const res = await fetchWrapper<GetAllStoresResponse>(
    `${BASE_URL}/saved-items/stores?searchParam=${searchParam}&sortType=${sortType}&subCategories=${subCategories}&minPercent=${minPercent}&maxPercent=${maxPercent}&page=${page}&pageSize=${pageSize}`,
    {
      token: token?.value,
      cache: 'no-store',
    }
  );
  return res;
}

async function getSavedGiftCards(searchParams: CustomSearchParamsTypes) {
  const {
    searchParam = '',
    sortType = 'newest',
    subCategories = '',
    page = '1',
    pageSize = '15',
  } = searchParams;

  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  const res = await fetchWrapper<GetGiftCardListResponse>(
    `${BASE_URL}/saved-items/gift-cards?searchParam=${searchParam}&sortType=${sortType}&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}`,
    {
      token: token?.value,
      cache: 'no-store',
    }
  );

  return res;
}

const Page = async ({
  searchParams,
}: {
  searchParams: CustomSearchParamsTypes;
}) => {
  let data: any;
  try {
    data = await Promise.allSettled([
      getSavedIDeals(searchParams),
      getSavedCoupons(searchParams),
      getSavedStores(searchParams),
      getSavedGiftCards(searchParams),
    ]);
  } catch (err: any) {
    console.error({ err });
  }

  const [savedDeals, savedCoupons, savedStores, savedGiftCards] = data;

  return (
    <IndexClientsSavedItems
      savedCoupons={savedCoupons.value}
      savedDeals={savedDeals.value}
      savedGiftCards={savedGiftCards.value}
      savedStores={savedStores.value}
    />
  );
};

export default Page;
export const dynamic = 'force-dynamic';

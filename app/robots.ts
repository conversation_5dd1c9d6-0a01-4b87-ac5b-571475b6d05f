import { MetadataRoute } from 'next';

export default function robots(): MetadataRoute.Robots {
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.indiancashback.com';

  return {
    rules: [ // You can have multiple rule objects for different user-agents
      {
        userAgent: '*', // Applies to all crawlers
        allow: '/',     // Allow access to the entire site
        disallow: ['/private/', '/admin/'], // Disallow specific paths
      }
    ],
    sitemap: `${siteUrl}/sitemap.xml`,
    // host: siteUrl, // Optional: Specifies the preferred domain
  };
}
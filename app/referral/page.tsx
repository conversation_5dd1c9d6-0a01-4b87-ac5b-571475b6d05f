import React from 'react';

// Section components (to be implemented in app/components/referral/)
import IndexClientsReferEarn from './index-clients';
import type { GetReferralLeaderboardResponse } from '@/services/api/data-contracts';
import fetchWrapper from '@/utils/fetch-wrapper';
import { BASE_URL } from '@/config';

async function getReferralLeaderBoardData({
  timeFrame = 'all',
}: {
  timeFrame: 'weekly' | 'monthly' | 'all';
}) {
  return await fetchWrapper<GetReferralLeaderboardResponse[]>(
    `${BASE_URL}/campaign/referral-earnings-leaderboard?timeFrame=${timeFrame}`,
    {
      cache: 'no-store', // Disable caching for real-time data
      next: { revalidate: 0 }, // Force revalidation on every request
    }
  );
}

async function getAllReferralLeaderBoardData() {
  try {
    const [allTimeData, weeklyData, monthlyData] = await Promise.all([
      getReferralLeaderBoardData({ timeFrame: 'all' }),
      getReferralLeaderBoardData({ timeFrame: 'weekly' }),
      getReferralLeaderBoardData({ timeFrame: 'monthly' }),
    ]);

    return {
      all: allTimeData,
      weekly: weeklyData,
      monthly: monthlyData,
    };
  } catch (error) {
    console.error('Error fetching referral leaderboard data:', error);
    return {
      all: [],
      weekly: [],
      monthly: [],
    };
  }
}

const ReferralPage = async () => {
  const data = await getAllReferralLeaderBoardData();
  return <IndexClientsReferEarn referralData={data} />;
};

export default ReferralPage;

<!--
    Copyright 2019 Google Inc. All Rights Reserved.

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<adaptive-icon xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt">
    <background>
        <layer-list>
            <item android:drawable="@android:color/white" />
            <!-- 
                The paddings below give the icons a similar proportion as the
                icon generated by WebAPKs.
            -->
            <item android:drawable="@mipmap/ic_maskable"
                android:top="8.5dp"
                android:right="8.5dp"
                android:left="8.5dp"
                android:bottom="8.5dp" />
        </layer-list>
    </background>
    <foreground android:drawable="@android:color/transparent" />
</adaptive-icon>

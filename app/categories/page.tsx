import React from 'react';
import IndexClientsCategories from './index-clients';
import fetchWrapper from '@/utils/fetch-wrapper';
import {
  CategoryResponse,
  SubCategoriesByCategoryResponse,
} from '@/services/api/data-contracts';
import { BASE_URL } from '@/config';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Shopping Categories - IndianCashback',
  description:
    'Explore all shopping categories on IndianCashback and find the best cashback deals for fashion, electronics, travel, food, beauty, and more. Filter by category to discover exclusive offers tailored to your shopping preferences.',
  alternates: {
    canonical: 'https://www.indiancashback.com/categories',
  },
  openGraph: {
    url: 'https://www.indiancashback.com/categories',
    title: 'Shopping Categories - IndianCashback',
    description:
      'Explore all shopping categories on IndianCashback and find the best cashback deals for fashion, electronics, travel, food, beauty, and more. Filter by category to discover exclusive offers tailored to your shopping preferences.',
  },
};

async function getCategoriesData() {
  const categories = await fetchWrapper<CategoryResponse[]>(
    `${BASE_URL}/context/category?trending=false`
  );
  // Get the first category
  const firstCategory = categories[0];
  // Fetch the subcategories for the first category
  const subCategories = await fetchWrapper<SubCategoriesByCategoryResponse>(
    `${BASE_URL}/context/category/sub-category${firstCategory.id}`
  );
  return { categories, subCategories };
}

export default async function Page() {
  // const { activeCategory, categoriesList } = useAppSelector((state) => state.categoriesList);
  // const dispatch = useAppDispatch();
  let data: any;
  try {
    data = await Promise.allSettled([getCategoriesData()]);
  } catch (err: any) {
    console.log({ err });
  }
  const [categoriesData] = data;
  return (
    <IndexClientsCategories
      data={categoriesData.value}
      promiseStatus={categoriesData.status}
    />
  );
}

// ISR Configuration
// Revalidate every 6 hours (21600 seconds)
// Categories data is relatively stable but may have new categories added occasionally
export const revalidate = 21600;

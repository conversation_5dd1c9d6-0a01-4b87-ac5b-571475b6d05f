'use client';
// import CommonHeader from '../components/headers/common-header';
import SearchInput from '../components/atoms/search-input';
import CategoryCard from '../components/cards/category-card';
import Image from 'next/image';
import clsx from 'clsx';
import type {
  CategoryResponse,
  SubCategoriesByCategoryResponse,
} from '@/services/api/data-contracts';
import type { PromiseStatus } from '@/types/global-types';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setActiveCategory } from '@/redux/slices/categories-list-slice';
import { useRouter } from 'next/navigation';
import StoreByCBCard from '../components/landing/stores-cb-percentage/store-by-cb-card';
import CommonHeader from '../components/headers/common-header';
import { useState } from 'react';
import { LoadingGif } from '../components/misc/loading-components';

const IndexClientsCategories = ({
  data,
  promiseStatus,
}: {
  data: {
    categories: CategoryResponse[];
    subCategories: SubCategoriesByCategoryResponse;
  };
  promiseStatus: PromiseStatus;
}) => {
  const [searchValue, setSearchValue] = useState('');
  const { activeCategory, subCategoriesList, isActiveCategoryLoading } =
    useAppSelector((state) => state.categoriesList);
  const dispatch = useAppDispatch();
  const router = useRouter();

  if (promiseStatus === 'rejected') {
    return;
  }
  const handleSubCategoryRedirect = (uid: number) => {
    router.push(`/deals-and-coupons?subCategories=${uid}`);
  };

  const subCategories = activeCategory
    ? subCategoriesList[activeCategory]?.subCategories
    : data?.subCategories?.subCategories;
  const stores = activeCategory
    ? subCategoriesList[activeCategory]?.stores
    : data?.subCategories?.stores;

  return (
    <>
      <CommonHeader
        backRoute='/'
        headline='Categories'
        subHeading={
          <>
            <span>Results</span>
            <span className='ml-[3px] text-[7px] sm:text-[8px] font-nexa'>
              ({data?.categories?.length})
            </span>
          </>
        }
      />
      <div className='min-h-[56px] z-[10] flex items-center bg-[#E4E6E8] dark:bg-container lg:h-fit px-[14px] sticky top-[64px] shadow-md'>
        <SearchInput
          onChange={(value) => setSearchValue(value)}
          placeholder='Search Categories...'
          value={searchValue}
        />
      </div>
      <div className='w-full flex'>
        {data && (
          <aside
            className='bg-container shadow-md w-[73px] h-[calc(100vh-120px)]  shrink-0 grow-0 sticky top-[120px] left-0 z-[5] overscroll-contain overflow-auto pb-[9rem] scrollbarNone scroll-smooth'
            style={{ boxShadow: '3px -8px 5px rgb(0 0 0 / 15%)' }}
          >
            {data?.categories?.map((item, index) => (
              <button
                className={clsx(
                  (activeCategory === item.id ||
                    (activeCategory === '' && index === 0)) &&
                    '!bg-[#FFC554]',
                  'h-[74px] w-full bg-container dark:bg-[#3E424C] px-[12px] py-[8px] mb-[2px] flex-center flex-col gap-y-[8px] shadow'
                )}
                key={item?.id}
                onClick={() => dispatch(setActiveCategory(item?.id))}
                type='button'
              >
                <div>
                  <Image
                    alt='icon'
                    className='w-[25px] h-[25px]'
                    height={25}
                    quality={100}
                    src={item.iconUrl}
                    width={25}
                  />
                </div>
                <p
                  className={clsx(
                    (activeCategory === item.id ||
                      (activeCategory === '' && index === 0)) &&
                      '!text-black',
                    'text-[8px] sm:text-[9px] text-center font-semibold text-blackWhite maxLines2'
                  )}
                >
                  {item.name}
                </p>
              </button>
            ))}
          </aside>
        )}
        <div className='bg-container ml-[2px] w-full pb-[27px]'>
          <div>
            <h4 className='font-pat text-xs text-blackWhite font-normal ml-[21px] my-[20px]'>
              Top Sub-categories
            </h4>
            <div className='grid grid-cols-2 min-[400px]:grid-cols-3 md:grid-cols-4 gap-[8px] px-[8px]'>
              {!isActiveCategoryLoading ? (
                subCategories?.map((item) => (
                  <CategoryCard
                    imgUrl={item?.iconUrl}
                    isLiked
                    key={item.uid}
                    onClickCard={() => handleSubCategoryRedirect(item.uid)}
                    text={item?.title}
                  />
                ))
              ) : (
                <LoadingGif />
              )}
            </div>
          </div>
          <div>
            <h4 className='font-pat text-xs text-blackWhite font-normal ml-[21px] my-[20px]'>
              Top Stores
            </h4>
            <div className='grid grid-cols-2 min-[400px]:grid-cols-3 gap-[8px] px-[8px]'>
              {!isActiveCategoryLoading ? (
                stores?.map((item) => (
                  <StoreByCBCard
                    bgColor={item.bgColor}
                    caption={item.caption}
                    className='lg:!w-[146px]'
                    key={item.uid}
                    saved={item.saved}
                    src={item.imageUrl}
                    storeName={item.storeName}
                    uid={item.uid}
                  />
                ))
              ) : (
                <LoadingGif />
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default IndexClientsCategories;

'use client';

import { useEffect, useState, createContext, useContext } from 'react';
import {
  initializeForesight,
  type ForesightConfig,
  isForesightAvailable,
} from '@/utils/foresight-config';

/**
 * ForesightJS context interface
 */
interface ForesightContextType {
  isInitialized: boolean;
  isAvailable: boolean;
  error: string | null;
  reinitialize: (config?: Partial<ForesightConfig>) => void;
}

/**
 * ForesightJS context
 */
const ForesightContext = createContext<ForesightContextType | undefined>(
  undefined
);

/**
 * Props for ForesightProvider
 */
interface ForesightProviderProps {
  children: React.ReactNode;
  config?: Partial<ForesightConfig>;
  enableInDevelopment?: boolean;
  enableInProduction?: boolean;
  onInitialized?: () => void;
  onError?: (error: string) => void;
}

/**
 * ForesightProvider Component
 *
 * Provides ForesightJS initialization and context to the entire application.
 * This component should be placed high in the component tree, typically in
 * the root layout or main provider component.
 *
 * Features:
 * - Automatic ForesightJS initialization
 * - Environment-based configuration
 * - Error handling and recovery
 * - Context for accessing ForesightJS state
 * - Reinitialize capability for dynamic configuration
 *
 * @example
 * // Basic usage in layout.tsx
 * <ForesightProvider>
 *   <App />
 * </ForesightProvider>
 *
 * @example
 * // With custom configuration
 * <ForesightProvider
 *   config={{
 *     trajectoryPredictionTime: 100,
 *     debug: true,
 *   }}
 *   onInitialized={() => console.log('ForesightJS ready')}
 * >
 *   <App />
 * </ForesightProvider>
 */
export const ForesightProvider = ({
  children,
  config,
  enableInDevelopment = true,
  enableInProduction = true,
  onInitialized,
  onError,
}: ForesightProviderProps) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if ForesightJS should be enabled based on environment
  const shouldEnable = () => {
    const isDevelopment = process.env.NODE_ENV === 'development';
    return isDevelopment ? enableInDevelopment : enableInProduction;
  };

  // Initialize ForesightJS
  const initialize = (customConfig?: Partial<ForesightConfig>) => {
    if (typeof window === 'undefined') {
      return; // Skip SSR
    }

    if (!shouldEnable()) {
      console.log('ForesightJS disabled for current environment');
      return;
    }

    try {
      setError(null);
      initializeForesight({ ...config, ...customConfig });
      setIsInitialized(true);

      if (onInitialized) {
        onInitialized();
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      setIsInitialized(false);

      if (onError) {
        onError(errorMessage);
      }

      console.error('Failed to initialize ForesightJS:', err);
    }
  };

  // Reinitialize function for dynamic configuration changes
  const reinitialize = (newConfig?: Partial<ForesightConfig>) => {
    if (typeof window !== 'undefined') {
      // Reset initialization flag
      (window as any).__foresightInitialized = false;
    }
    setIsInitialized(false);
    initialize(newConfig);
  };

  // Initialize on mount
  useEffect(() => {
    initialize();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once

  // Context value
  const contextValue: ForesightContextType = {
    isInitialized,
    isAvailable: isForesightAvailable(),
    error,
    reinitialize,
  };

  return (
    <ForesightContext.Provider value={contextValue}>
      {children}
    </ForesightContext.Provider>
  );
};

/**
 * Hook to access ForesightJS context
 *
 * @example
 * const { isInitialized, isAvailable, error } = useForesightContext();
 *
 * if (!isAvailable) {
 *   return <div>ForesightJS not available</div>;
 * }
 */
export const useForesightContext = (): ForesightContextType => {
  const context = useContext(ForesightContext);

  if (context === undefined) {
    throw new Error(
      'useForesightContext must be used within a ForesightProvider'
    );
  }

  return context;
};

/**
 * HOC for components that require ForesightJS
 *
 * @example
 * const MyComponent = withForesight(() => {
 *   // Component that uses ForesightJS
 *   return <div>ForesightJS is available</div>;
 * });
 */
export const withForesight = <P extends object>(
  Component: React.ComponentType<P>
) => {
  const WrappedComponent = (props: P) => {
    const { isAvailable, error } = useForesightContext();

    if (error) {
      console.warn('ForesightJS error:', error);
      // Render component anyway, it should handle the fallback
    }

    if (!isAvailable) {
      console.warn(
        'ForesightJS not available, component may fall back to standard behavior'
      );
    }

    return <Component {...props} />;
  };

  WrappedComponent.displayName = `withForesight(${
    Component.displayName || Component.name
  })`;

  return WrappedComponent;
};

/**
 * Component for debugging ForesightJS state
 * Only renders in development mode
 */
export const ForesightDebugInfo = () => {
  const { isInitialized, isAvailable, error } = useForesightContext();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className='fixed bottom-4 right-4 bg-black bg-opacity-75 text-white p-2 rounded text-xs z-50'>
      <div>ForesightJS Status:</div>
      <div>Initialized: {isInitialized ? '✅' : '❌'}</div>
      <div>Available: {isAvailable ? '✅' : '❌'}</div>
      {error && <div className='text-red-300'>Error: {error}</div>}
    </div>
  );
};

export default ForesightProvider;

import React, { useState } from 'react';
import SlidingButton from '../atoms/sliding-button';
import InputFieldWithIcon, { TextAreaWithIcon } from '../atoms/form-inputs';
import { FieldErrors, FieldValues, UseFormRegister } from 'react-hook-form';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import MailSVG from '../svg/mail';
import ThemeButton from '../atoms/theme-btn';

const SendGiftcardForm = ({
  register,
  errors,
}: {
  register: UseFormRegister<FieldValues>;
  errors: FieldErrors<FieldValues>;
}) => {
  const { userDetails, isUserLogin } = useAppSelector((state) => state.auth);
  const [isSelf, setIsSelf] = useState(true);
  const dispatch = useAppDispatch();
  return (
    <div className='min-h-[440px] bg-[#EEE] dark:bg-[#35383E] rounded-[10px] flex items-center justify-start flex-col pb-[20px] lg:pb-[24px] mx-[6px] lg:mx-0'>
      <div className='flex items-center gap-x-[20px] pt-[14px] pb-[22px] lg:pt-[21px]'>
        <span className='font-pat text-blackWhite text-sm'>Send To</span>
        <SlidingButton
          buttonDetails={[
            { title: 'Friend', value: 'Friend' },
            { title: 'Self', value: 'Self' },
          ]}
          defaultSelectedBtn={isSelf ? 2 : 1}
          onChange={(value) => setIsSelf(value.target.value === 'Self')}
          uniqueId='giftcard'
        />
      </div>
      {isSelf ? (
        isUserLogin ? (
          <div className='flex flex-col gap-y-[14px] w-full items-center px-[8px] font-pop text-[12px] m-auto pb-8'>
            <div className='flex justify-center gap-x-2 items-center bg-[#D7D7D7] dark:bg-[#3e424c] rounded-[10px] px-2'>
              <MailSVG
                className='w-[15px] h-[13px]'
                fill='currentColor'
                stroke='currentColor'
              />
              {userDetails.email}
            </div>
            <div className='font-medium max-w-[330px] text-primary text-center'>
              You will get the giftcard details in your registered email and
              mobile number.
            </div>
            <div className='text-[8px] text-center max-w-[330px]'>
              If you want to send to another email or number, then switch to
              "Friend" tab and enter details.
            </div>
          </div>
        ) : (
          <div className='pb-8 m-auto'>
            <ThemeButton
              className='border-[1px] m-auto border-[#4D3EC1] !text-[#4D3EC1] dark:!text-white !bg-transparent dark:!bg-primary !w-[60px] !h-[30px]'
              onClick={() => dispatch(setLoginModalOpen(true))}
              text='Login'
            />
            <div className='font-medium text-[12px] max-w-[330px] my-6 text-primary text-center'>
              Login to send giftcard to yourself.
            </div>
          </div>
        )
      ) : (
        <div className='flex flex-col gap-y-[14px] w-full items-center px-[8px]'>
          <InputFieldWithIcon
            iconUrl='/svg/forms/user.svg'
            name='fullName'
            placeholder='Enter Recipient Full Name'
            register={register}
            validationSchema={{
              required: 'Full Name is required',
              maxLength: {
                value: 30,
                message: 'Full Name must be less then 30 characters.',
              },
              minLength: {
                value: 3,
                message: 'Full Name must be greater then 3 characters.',
              },
            }}
          />
          {errors && (
            <span className='text-[#F00] text-[10px] font-light'>
              {errors.fullName?.message?.toString()}
            </span>
          )}
          <InputFieldWithIcon
            iconUrl='/svg/forms/phone.svg'
            name='phone'
            placeholder='Enter Recipient Phone Number'
            register={register}
            validationSchema={{
              required: 'Phone Number is required',
              pattern: {
                value: /^[0-9]{10}$/g,
                message: 'Please enter valid phone number.',
              },
            }}
          />
          {errors && (
            <span className='text-[#F00] text-[10px] font-light'>
              {errors.phone?.message?.toString()}
            </span>
          )}
          <InputFieldWithIcon
            iconUrl='/svg/forms/email.svg'
            name='email'
            placeholder='Enter RecipientMail Address'
            register={register}
            validationSchema={{
              required: 'Email is required',
              pattern: {
                value: /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/g,
                message: 'Please enter valid email.',
              },
            }}
          />
          {errors && (
            <span className='text-[#F00] text-[10px] font-light'>
              {errors.email?.message?.toString()}
            </span>
          )}
          <TextAreaWithIcon
            iconUrl='/svg/forms/message.svg'
            name='note'
            placeholder='Type a Message'
            register={register}
            validationSchema={{
              // It needs to be a string with a length of at least 30 characters
              pattern: {
                value: /^.{30,}$/g,
                message: 'Message must be at least 30 characters long.',
              },
            }}
          />
          {errors && (
            <span className='text-[#F00] text-[10px] font-light'>
              {errors.note?.message?.toString()}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default SendGiftcardForm;

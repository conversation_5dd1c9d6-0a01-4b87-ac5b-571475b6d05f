import React, { useState } from 'react';
import OTPInput from 'react-otp-input';
import ThemeButton from '../atoms/theme-btn';
import type { LoginDto, VerifyUserDto } from '@/services/api/data-contracts';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  setIsUserLogin,
  setLoading,
  setLoginModalOpen,
  setLoginSignupScreen,
} from '@/redux/slices/auth-slice';
import { toast } from 'react-toastify';
import RightArrow from '../svg/right-arrow';
import { LoadingGif } from '../misc/loading-components';
import clsx from 'clsx';
import fetchWrapper from '@/utils/fetch-wrapper';
import { useRouter, usePathname } from 'next/navigation';

const VerifyAuthOtpForm = () => {
  const [otp, setOtp] = useState<number>();
  const [error, setError] = useState('');
  const { screen, isLoading, userDetails } = useAppSelector(
    (state) => state.auth
  );
  const [loadingResendOtp, setLoadingResendOtp] = useState(false);
  const dispatch = useAppDispatch();
  const router = useRouter();
  const pathname = usePathname();
  const verifyAuthOTP = async () => {
    if (!otp) {
      setError('Please enter the OTP to verify.');
      return;
    }
    setError('');

    const body: VerifyUserDto = {
      otp: otp,
      email: userDetails.email,
      mobile: userDetails.mobile,
    };

    try {
      dispatch(setLoading(true));

      // Make sure to use credentials: 'include' to allow cookies to be set
      await fetchWrapper('/api/proxy/auth/verify-user', {
        method: 'POST',
        body: JSON.stringify(body),
        // Explicitly set credentials to include to ensure cookies are sent and received
        credentials: 'include',
      });

      // Check if the cookie was set
      const hasCookie = document.cookie.includes('accessToken');
      if (!hasCookie) {
        console.warn(
          'Authentication cookie not set. This may cause issues with protected routes.'
        );
      }

      toast.success('Successfully Login.');
      dispatch(setIsUserLogin(true));
      dispatch(setLoading(false));
      dispatch(setLoginSignupScreen({ for: 'login', step: 1 }));
      dispatch(setLoginModalOpen(false));

      const redirectUrlValue = localStorage.getItem('redirectTo');
      // Get the redirect URL from search params or use current path
      const redirectUrl = redirectUrlValue || pathname;

      // If we're on a protected page or have a specific redirect URL, navigate there
      if (redirectUrl && redirectUrl !== '/') {
        router.push(redirectUrl);
      } else {
        // Default redirect to home page
        router.push('/');
      }

      router.refresh();
    } catch (error: unknown) {
      dispatch(setLoading(false));
      if (error instanceof Error) {
        toast.error(error.message);
      } else {
        toast.error('An error occurred during verification');
      }
    }
  };

  const resendAuthOTP = async () => {
    let body: LoginDto;
    const { email, mobile } = userDetails;
    if (email) {
      body = { email };
    } else if (mobile) {
      body = { mobile };
    } else {
      if (screen.for === 'login') {
        dispatch(setLoginSignupScreen({ for: 'login', step: 1 }));
      } else {
        dispatch(setLoginSignupScreen({ for: 'login', step: 1 }));
      }
      return;
    }

    setLoadingResendOtp(true);
    await fetchWrapper('/api/proxy/auth/resend-otp', {
      method: 'POST',
      body: JSON.stringify(body),
    });
    toast.success('OTP has been resend to your mail.');

    setLoadingResendOtp(false);
  };

  return (
    <>
      <div className='bg-[#F7E5C4] text-black text-[10px] font-medium p-[16px] rounded-[5px] mt-[20px] lg:mt-[40px]'>
        We have sent an OTP to{' '}
        <span className='font-bold'>
          {userDetails.mobile || userDetails.email}
        </span>
        , Enter the OTP and complete your onboarding
      </div>

      <div className='px-[30px] lg:px-[20px] mt-[20px] lg:mt-[25px]'>
        <label className='text-[10px] font-medium text-[#767676] dark:text-[#929090]'>
          Enter OTP <span className='text-[#F00] sup'>*</span>
        </label>
        <OTPInput
          containerStyle='w-full h-[40px] flex items-center justify-between lg:mt-[5px]'
          inputStyle='!w-[37px] h-[35px]  lg:!w-[51px] lg:h-[48px] outline-none rounded-[5px] border border-primary'
          inputType='number'
          numInputs={4}
          onChange={(value) => setOtp(Number(value))}
          renderInput={(props) => <input {...props} />}
          shouldAutoFocus={true}
          value={otp?.toString()}
        />
        {error && (
          <span className='text-[#F00] text-[10px] font-light'>{error}</span>
        )}
        {isLoading ? (
          <LoadingGif className='mt-[30px]' />
        ) : (
          <ThemeButton
            className='!w-[90px] lg:w-[100px] mx-auto mt-[30px]'
            isDisabled={loadingResendOtp}
            onClick={() => verifyAuthOTP()}
            text='VERIFY'
          />
        )}
        <div className='flex justify-between mt-[30px]'>
          <button
            className={clsx(
              (isLoading || loadingResendOtp) && 'opacity-40',
              'text-[12px] font-semibold flex items-center gap-x-[5px] text-blackWhite'
            )}
            disabled={isLoading || loadingResendOtp}
            onClick={() => {
              if (screen.for === 'login') {
                dispatch(setLoginSignupScreen({ for: 'login', step: 1 }));
              } else {
                dispatch(setLoginSignupScreen({ for: 'signup', step: 1 }));
              }
            }}
          >
            <RightArrow className='rotate-[180deg] text-blackWhite w-[16px]' />
            {screen.for === 'login'
              ? userDetails?.email
                ? 'Edit Email'
                : 'Edit Number'
              : 'Back'}
          </button>
          {loadingResendOtp ? (
            <LoadingGif className='!h-[20px] mx-0' />
          ) : (
            <button
              className={clsx(
                isLoading && 'opacity-40',
                'text-[12px] font-medium text-blackWhite'
              )}
              disabled={isLoading}
              onClick={resendAuthOTP}
            >
              Resend OTP
            </button>
          )}
        </div>
      </div>
    </>
  );
};

export default VerifyAuthOtpForm;

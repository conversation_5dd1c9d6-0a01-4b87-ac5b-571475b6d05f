'use client';
import { useAppSelector } from '@/redux/hooks';
import React, { useState } from 'react';
import { useAppDispatch } from '../../../redux/hooks';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { InputFieldNormal } from '../atoms/form-inputs';
import ThemeButton from '../atoms/theme-btn';
import {
  setLoading,
  setLoginSignupScreen,
  setUserDetails,
} from '@/redux/slices/auth-slice';
import type { CreateUserDto } from '@/services/api/data-contracts';
import { useSearchParams } from 'next/navigation';
import VerifyAuthOtpForm from './verify-auth-otp-form';
import { LoadingGif } from '../misc/loading-components';
import fetchWrapper from '@/utils/fetch-wrapper';
import Link from 'next/link';
import { motion } from 'framer-motion';

const SignupForm = () => {
  const { screen, isLoading } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const [termsError, setTermsError] = useState('');
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<SignupFormData>({
    mode: 'onChange',
  });

  // Watch all required fields to enable/disable the Next button
  const name = watch('name');
  const email = watch('email');
  const agreedToTerms = watch('agreedToTerms');

  // Check if all required fields are filled and valid
  const isFormValid =
    !!name && !!email && !!agreedToTerms && !errors.name && !errors.email;

  interface SignupFormData {
    name: string;
    email: string;
    agreedToTerms: boolean;
    referralCode?: string;
  }

  const signupHandler: SubmitHandler<SignupFormData> = async (data) => {
    // Check if user agreed to terms and conditions
    if (!data.agreedToTerms) {
      setTermsError(
        'You must agree to the Terms & Conditions and Privacy Policy'
      );
      return;
    }

    setTermsError('');

    let referralCode = searchParams.get('r');
    if (referralCode) {
      //if referralCode exist in url then take from them and set into local storage
      localStorage.setItem('referralCode', `${referralCode}`);
    } else {
      // if referralCode doesn't exist in url, then find it from local storage if any.
      referralCode = localStorage.getItem('referralCode');
    }
    // Create a CreateUserDto object from the form data
    const body: CreateUserDto = {
      name: data.name,
      email: data.email,
    };
    if (referralCode) {
      body.referralCode = referralCode;
    }
    try {
      dispatch(setLoading(true));
      await fetchWrapper('/api/proxy/auth/register', {
        method: 'POST',
        body: JSON.stringify(body),
      });
      dispatch(setLoading(false));
      dispatch(setUserDetails({ email: data?.email }));
      dispatch(setLoginSignupScreen({ for: 'signup', step: 2 }));
    } catch (error: unknown) {
      dispatch(setLoading(false));
    }
  };
  return (
    <>
      {screen.for === 'signup' && (
        <div className='max-w-[332px] mx-auto px-[8px]'>
          {screen.step === 1 ? (
            <>
              <div className='mt-[25px] lg:mt-[30px]'>
                <label
                  className='text-[10px] font-medium text-[#767676] dark:text-[#929090] ml-[10px]'
                  htmlFor='name'
                >
                  Full Name <span className='text-[#F00] sup'>*</span>
                </label>
                <InputFieldNormal
                  name='name'
                  placeholder='Enter Your Full Name'
                  register={register}
                  validationSchema={{
                    required: 'Full Name is required',
                    maxLength: {
                      value: 30,
                      message: 'Full Name must be less then 30 characters.',
                    },
                    minLength: {
                      value: 3,
                      message: 'Full Name must be greater then 3 characters.',
                    },
                  }}
                />
                {errors.name && (
                  <span className='text-[#F00] text-[10px] font-light'>
                    {errors.name.message?.toString()}
                  </span>
                )}
              </div>
              <div className='mt-[15px]'>
                <label
                  className='text-[10px] font-medium text-[#767676] dark:text-[#929090] ml-[10px]'
                  htmlFor='email'
                >
                  Email <span className='text-[#F00] sup'>*</span>
                </label>
                <InputFieldNormal
                  name='email'
                  placeholder='Enter Your Email'
                  register={register}
                  validationSchema={{
                    required: 'Email is required',
                    pattern: {
                      value: /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/g,
                      message: 'Please enter valid email.',
                    },
                  }}
                />
                {errors.email && (
                  <span className='text-[#F00] text-[10px] font-light'>
                    {errors.email.message?.toString()}
                  </span>
                )}
                {/* {serverErrMsg && (
                  <span className='text-[#F00] text-[10px] font-light'>
                    {serverErrMsg}
                  </span>
                )} */}
              </div>
              <div className='mt-[15px]'>
                <div className='flex items-start mt-2'>
                  <div className='flex items-start'>
                    <input
                      className='mt-1 h-3 w-3 cursor-pointer accent-primary'
                      id='agreedToTerms'
                      type='checkbox'
                      {...register('agreedToTerms')}
                    />
                  </div>
                  <label
                    className='ml-2 mt-[2px] text-[10px] font-medium text-[#767676] dark:text-[#929090] cursor-pointer'
                    htmlFor='agreedToTerms'
                  >
                    I agree to the{' '}
                    <Link
                      className='text-primary hover:underline'
                      href='/terms-and-conditions'
                      target='_blank'
                    >
                      Terms & Conditions
                    </Link>{' '}
                    and{' '}
                    <Link
                      className='text-primary hover:underline'
                      href='/privacy-policies'
                      target='_blank'
                    >
                      Privacy Policy
                    </Link>
                  </label>
                </div>
                {termsError && (
                  <motion.span
                    animate={{ opacity: 1, y: 0 }}
                    className='text-[#F00] text-[10px] font-light block mt-1'
                    initial={{ opacity: 0, y: -5 }}
                    transition={{ duration: 0.3 }}
                  >
                    {termsError}
                  </motion.span>
                )}
              </div>
              <div className='relative flex-center mt-[20px] lg:mt-[30px]'>
                {isLoading ? (
                  <LoadingGif />
                ) : (
                  <ThemeButton
                    className='!w-[90px] lg:!w-[100px] !text-[10px] lg:text-xs uppercase'
                    isDisabled={isLoading || !isFormValid}
                    onClick={handleSubmit(signupHandler)}
                    text='Next'
                  />
                )}
              </div>
            </>
          ) : screen.step === 2 ? (
            <VerifyAuthOtpForm />
          ) : (
            <></>
          )}
        </div>
      )}
    </>
  );
};

export default SignupForm;

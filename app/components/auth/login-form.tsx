import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import React from 'react';
import { InputFieldNormal } from '../atoms/form-inputs';
import ThemeButton from '../atoms/theme-btn';
import { type FieldValues, useForm } from 'react-hook-form';
import {
  setLoading,
  setLoginSignupScreen,
  setUserDetails,
} from '@/redux/slices/auth-slice';
import type { LoginDto } from '@/services/api/data-contracts';
import VerifyAuthOtpForm from './verify-auth-otp-form';
import { LoadingGif } from '../misc/loading-components';
import fetchWrapper from '@/utils/fetch-wrapper';
import GoogleLoginButton from './google-login-button';
import { motion } from 'framer-motion';

const LoginForm = () => {
  const { screen, isLoading } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm();

  const loginHandler = async ({ emailMobile }: FieldValues) => {
    let email = '';
    let phone = '';
    let body: LoginDto;
    if (/^[0-9]{10}$/g.test(emailMobile)) {
      phone = emailMobile;
      body = { mobile: phone };
    } else {
      email = emailMobile;
      body = { email };
    }
    dispatch(setLoading(true));
    try {
      await fetchWrapper('/api/proxy/auth/login', {
        method: 'POST',
        body: JSON.stringify(body),
      });
      dispatch(setLoading(false));
      dispatch(setUserDetails({ email, mobile: phone }));
      dispatch(setLoginSignupScreen({ for: 'login', step: 2 }));
    } catch (error) {
      dispatch(setLoading(false));
    }
  };

  return (
    <>
      {screen.for === 'login' && (
        <div className='max-w-[332px] mx-auto px-[8px]'>
          {screen.step === 1 ? (
            <>
              <div className='mt-[50px] lg:mt-[75px]'>
                <span className='text-[10px] font-medium text-[#767676] dark:text-[#929090] ml-[10px]'>
                  Enter E-Mail/Mobile
                  <span className='text-[#F00] sup'>*</span>
                </span>
                <InputFieldNormal
                  name='emailMobile'
                  placeholder='Enter Your Email/Mobile'
                  register={register}
                  validationSchema={{
                    required: 'Email/Mobile is required',
                    validate: (fieldValue) => {
                      if (
                        !/^[0-9]{10}$/g.test(fieldValue) &&
                        !/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/g.test(fieldValue)
                      ) {
                        return 'Please enter valid email/mobile.';
                      }
                      return true;
                    },
                  }}
                />

                {errors && (
                  <span className='text-[#F00] text-[10px] font-light'>
                    {errors.emailMobile?.message?.toString()}
                  </span>
                )}
                {/* {serverErrMsg && (
                  <span className='text-[#F00] text-[10px] font-light'>
                    {serverErrMsg}
                  </span>
                )} */}
              </div>
              {isLoading ? (
                <LoadingGif className='mt-[30px] lg:mt-[40px]' />
              ) : (
                <>
                  <ThemeButton
                    className='!w-[90px] lg:!w-[100px] !text-[10px] mx-auto lg:text-xs uppercase mt-[30px] lg:mt-[40px]'
                    isDisabled={isLoading}
                    onClick={handleSubmit(loginHandler)}
                    text='Login'
                  />

                  {/* Google Login */}
                  <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    className='mt-6 flex flex-col items-center'
                    initial={{ opacity: 0, y: 10 }}
                    transition={{ delay: 0.2 }}
                  >
                    <div className='flex items-center w-full mb-4'>
                      <div className='flex-grow h-px bg-gray-300 dark:bg-gray-600' />
                      <span className='mx-2 text-xs text-gray-500 dark:text-gray-400'>
                        or
                      </span>
                      <div className='flex-grow h-px bg-gray-300 dark:bg-gray-600' />
                    </div>
                    <GoogleLoginButton className='max-w-[250px]' />
                  </motion.div>
                </>
              )}
            </>
          ) : screen.step === 2 ? (
            <VerifyAuthOtpForm />
          ) : (
            <></>
          )}
        </div>
      )}
    </>
  );
};

export default LoginForm;

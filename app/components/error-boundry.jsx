'use client';
import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);

    // Define a state variable to track whether is an error or not
    this.state = { hasError: false };
  }
  static getDerivedStateFromError() {
    // Update state so the next render will show the fallback UI

    return { hasError: true };
  }
  componentDidCatch(error, errorInfo) {
    // You can use your own error logging service here
    console.log({ error, errorInfo });
  }
  render() {
    // Check if the error is thrown
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return (
        <div className='border-[2px] border-[red] bg-red-300 rounded-[5px] text-center py-[10px]'>
          <h2 className='text-center text-black text-[10px] lg:text-sm font-medium'>
            Oops, Something went wrong!
          </h2>
          <button
            className='mt-[10px] rounded-[5px] border-[1px] border-primary bg-primary text-white px-[12px] py-[7px]'
            onClick={() => this.setState({ hasError: false })}
            type='button'
          >
            Try again
          </button>
        </div>
      );
    }

    // Return children components in case of no error

    return this.props.children;
  }
}

export default ErrorBoundary;

'use client';
import React, { useRef } from 'react';
import clsx from 'clsx';

export interface GiftCardAccordianType {
  id: number;
  question: string;
}

const GiftCardAccordian = ({
  onClick,
  data,
  activeId,
  rootClassName,
  children,
}: {
  activeId: number;
  data: GiftCardAccordianType;
  onClick: (id: number) => void;
  rootClassName?: string;
  children: React.ReactNode;
}) => {
  const { id, question } = data;
  const contentEl = useRef<HTMLDivElement>(null);

  return (
    <div
      className={rootClassName}
      style={{ boxShadow: '0px 4px 16px 0px rgba(0, 0, 0, 0.04)' }}
    >
      <div
        className={clsx(
          activeId === id && '!rounded-t-[10px] !rounded-b-none',
          'py-[15px] lg:py-[20px] px-[8px] lg:px-[26px] rounded-[10px] cursor-pointer font-medium bg-container flex justify-between items-center gap-[5px] border-[0.5px] border-white dark:border-[#2d2e32] relative'
        )}
        onClick={() => onClick(id)}
        style={{ boxShadow: '0px 4px 16px 0px rgba(0, 0, 0, 0.04)' }}
      >
        <span className='ml-[5px] text-[8px] sm:text-[9px] lg:text-xs text-primary dark:text-white font-semibold'>
          {question}
        </span>

        <div className='inline-block ml-[23px]'>
          <OpenCloseICon isOpen={activeId === id} />
        </div>
      </div>
      <div
        className='overflow-hidden transition-all duration-300 rounded-b-[5px]'
        ref={contentEl}
        style={
          activeId === id
            ? { height: contentEl.current?.scrollHeight }
            : { height: '0px' }
        }
      >
        <div className='bg-[#E8E8E8] dark:bg-[#35383E] rounded-b-[10px] text-[8px] sm:text-[9px] lg:text-xs text-blackWhite font-normal overflow-hidden'>
          {children}
        </div>
      </div>
    </div>
  );
};

const OpenCloseICon = ({ isOpen }: { isOpen: boolean }) => {
  return (
    <>
      {
        <div className='w-[16px] h-[16px] rounded-full bg-primary text-white flex items-center justify-center text-[12px]'>
          {isOpen ? <span>-</span> : <span>+</span>}
        </div>
      }
    </>
  );
};

export default GiftCardAccordian;

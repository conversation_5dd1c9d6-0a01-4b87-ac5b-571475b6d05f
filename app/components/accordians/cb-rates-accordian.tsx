'use client';
import React, { useRef } from 'react';
import clsx from 'clsx';
import { ArrowDown, ArrowUp } from '../svg/arrow-up-down';
import { useGenerateClickData } from '@/utils/custom-hooks';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { LoadingGif } from '../misc/loading-components';
import {
  CashbackRateTypeTypeEnum,
  ClickTypeTypeEnum,
} from '@/services/api/data-contracts';
import {
  setProceedWithoutCb,
  setSelectedOffer,
} from '@/redux/slices/global-slice';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import Image from 'next/image';
import { Info } from 'lucide-react';
import { Tooltip } from 'antd';

export interface CbRatesAccordianDataType {
  id: number;
  uid: number;
  question: string;
  answer: string;
  newUser: number;
  oldUser: number;
  cashbackType: CashbackRateTypeTypeEnum;
  storeName: string;
  storeLogo: string;
}

const CbRatesAccordian = ({
  onClick,
  data,
  activeId,
}: {
  activeId: number;
  data: CbRatesAccordianDataType;
  onClick: (id: number) => void;
}) => {
  const generateClickData = useGenerateClickData();
  const { isGlobalLoading } = useAppSelector((state) => state.global);
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const { id, uid, question, answer, newUser, oldUser, cashbackType } = data;
  const contentEl = useRef<HTMLDivElement>(null);
  const dispatch = useAppDispatch();

  return (
    <div className='bg-[#F5F5F5] dark:bg-[#222529] rounded-[10px] overflow-hidden shadow-sm'>
      <div
        className={clsx(
          activeId === id && '!rounded-t-[10px] !rounded-b-none',
          'py-[10px] lg:py-[20px] px-[8px] lg:px-[26px] cursor-pointer font-medium bg-container rounded-[10px] flex justify-between items-center gap-[5px] border-[0.5px] border-white dark:border-[#353943]'
        )}
        onClick={() => onClick(id)}
        style={{ boxShadow: '0px 4px 16px 0px rgba(0, 0, 0, 0.04)' }}
      >
        <div className='flex'>
          <OpenClose isOpen={activeId === id} />
          <span className='ml-[5px] text-[8px] sm:text-[9px] lg:text-xs text-primary dark:text-white font-semibold'>
            {question}
          </span>
        </div>

        <div className='flex items-center text-blackWhite shrink-0 ml-2 lg:ml-4'>
          <Tooltip
            arrow
            color='bg-primary'
            title={
              <div className='text-xs lg:text-sm flex gap-x-1 items-center'>
                <Image
                  alt={'store-logo'}
                  className='object-contain inline rounded-sm'
                  height={40}
                  src={data.storeLogo}
                  width={40}
                />{' '}
                <p className='ml-1'>
                  <b>'New'</b> and <b>'Old'</b> refer to {data.storeName} users,
                  not ICB users.
                </p>
              </div>
            }
          >
            <Info className='w-4 lg:w-5 h-auto text-black/50 hover:text-black dark:text-white/50 dark:hover:text-white mr-1' />
          </Tooltip>
          <span className='text-[8px] sm:text-[9px] lg:text-xs font-medium'>
            New User
          </span>
          <div className='flex items-center justify-center text-[8px] sm:text-[9px] lg:text-xs font-black bg-[#FFC554] rounded-[5px] h-[23px] px-[5px] ml-[5px] text-black'>
            {cashbackType === 'amount' && '₹ '}
            {newUser}
            {cashbackType === 'percent' && '%'}
          </div>
          <span className='text-[8px] sm:text-[9px] lg:text-xs font-black ml-[13px]'>
            Old User
          </span>
          <div className='flex items-center justify-center text-[8px] sm:text-[9px] lg:text-xs font-black bg-[#FFC554] rounded-[5px] h-[23px] px-[5px] ml-[5px] text-black'>
            {cashbackType === 'amount' && '₹ '}
            {oldUser}
            {cashbackType === 'percent' && '%'}
          </div>
          <div className='hidden lg:inline-block ml-5'>
            <OpenClose isOpen={activeId === id} useArrow={true} />
          </div>
        </div>
      </div>
      <div
        className='overflow-hidden transition-all duration-300 rounded-b-[10px]'
        ref={contentEl}
        style={
          activeId === id
            ? { height: contentEl.current?.scrollHeight }
            : { height: '0px' }
        }
      >
        <div className='rounded-b-[5px] p-[16px] text-[10px] lg:text-xs text-black dark:!text-[#9A9A9A] font-normal text-justify'>
          <div dangerouslySetInnerHTML={{ __html: answer }} />
          {isGlobalLoading ? (
            <LoadingGif className='mx-auto block mt-auto' />
          ) : (
            <button
              className='w-[100px] h-[36px] border-[1px] border-primary rounded-[5px] mx-auto text-primary text-xs font-semibold mt-[15px] block hover:bg-primary hover:text-white'
              onClick={async () => {
                // Dispatch the selected offer right away
                dispatch(
                  setSelectedOffer({
                    uid,
                    type: ClickTypeTypeEnum.Rates,
                  })
                );

                if (!isUserLogin) {
                  // Set modal states for login
                  dispatch(setProceedWithoutCb(true));
                  return dispatch(setLoginModalOpen(true));
                }

                // Handle window opening and login actions in concurrently
                const actions = [];

                // Open a new tab if the user is logged in and no offer warning
                if (isUserLogin) {
                  actions.push(
                    window.open('/redirecting', '_blank', 'noreferrer')
                  );
                }

                // Generate click data if the user is logged in and no warning
                actions.push(
                  generateClickData({
                    uid: uid,
                    type: ClickTypeTypeEnum.Rates,
                  })
                );

                // Run all actions in parallel
                await Promise.all(actions);
              }}
            >
              Grab Deal
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export const OpenClose = ({
  isOpen,
  useArrow,
}: {
  isOpen: boolean;
  useArrow?: boolean;
}) => {
  return (
    <>
      {useArrow ? (
        <div>
          {isOpen ? (
            <ArrowUp className='text-blackWhite' />
          ) : (
            <ArrowDown className='text-blackWhite' />
          )}
        </div>
      ) : (
        <div className='w-[16px] h-[16px] shrink-0 rounded-full bg-primary text-white flex items-center justify-center text-[12px] lg:hidden'>
          {isOpen ? <span>-</span> : <span>+</span>}
        </div>
      )}
    </>
  );
};

export default CbRatesAccordian;

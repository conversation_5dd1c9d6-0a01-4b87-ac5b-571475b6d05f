'use client';
import React, { useRef } from 'react';
import clsx from 'clsx';
import { motion, AnimatePresence } from 'framer-motion';
import { ArrowDown, ArrowUp } from '../svg/arrow-up-down';

export interface FAQAccordionProps {
  id: number;
  question: string;
  answer: string;
  activeId: number | null;
  onClick: (id: number) => void;
  rootClassName?: string;
  highlight?: string;
}

const FAQAccordion = ({
  id,
  question,
  answer,
  activeId,
  onClick,
  rootClassName,
  highlight,
}: FAQAccordionProps) => {
  const contentEl = useRef<HTMLDivElement>(null);
  const isActive = activeId === id;

  // Function to highlight search terms in text
  const highlightText = (text: string, term: string) => {
    if (!term) {
      return text;
    }

    const parts = text.split(new RegExp(`(${term})`, 'gi'));
    return parts.map((part, i) =>
      part.toLowerCase() === term.toLowerCase() ? (
        <span className='bg-yellow-200 dark:bg-yellow-700 font-medium' key={i}>
          {part}
        </span>
      ) : (
        part
      )
    );
  };

  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className={clsx(
        'bg-white dark:bg-[#222529] rounded-[10px] overflow-hidden shadow-sm mb-4',
        rootClassName
      )}
      initial={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.4 }}
      whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
    >
      {/* Accordion Header */}
      <motion.div
        className={clsx(
          isActive && '!rounded-t-[10px] !rounded-b-none',
          'py-[15px] lg:py-[20px] px-[16px] lg:px-[26px] cursor-pointer font-medium bg-container dark:bg-[#3E424C] rounded-[10px] flex justify-between items-center gap-[5px] border-[0.5px] border-white dark:border-[#353943]'
        )}
        onClick={() => onClick(id)}
        style={{ boxShadow: '0px 4px 16px 0px rgba(0, 0, 0, 0.04)' }}
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        <span className='text-[13px] lg:text-[15px] text-blackWhite dark:text-white font-medium pr-8'>
          {highlight ? highlightText(question, highlight) : question}
        </span>

        <div className='inline-block ml-auto'>
          <OpenCloseIcon isOpen={isActive} />
        </div>
      </motion.div>

      {/* Accordion Content */}
      <motion.div
        className='overflow-hidden transition-all duration-300 rounded-b-[10px]'
        ref={contentEl}
        style={
          isActive
            ? { height: contentEl.current?.scrollHeight }
            : { height: '0px' }
        }
      >
        <div className='bg-[#F5F5F5] dark:bg-[#35383E] rounded-b-[10px] text-[12px] lg:text-[14px] text-blackWhite dark:text-gray-200 font-normal p-[16px] lg:p-[26px]'>
          {highlight ? highlightText(answer, highlight) : answer}
        </div>
      </motion.div>
    </motion.div>
  );
};

const OpenCloseIcon = ({ isOpen }: { isOpen: boolean }) => {
  return (
    <AnimatePresence initial={false} mode='wait'>
      {isOpen ? (
        <motion.div
          animate={{ opacity: 1, rotate: 0 }}
          exit={{ opacity: 0, rotate: 180 }}
          initial={{ opacity: 0, rotate: 180 }}
          key='up'
          transition={{ duration: 0.2 }}
        >
          <ArrowUp className='text-primary dark:text-white w-[15px]' />
        </motion.div>
      ) : (
        <motion.div
          animate={{ opacity: 1, rotate: 0 }}
          exit={{ opacity: 0, rotate: -180 }}
          initial={{ opacity: 0, rotate: -180 }}
          key='down'
          transition={{ duration: 0.2 }}
        >
          <ArrowDown className='text-primary dark:text-white w-[15px]' />
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default FAQAccordion;

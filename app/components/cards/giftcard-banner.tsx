import Image from 'next/image';
import React from 'react';
import offerBadge from '@/public/svg/offer-badge.svg';
import RightArrow from '../svg/right-arrow';
import clsx from 'clsx';
import { GetGiftCardResponse } from '@/services/api/data-contracts';
import SmartLink from '../common/smart-link';
import { LinkType } from '@/utils/link-utils';

const GiftcardBanner = ({ data }: { data: GetGiftCardResponse }) => {
  return (
    <div
      className='w-full mt-[12px] lg:mt-0'
      style={{ boxShadow: '0px 4px 16px 0px rgba(0, 0, 0, 0.04)' }}
    >
      <div className='flex justify-between items-center lg:gap-x-[40px] bg-[#f5f5f5] dark:bg-[#292B31] pr-0 lg:pr-[48px]'>
        <div className='bg-[#f5f5f5] dark:bg-[#2D313A] lg:!bg-transparent pl-[16px] pr-[7px] pb-[20px] grow lg:grow-0'>
          <div className='flex items-center justify-start gap-x-[8px] mt-[16px] lg:gap-x-[40px]'>
            <div className='w-[84px] lg:w-[285px] h-[48px] lg:h-[174px] relative overflow-hidden'>
              <Image
                alt='giftcard'
                className='object-contain'
                fill
                src={data?.giftCard?.imageUrl}
              />
            </div>
            <div className='flex flex-col justify-between gap-x-[3px] lg:gap-y-[8px] lg:w-[285px] h-[48px] lg:h-[174px]'>
              <div>
                <h4 className='text-[12px] lg:text-[14px] font-semibold text-blackWhite'>
                  {data?.giftCard?.name}
                </h4>
                <p className='text-blackWhite hidden lg:block text-xs font-normal'>
                  {data?.giftCard?.description}
                </p>
              </div>
              {/* <span className='text-[8px] sm:text-[9px] lg:text-xs font-semibold text-primary dark:text-[#929396]'>
                Redeem with any catagory on Flipkart
              </span> */}
              <HighLights className='hidden lg:flex h-[56px] min-w-[250px] max-h-[85px] max-w-[337px] grow rounded-[5px]' />
            </div>
          </div>
          <p
            className='font-normal text-[8px] sm:text-[10px] mt-[7px] text-blackWhite break-words lg:hidden'
            style={{ wordBreak: 'break-word' }}
          >
            {data?.giftCard?.description}
          </p>
        </div>
        <div className='flex flex-col justify-between items-end'>
          <div className='flex justify-end items-center mb-4 pr-2 lg:pr-0'>
            <Image
              alt='store'
              className='w-[42px] h-[24px] object-scale-down'
              height={24}
              src={data.giftCard.storeLogo}
              width={42}
            />

            <SmartLink
              className='text-[11px] ml-4 text-primary font-semibold shrink-0 flex items-center justify-center gap-x-[4px] text-primary'
              href={`/store/${data.giftCard.storeName}`}
              linkType={LinkType.INTERNAL}
            >
              Go to Store Page
              <RightArrow className='w-[12px] text-primary -rotate-45' />
            </SmartLink>
          </div>
          <div className='bg-[#f6f6f6] dark:bg-[#25282F] lg:bg-[#EEE] lg:dark:bg-[#2d2e32] shrink-0 min-w-[90px] lg:max-w-[337px] lg:min-w-[280px] flex-center flex-col lg:flex-row lg:h-[144px] lg:gap-x-[10px] lg:rounded-[5px] lg:px-[10px] xl:px-[20px]'>
            {data?.giftCard?.discountGetting && (
              <>
                <div className='w-[76px] h-[22px] bg-[#FDC454] text-black dark:lg:text-white text-[8px] sm:text-[9px] lg:text-[18px] font-[700] flex-center rounded-[2.5px] gap-x-[3px] lg:bg-transparent lg:w-auto lg:h-auto'>
                  <span className='font-black font-nexa pt-[3px] lg:pt-[4px] '>
                    {data?.giftCard?.discountGetting}%
                  </span>
                  <span>Discount</span>
                </div>
                <span className='text-[11px] lg:text-[22px] font-semibold lg:font-[700] text-[#5F51CF] dark:text-blackWhite'>
                  +
                </span>
              </>
            )}
            <div className='flex-center flex-col'>
              <RewardBadge percent={data?.giftCard?.cashbackGiving} />
              <span className='text-[8px] sm:text-[9px] lg:text-[12px] font-semibold text-[#5F51CF] mt-[8px]'>
                Cashback
              </span>
            </div>
          </div>
        </div>
      </div>
      <HighLights className='lg:hidden' />
      <div className='h-[32px] lg:h-[42px] bg-[#4B3FA5] flex items-center justify-center'>
        <Image
          alt=''
          className='h-[15px] w-[20px] lg:w-[29px] lg:h-[29px] srhink-0'
          height={14}
          priority={true}
          src='/svg/icb-logo.svg'
          width={20}
        />
        <div className='relative w-[32px] lg:w-[48px] h-[17px] lg:h-[25px] shrink-0 rounded-[4px] bg-[#8171FF] ml-[4px]'>
          <Image
            alt=''
            className='object-scale-down'
            fill
            quality={100}
            src={'/img/Club.png'}
          />
        </div>
        <div className='text-[#FFC554] text-[8px] sm:text-[9px] lg:text-[14px] font-medium flex items-center ml-[7px] lg:ml-[14px] gap-x-[2px]'>
          <span>Members get </span>
          <span className='line-through	'> 90 Days </span>
          <span> 10 Days </span>
          <span> Cashback Approval </span>
        </div>
        <RightArrow className='text-white w-[14px] ml-[15px]' />
      </div>
    </div>
  );
};

const RewardBadge = ({ percent }: { percent: number }) => {
  return (
    <div className='relative drop-shadow-md'>
      <Image
        alt='offer badge'
        className='w-[50px] lg:w-[90px] lg:h-[90px]'
        src={offerBadge}
      />
      <div className='text-[7px] lg:text-[14px] font-normal text-white absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]'>
        Flat
        <br />
        <span className='font-nexa font-black text-[10px] lg:text-[18px] lg:font-[900]'>
          {percent}%
        </span>
      </div>
    </div>
  );
};

const HighLights = ({ className }: { className?: string }) => {
  return (
    <div
      className={clsx(
        className,
        'h-[52px] bg-[#FFC554] flex items-center justify-evenly text-black'
      )}
    >
      <div className='text-center flex flex-col gap-y-[4px]'>
        <span className='text-[8px] lg:text-[10px] font-medium'>Cashback</span>
        <span className='text-[10px] lg:text-xs font-bold'>Instant</span>
      </div>
      <div className='w-[1px] h-[23px] bg-black shrink-0' />
      <div className='text-center flex flex-col gap-y-[4px]'>
        <span className='text-[8px] lg:text-[10px] font-medium'>Validity</span>
        <span className='text-[10px] lg:text-xs font-bold'>12 Months</span>
      </div>
      <div className='w-[1px] h-[23px] bg-black shrink-0' />
      <div className='text-center flex flex-col gap-y-[4px]'>
        <span className='text-[8px] lg:text-[10px] font-medium'>Usage</span>
        <span className='text-[10px] lg:text-xs font-bold'>Online</span>
      </div>
    </div>
  );
};

export default GiftcardBanner;

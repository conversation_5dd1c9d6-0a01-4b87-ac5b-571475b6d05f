import Image from 'next/image';
import React from 'react';
import FilterSVG from '../svg/filter';
import SortSVG from '../svg/sort';
import SearchSVG from '../svg/search';

const SavedPageCard = ({
  imgUrl,
  title,
  searchValue,
  filterApplied,
  sortApplied,
}: {
  imgUrl: string;
  title: string;
  searchValue: string;
  filterApplied: number;
  sortApplied: number;
}) => {
  return (
    <div className='w-full rounded-[5px] shadow-md shrink-0'>
      <div className='overflow-hidden w-full h-auto rounded-t-[5px] relative'>
        <Image alt='giftcard img' height={115} src={imgUrl} width={300} />
        <div
          className='absolute top-0 left-0 right-0 bottom-0 opacity-90'
          style={{
            background:
              'linear-gradient(180deg, rgba(115, 102, 217, 0.00) 0%, #7366D9 100%)',
          }}
        />
        <div className='absolute pb-[5px] bottom-0 w-full h-auto text-center text-white text-[10px] text-xs font-semibold'>
          {title}
        </div>
      </div>
      <div className='my-[6px] lg:mt-[12px] lg:mb-[2px]'>
        <div className='flex-center gap-x-[5px] text-[8px] lg:text-[10px] font-medium text-black mt-[7px] mb-[10px]'>
          <div className='bg-[#FFE4B0] rounded-[7px] h-[14px] lg:h-[20px] flex-center gap-x-[3px] px-[8px] '>
            <SortSVG className='w-[7px] text-black shrink-0' />
            {sortApplied}
          </div>
          <div className='bg-[#FFE4B0] rounded-[7px] h-[14px] lg:h-[20px] flex-center gap-x-[3px] px-[8px] '>
            <FilterSVG className='w-[7px] text-black shrink-0' />
            {filterApplied}
          </div>
          <div className='bg-[#FFE4B0] rounded-[7px] h-[14px] lg:h-[20px] flex-center gap-x-[3px] px-[8px] '>
            <SearchSVG className='w-[7px] text-black shrink-0' />
            <span className='truncate max-w-[50px] lg:max-w-[80px]'>
              {searchValue}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SavedPageCard;

import React from 'react';
import Apostrophe from '../svg/patterns/apostrophe';
import RatingStars from '../atoms/rating-stars';
import Image from 'next/image';

const TestimonialCard = (props: any) => {
  return (
    <div className='h-full w-full flex flex-col rounded-[10px] bg-[#582CB4] lg:w-[393px] lg:h-[280px] lg:shrink-0 relative lg:shadow-md'>
      <div className='bg-[#7049C1] lg:bg-white lg:dark:bg-[#4D515F] grow p-[20px] rounded-t-[10px]'>
        <Apostrophe className='w-[16px] h-[20px] absolute top-[15px] text-white lg:text-black lg:dark:text-white' />
        <p className='text-justify text-[9px] md:text-[10px] lg:text-[12px] font-normal text-[#F1F1F1] lg:text-black lg:dark:text-white clear-left pt-[20px] lg:pt-[30px] tracking-[1px]'>
          “{props?.testimonial?.review}”
        </p>
      </div>
      <div className='h-[51px] lg:h-[84px] bg-[#582CB4] shrink-0 px-[10px] flex items-center justify-between rounded-b-[10px]'>
        <div className='flex'>
          <div className='user rounded-full overflow-hidden'>
            <Image
              alt='user img'
              className='rounded-full w-[35px] h-[35px] lg:w-[58px] lg:h-[58px]'
              height={35}
              // quality={100}
              src={props?.testimonial?.reviewerAvatar}
              width={35}
            />
          </div>
          <div className='flex flex-col justify-center gap-y-[3px] lg:gap-y-[5px] items-center ml-[8px]'>
            <span className='text-[8px] lg:text-sm font-semibold text-white tracking-[0.5px]'>
              {props?.testimonial?.reviewerName}
            </span>
            <RatingStars
              rating={props?.testimonial?.rating}
              starClassName='w-[6.66px] h-[6.33px] lg:w-[14px] lg:h-[13px]'
              wrapperClassName='lg:gap-x-[8px]'
            />
            <div />
          </div>
        </div>
        <div className='rounded-[5px] w-[40px] h-[28px] shrink-0 bg-[#FEAA47] text-[10px] font-nexa font-black text-[#582CB4] flex items-center justify-center'>
          {props?.testimonial?.rating}/5
        </div>
      </div>
    </div>
  );
};

export default TestimonialCard;

// import clsx from 'clsx';
// import React from 'react';

// const NotificationCard = () => {
//   return (
//     <div
//       className={clsx(
//         !hasRead && 'rouned-[6px] p-[3px] bg-white dark:bg-[#3E424C]'
//       )}
//     >
//       <div
//         className={clsx(
//           !hasRead && '!bg-[#FEC]',
//           'relative bg-white dark:bg-[#3E424C] rounded-[10px]'
//         )}
//       >
//         <div className={clsx('indiacator')} />
//       </div>
//     </div>
//   );
// };

// export default NotificationCard;

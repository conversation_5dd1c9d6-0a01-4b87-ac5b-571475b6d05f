import type { ContextMissedOfferType } from '@/services/api/data-contracts';
import {
  formatIndRs,
  formatStoreName,
  generateProductUrl,
} from '@/utils/helpers';
import type { Omit } from '@reduxjs/toolkit/dist/tsHelpers';
import Image from 'next/image';
import React from 'react';
import clsx from 'clsx';
import ShimmerEffect, { RectShimmer } from '../atoms/shimmer-effect';
import { LinkType } from '@/utils/link-utils';
import SmartLink from '../common/smart-link';

const MissedOfferCard = ({
  uid,
  offerTitle,
  productImage,
  storeName,
  storeLogoUrl,
  currentAmount,
  isLoading = false,
}: Omit<ContextMissedOfferType, 'storeBgColor'> & { isLoading?: boolean }) => {
  const url = generateProductUrl(storeName, offerTitle);

  if (isLoading) {
    return (
      <div className='min-h-[148px] w-full max-w-[192px] p-[2px] lg:p-[5px] lg:pb-[20px] rounded-[5px] bg-[#FEFEFE] dark:bg-[#2d2e32] shrink-0 shadow-md'>
        <div className='relative w-auto h-[157px] overflow-hidden rounded-t-[6px]'>
          <ShimmerEffect className='w-full h-full' />
        </div>

        <div className='flex flex-col items-center justify-center px-1 mt-[8px] lg:mt-[24px] gap-[6px] relative'>
          <div className='offerStoreCont absolute z-[2] top-[-38px] left-[50%] translate-x-[-50%] hidden lg:block'>
            <div className='relative w-[100px] h-[28px] shrink-0 rounded-[6px] bg-[#FDFDFE] shadow-md overflow-hidden'>
              <ShimmerEffect className='w-full h-full' />
            </div>
          </div>
          <RectShimmer className='w-[120px] h-[10px] mt-[5px]' />
          <RectShimmer className='w-[80px] h-[10px] mt-[5px]' />
        </div>

        <style jsx>{`
          @keyframes subtleTransition {
            from {
              opacity: 0.9;
            }
            to {
              opacity: 1;
            }
          }
        `}</style>
      </div>
    );
  }

  return (
    <SmartLink
      className={clsx(
        'min-h-[148px] w-full max-w-[192px] rounded-[5px] bg-[#FEFEFE] dark:bg-[#2d2e32] shrink-0 shadow-md cursor-pointer transition-all duration-300 group',
        'hover:shadow-lg active:scale-[0.97]'
      )}
      href={`/store/${formatStoreName(storeName)}/${url}?uid=${uid}`}
      linkType={LinkType.INTERNAL}
    >
      <div className='relative w-auto h-[157px] overflow-hidden rounded-t-[6px]'>
        <Image
          alt='offer'
          className='object-cover transition-all duration-300 group-hover:opacity-100 group-hover:scale-110'
          fill
          src={productImage}
          style={{ opacity: 0.9 }}
        />
      </div>

      <div className='flex flex-col items-center justify-center mt-[8px] lg:mt-[24px] gap-[6px] relative pt-[5px] px-[8px]'>
        <div className='offerStoreCont absolute z-[2] top-[-38px] left-[50%] translate-x-[-50%] hidden lg:block'>
          <div className='relative w-[100px] h-[28px] shrink-0 rounded-[6px] bg-[#FDFDFE] shadow-md overflow-hidden'>
            <Image
              alt={'store img'}
              className='object-contain scale-[80%]'
              fill
              quality={100}
              src={storeLogoUrl}
            />
          </div>
        </div>
        <span className='text-[10px] xl:text-[11px] font-[300] text-blackWhite'>
          {offerTitle}
        </span>
        <span className='text-[10px] xl:text-[11px] font-[500] text-red-500 font-nexa mt-[5px]'>
          {currentAmount > 0 && formatIndRs(currentAmount)}
        </span>
      </div>

      <style jsx>{`
        @keyframes subtleTransition {
          from {
            opacity: 0.9;
          }
          to {
            opacity: 1;
          }
        }
      `}</style>
    </SmartLink>
  );
};

export default MissedOfferCard;

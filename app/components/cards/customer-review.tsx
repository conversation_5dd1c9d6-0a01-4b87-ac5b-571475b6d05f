'use client';
import Image from 'next/image';
import React from 'react';
import RatingStars from '../atoms/rating-stars';
import dayjs from 'dayjs';

const CustomerReview = ({
  name,
  reviewText,
  rating,
  date,
  avatar,
}: {
  name: string;
  reviewText: string;
  rating: number;
  date: string;
  avatar: string;
}) => {
  return (
    <div className='flex rounded-[5px] shadow-sm'>
      <div className='w-[97px] bg-[#f9f9f9] dark:bg-[#464B56] flex flex-col gap-y-[6px] justify-center items-center shrink-0 rounded-l-[5px] py-[8px] overflow-hidden'>
        <div className='relative w-[34px] h-[34px] rounded-full'>
          <Image
            alt='profile'
            className='object-cover'
            fill
            src={
              avatar ??
              `https://ui-avatars.com/api/?background=574ABE&color=fff&name=${name}`
            }
          />
        </div>
        <span className='text-[8px] text-center sm:text-[9px] lg:text-[10px] font-medium text-blackWhite maxLines2 break-words max-w-[97px]'>
          {name}
        </span>
      </div>
      <div className='bg-white dark:bg-[#32363F] text-blackWhite p-[12px] rounded-r-[5px] grow'>
        <p className='text-[8px] sm:text-[9px] lg:text-xs font-normal italic'>
          {reviewText}
        </p>
        <div className='flex justify-between items-center mt-[5px] lg:mt-[12px]'>
          <RatingStars
            rating={rating}
            starClassName='w-[9px] sm:w-[10px] lg:w-[12px]'
          />
          <span className='text-[8px] sm:text-[9px] lg:text-[10px] font-[300]'>
            {dayjs(date).format('DD MMM YYYY')}
          </span>
        </div>
      </div>
    </div>
  );
};

export default CustomerReview;

import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';

const TrendingCategoryCard = ({
  id,
  iconUrl,
  title,
}: {
  id: string;
  iconUrl: string;
  title: string;
}) => {
  const router = useRouter();
  return (
    <div
      className='cursor-pointer'
      onClick={() =>
        router.push(`/deals-and-coupons/?trendingMainCategory=${id}`)
      }
    >
      <div className='w-[80px] min-h-[83px] lg:w-[125px] lg:h-[114px] flex flex-col justify-evenly items-center p-[2px] rounded-[5px] bg-[#EFEFEF] dark:bg-[#292B31] shrink-0 border-[0.5px] border-white dark:border-[#292B31] lg:border-none lg:shadow-md'>
        <div className='relative w-[20px] h-[20px] lg:w-[40px] lg:h-[40px]'>
          <Image
            alt={title || 'trending-category'}
            className='object-contain'
            fill
            src={iconUrl}
          />
        </div>

        <div className='text-center text-[8px] lg:text-[12px] font-medium text-blackWhite lg:hidden'>
          {title}
        </div>
      </div>
      <div className='text-center text-[8px] lg:text-[12px] font-semibold text-black hidden lg:block mt-[20px]'>
        {title}
      </div>
    </div>
  );
};

export default TrendingCategoryCard;

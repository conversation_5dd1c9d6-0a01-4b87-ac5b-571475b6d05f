import Image from 'next/image';
import React from 'react';
import RatingStars from '../atoms/rating-stars';
import offerBadge from '@/public/svg/offer-badge.svg';
import RightArrow from '../svg/right-arrow';
import clsx from 'clsx';
import { motion } from 'framer-motion';
import {
  type GetStoreDetails,
  type GetStoreDetailsOfferTypeEnum,
  ClickTypeTypeEnum,
} from '@/services/api/data-contracts';
import { useGenerateClickData } from '@/utils/custom-hooks';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { LoadingGif } from '../misc/loading-components';
import EnhancedNoData from '../enhanced-no-data';
import {
  setProceedWithoutCb,
  setSelectedOffer,
  setShowWarningModal,
} from '@/redux/slices/global-slice';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import SmartLink from '../common/smart-link';
import { LinkType } from '@/utils/link-utils';
import { Modal } from 'antd';
import CrossSVG from '../svg/cross';
import ThemeButton from '../atoms/theme-btn';

interface StoreBannerProps {
  data?: GetStoreDetails;
  isLoading?: boolean;
  error?: string;
}

const StoreBanner = ({ data, isLoading, error }: StoreBannerProps) => {
  // const router = useRouter();
  const { showWarningModal } = useAppSelector((state) => state.global);
  const generateClickData = useGenerateClickData();
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();

  if (isLoading) {
    return (
      <motion.div
        animate={{ opacity: 1 }}
        className='w-full mt-[12px] lg:mt-0 min-h-[200px] bg-white dark:bg-[#2d2e32] rounded-md flex items-center justify-center'
        initial={{ opacity: 0 }}
      >
        <LoadingGif className='!h-[50px] lg:!h-[60px]' />
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='w-full mt-[12px] lg:mt-0 min-h-[200px] bg-white dark:bg-[#2d2e32] rounded-md'
        initial={{ opacity: 0, y: 20 }}
        transition={{ duration: 0.5 }}
      >
        <EnhancedNoData
          customHeight='h-[200px]'
          imgClass='!w-[100px] !h-[100px]'
          message={error}
          showHomeLink={true}
        />
      </motion.div>
    );
  }

  if (!data) {
    return (
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='w-full mt-[12px] lg:mt-0 min-h-[200px] bg-white dark:bg-[#2d2e32] rounded-md'
        initial={{ opacity: 0, y: 20 }}
        transition={{ duration: 0.5 }}
      >
        <EnhancedNoData
          customHeight='h-[200px]'
          imgClass='!w-[100px] !h-[100px]'
          message='Store information not available'
          showHomeLink={true}
        />
      </motion.div>
    );
  }

  return (
    <>
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='w-full mt-[12px] lg:mt-0 bg-white dark:bg-[#2d2e32]'
        initial={{ opacity: 0, y: 20 }}
        transition={{ duration: 0.5 }}
        whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
      >
        <div className='flex flex-col lg:flex-row'>
          <motion.div
            animate={{ opacity: 1 }}
            className='bg-[#f5f5f5] dark:bg-[#2D313A] lg:bg-white lg:dark:bg-[#2d2e32] p-2 lg:p-2.5 xl:p-4 lg:grow rounded-t-md lg:rounded-l-md'
            initial={{ opacity: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className='flex items-center justify-start flex-wrap gap-x-[8px]'>
              {data.logo ? (
                <div className='w-[80px] sm:w-[95px] lg:w-[118px] h-[42px] lg:h-[66px] relative overflow-hidden mt-[5px] bg-white rounded-md p-2'>
                  <Image
                    alt={data.name || 'Store logo'}
                    className='object-contain'
                    fill
                    src={data.logo}
                  />
                </div>
              ) : (
                <motion.div
                  animate={{ opacity: 1 }}
                  className='w-[80px] sm:w-[95px] lg:w-[118px] h-[42px] lg:h-[66px] bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center'
                  initial={{ opacity: 0 }}
                >
                  <span className='text-gray-400 dark:text-gray-500 text-sm'>
                    No Logo
                  </span>
                </motion.div>
              )}

              <motion.div
                animate={{ opacity: 1, x: 0 }}
                className='flex items-center mt-[8px]'
                initial={{ opacity: 0, x: -20 }}
                transition={{ delay: 0.3 }}
              >
                <RatingStars
                  rating={data.ratingAverage || 4}
                  wrapperClassName='gap-x-[2px] lg:ml-[35px]'
                />
                <div className='text-xs font-normal ml-2'>
                  <span className='font-black font-nexa'>
                    {data.ratingAverage || 4}
                  </span>
                  <span className='font-bold'> / </span>
                  <span className=''>{data.ratingsCount || 5}</span> Rating
                </div>
                {data.isAppSaleTrackable && (
                  <motion.div
                    animate={{ opacity: 1, scale: 1 }}
                    className='ml-2 px-2 py-0.5 rounded-full text-[10px] font-medium flex items-center bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    initial={{ opacity: 0, scale: 0.8 }}
                    transition={{
                      duration: 0.5,
                      delay: 0.4,
                      type: 'spring',
                      stiffness: 300,
                    }}
                    whileHover={{
                      scale: 1.05,
                      boxShadow: '0px 0px 8px rgba(34, 197, 94, 0.6)',
                    }}
                  >
                    <span className='w-2 h-2 rounded-full mr-1 bg-green-500 dark:bg-green-400 animate-pulse' />
                    App Trackable
                  </motion.div>
                )}
              </motion.div>
            </div>

            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.4 }}
            >
              <HighLights
                cashbackType={data.cashbackType}
                confirmationTime={data.confirmationTime || 'N/A'}
                highlightsClass='h-auto lg:h-[56px] w-full grow rounded-[5px] mt-4 lg:mt-0'
                isOfferWarning={Boolean(data.offerWarning)}
                missingAccepted={data.missingAccepted || false}
                rootClass='mt-4 lg:mt-0 hidden lg:flex'
                trackingTime={data.trackingTime || 'N/A'}
                uid={data.uid}
              />
            </motion.div>
          </motion.div>

          {data.active && (
            <motion.div
              animate={{ opacity: 1, x: 0 }}
              className='bg-[#f6f6f6] dark:bg-[#25282F] lg:bg-white lg:dark:bg-[#2d2e32] shrink-0 min-w-[90px] lg:min-w-[150px] min-[1060px]:min-w-[189px] 2xl:min-w-[20vw] flex items-center justify-center flex-col p-4 rounded-r-md'
              initial={{ opacity: 0, x: 20 }}
              transition={{ delay: 0.5 }}
            >
              <motion.div
                transition={{ duration: 0.5 }}
                whileHover={{ scale: 1.05, rotate: [0, -5, 5, -5, 0] }}
              >
                <RewardBadge
                  cashbackAmount={data.cashbackAmount}
                  cashbackPercent={data.cashbackPercent}
                  offerType={data.offerType}
                />
              </motion.div>

              {data.cashbackType === 'reward' && (
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  className='flex flex-col items-center mt-4'
                  initial={{ opacity: 0, y: 10 }}
                  transition={{ delay: 0.6 }}
                >
                  <span className='text-[10px] lg:text-xs 2xl:text-base font-semibold text-[#5F51CF] dark:text-white text-center'>
                    Reward Points
                  </span>
                  <span className='text-[10px] lg:text-xs 2xl:text-base font-bold text-[#5F51CF] dark:text-white mt-2 text-center'>
                    1 Point = 1 INR
                  </span>
                </motion.div>
              )}
            </motion.div>
          )}
        </div>

        <motion.div
          animate={{ opacity: 1 }}
          initial={{ opacity: 0 }}
          transition={{ delay: 0.6 }}
        >
          <HighLights
            cashbackType={data.cashbackType}
            confirmationTime={data.confirmationTime || 'N/A'}
            isOfferWarning={Boolean(data.offerWarning)}
            missingAccepted={data.missingAccepted || false}
            rootClass='mt-4 lg:hidden'
            trackingTime={data.trackingTime || 'N/A'}
            uid={data.uid}
          />
        </motion.div>

        {data.uid !== 26 && (!data.active || data.storeWarning.length > 0) && (
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='h-auto min-h-[32px] lg:min-h-[42px] flex items-center justify-center text-center px-4 py-2 font-medium w-full bg-red-500 text-white text-[10px] lg:text-sm'
            initial={{ opacity: 0, y: 20 }}
            transition={{ delay: 0.7 }}
          >
            {!data.active
              ? 'No Cashback available right now. We are trying hard to bring it back as soon as possible!'
              : data.storeWarning.length > 0
              ? data.storeWarning
              : ''}
          </motion.div>
        )}

        {data.uid !== 26 && (
          <SmartLink
            href='https://card.indiancashback.com'
            linkType={LinkType.EXTERNAL}
            target='_blank'
          >
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='h-[32px] lg:h-[42px] bg-[#4B3FA5] flex items-center justify-center rounded-b-md'
              initial={{ opacity: 0, y: 20 }}
              transition={{ delay: 0.8 }}
            >
              {' '}
              <div className='relative w-[70px] lg:w-[90px] min-h-[30px] shrink-0 rounded-[4px] ml-[14px]'>
                <Image
                  alt=''
                  className='object-scale-down'
                  fill
                  quality={100}
                  src={'/img/ICBInstantPay.png'}
                />
              </div>
              <div className='text-[#FFC554] text-[8px] sm:text-[9px] lg:text-[14px] font-medium flex items-center ml-[7px] lg:ml-[14px] gap-x-[2px]'>
                Members get <span className='line-through'>90 Days</span> 10
                Days Cashback Approval.
              </div>
            </motion.div>
          </SmartLink>
        )}
      </motion.div>

      <Modal
        cancelText=''
        centered
        classNames={{
          content: '!bg-container',
          header: '!bg-container !text-blackWhite',
        }}
        closeIcon={<CrossSVG className='text-blackWhite w-[16px]' />}
        destroyOnClose={true}
        footer={<></>}
        maskClosable={true}
        okText=''
        onCancel={() => dispatch(setShowWarningModal(false))}
        open={showWarningModal}
        title={
          <h4 className='text-[16px] font-bold text-blackWhite'>
            Important Points on the Store
          </h4>
        }
      >
        <div
          className='p-4 text-blackWhite'
          dangerouslySetInnerHTML={{ __html: data.offerWarning }}
        />
        {/* //Add a button to close the modal  with theme button*/}
        <ThemeButton
          className='w-36 mx-auto mt-4'
          onClick={async () => {
            if (!isUserLogin) {
              dispatch(setProceedWithoutCb(true));
              return dispatch(setLoginModalOpen(true));
            }

            const actions = [];

            // Open new tab action
            actions.push(window.open(`/redirecting`, '_blank', 'noreferrer'));

            // Generate click data action
            actions.push(
              generateClickData({
                uid: data.uid,
                type: ClickTypeTypeEnum.Express,
              })
            );

            // Run both actions concurrently
            await Promise.all(actions);
          }}
          text='Continue Shopping'
        />
      </Modal>
    </>
  );
};

export const RewardBadge = ({
  offerType,
  cashbackPercent,
  cashbackAmount,
}: {
  offerType?: GetStoreDetailsOfferTypeEnum;
  cashbackPercent?: number;
  cashbackAmount?: number;
}) => {
  if (!cashbackAmount && !cashbackPercent) {
    return null;
  }
  return (
    <div className='relative drop-shadow-md'>
      <Image
        alt='offer badge'
        className='w-[72px] lg:w-[60px] xl:w-[72px] 2xl:w-[92px]'
        src={offerBadge}
      />
      <div className='text-xs lg:text-[9px] xl:text-xs font-normal text-white absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] text-center'>
        {offerType === 'upto' ? 'UP TO' : 'FLAT'}
        <br />
        <span className='font-nexa font-black text-xs lg:text-[9px] xl:text-xs 2xl:text-base lg:font-[900]'>
          {cashbackPercent ? `${cashbackPercent}%` : `₹${cashbackAmount}`}
        </span>
      </div>
    </div>
  );
};

const HighLights = ({
  uid,
  rootClass,
  cashbackType,
  highlightsClass,
  missingAccepted,
  trackingTime,
  confirmationTime,
  isOfferWarning,
}: {
  uid: number;
  rootClass?: string;
  highlightsClass?: string;
  missingAccepted: boolean;
  trackingTime: string;
  confirmationTime: string;
  isOfferWarning: boolean;
  cashbackType: 'reward' | 'cashback';
}) => {
  const generateClickData = useGenerateClickData();
  const { isGlobalLoading } = useAppSelector((state) => state.global);
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const bannerDivClass = clsx(
    'text-center flex flex-col gap-y-[4px] min-w-[137px] lg:min-w-[140px] min-[1130px]:min-w-[150px] xl:min-w-[15vw] px-0 xl:px-2 py-0 xl:py-2'
  );
  const bannerTitleClass = clsx('text-[10px] font-medium');
  const bannerValueClass = clsx('text-xs font-bold');

  const isReward = cashbackType === 'reward';
  return (
    <div
      className={clsx(
        rootClass,
        highlightsClass,
        'flex flex-col lg:flex-row gap-y-2 lg:gap-x-2'
      )}
    >
      {uid !== 26 && (
        <div className='flex flex-wrap lg:flex-nowrap items-center justify-evenly bg-[#FFC554] text-black rounded p-2 lg:p-0 w-full  lg:grow'>
          <div className={bannerDivClass}>
            <span className={bannerTitleClass}>
              {isReward ? 'Missing Rewards' : 'Missing Cashback'}
            </span>
            <span className={bannerValueClass}>
              {missingAccepted ? 'Accepted' : 'Not Accepted'}
            </span>
          </div>
          <div className='w-[1px] h-[23px] bg-black shrink-0 hidden lg:block' />
          <div className={bannerDivClass}>
            <span className={bannerTitleClass}>
              {isReward ? 'Rewards Tracking' : 'Cashback Tracking'}
            </span>
            <span className={bannerValueClass}>{trackingTime}</span>
          </div>
          <div className='w-[1px] h-[23px] bg-black shrink-0 hidden lg:block' />
          <div className={bannerDivClass}>
            <span className={bannerTitleClass}>
              {isReward ? 'Rewards Confirmation' : 'Cashback Confirmation'}
            </span>
            <span className={bannerValueClass}>{confirmationTime}</span>
          </div>
        </div>
      )}

      <button
        className={clsx(
          isGlobalLoading && '!bg-transparent border-[2px] border-primary',
          'w-full lg:max-w-[140px] xl:max-w-[206px] h-[40px] lg:h-[56px] text-xs lg:text-[14px] xl:text-[16px] font-semibold bg-primary rounded-[5px] shrink-0 flex items-center justify-center gap-x-[7px] text-white hover:bg-primaryDark active:bg-primary',
          uid === 26 && 'sm:mt-2'
        )}
        disabled={isGlobalLoading}
        onClick={async () => {
          // Dispatch the selected offer right away
          dispatch(
            setSelectedOffer({
              uid,
              type: ClickTypeTypeEnum.Express,
            })
          );

          // // Handle window opening and login actions in concurrently
          const actions = [];

          // Open a new tab if the user is logged in and no offer warning
          if (!isOfferWarning && isUserLogin) {
            actions.push(window.open('/redirecting', '_blank', 'noreferrer'));
          }

          // Show warning modal if offer has a warning
          if (isOfferWarning) {
            dispatch(setShowWarningModal(true));
          } else {
            if (!isUserLogin) {
              // Set modal states for login
              dispatch(setProceedWithoutCb(true));
              return dispatch(setLoginModalOpen(true));
            }

            // Generate click data if the user is logged in and no warning
            actions.push(
              generateClickData({ uid, type: ClickTypeTypeEnum.Express })
            );
          }

          // Run all actions in parallel
          await Promise.all(actions);
        }}
        type='button'
      >
        {isGlobalLoading ? (
          <LoadingGif className='!h-[36px] lg:!h-[40px]' />
        ) : (
          <>
            Visit Store
            <RightArrow className='w-[12px] lg:w-[20px] text-white mt-[2px]' />
          </>
        )}
      </button>
    </div>
  );
};

export default StoreBanner;

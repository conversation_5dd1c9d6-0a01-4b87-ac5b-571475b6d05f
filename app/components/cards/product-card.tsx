import Image from 'next/image';
import React, { forwardRef, Ref } from 'react';
import offerBadge from '@/public/svg/offer-badge.svg';
import { ProductProps } from '@/types/global-types';
import { getLastWord } from '../../../utils/helpers';
import { useAppSelector } from '@/redux/hooks';
import { LoadingGif } from '../misc/loading-components';
import clsx from 'clsx';
import { motion } from 'framer-motion';
import SmartLink from '../common/smart-link';
import { LinkType } from '@/utils/link-utils';
import OfferCountdown from '../common/offer-countdown';

const ProductCard = forwardRef(
  (props: ProductProps, ref: Ref<HTMLDivElement>) => {
    const {
      productImg,
      offerTitle,
      offerPercent,
      // newUser,
      // oldUser,
      appliedPrice,
      offerType,
      offerEndsIn,
      storeLogoUrl,
      storeName,
      offerAmount,
      offerProductHandler,
      offerCaption,
      isExpired,
      hideCbTag,
    } = props;

    const { isGlobalLoading } = useAppSelector((state) => state.global);
    // console.log(appliedPrice, 'applied price');

    return (
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='px-[16px] pt-[18px] pb-[27px] mt-4 lg:mx-0 shadow-sm lg:flex lg:items-end lg:gap-x-[30px] dark:bg-[#292B31] rounded-[10px] transition-all duration-300'
        initial={{ opacity: 0, y: 20 }}
        ref={ref}
        transition={{ duration: 0.5, ease: 'easeOut' }}
        whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
      >
        <motion.div
          className='relative w-auto h-[160px] lg:w-[434px] lg:h-[260px] overflow-hidden rounded-t-[10px] lg:rounded-[10px] shrink-0'
          transition={{ duration: 0.3 }}
          whileHover={{ scale: 1.02 }}
        >
          <Image
            alt='product img'
            className='object-cover lg:object-contain lg:rounded-[10px]'
            fill
            priority
            quality={100}
            src={productImg}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='offerDetailsWrapper lg:hidden relative bg-white dark:bg-[#2d2e32] rounded-b-[6px] shadow-md'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <motion.div
            animate={{ y: 0, opacity: 1 }}
            className='offerStoreCont absolute top-[-16px] left-[50%] translate-x-[-50%]'
            initial={{ y: -10, opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <div className='relative w-[100px] h-[28px] shrink-0 rounded-[6px] bg-[#FDFDFE] shadow-md'>
              <Image
                alt={'store img'}
                className='object-contain scale-[80%]'
                fill
                src={storeLogoUrl || ''}
              />
            </div>
          </motion.div>
          {offerEndsIn && (
            <motion.div
              animate={{ opacity: 1 }}
              className='offerEndCont text-[10px] text-[#FF3737] flex items-center justify-center pt-[25px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
            >
              {isExpired ? (
                <span className='font-light'>Expired </span>
              ) : (
                <OfferCountdown duration={offerEndsIn} />
              )}
            </motion.div>
          )}
          <motion.div
            animate={{ opacity: 1 }}
            className='pt-[5px] text-center text-[10px] lg:text-sm font-normal text-[#292B31] dark:text-[#FDFDFE] px-[8px] flex items-center justify-center grow'
            initial={{ opacity: 0 }}
            style={
              isExpired
                ? { paddingBottom: 10, overflowWrap: 'break-word' }
                : { overflowWrap: 'break-word' }
            }
            transition={{ duration: 0.3, delay: 0.5 }}
          >
            {offerTitle}
          </motion.div>
          {/* {offerPercent ||
            (offerAmount && (
              <div
                className='mt-[10px] text-center px-[8px] pb-[10px] text-[10px] text-primary font-[700]'
                style={{ overflowWrap: 'break-word' }}
              >
                Up to {offerPercent ? `${offerPercent}%` : `₹${offerAmount}`}{' '}
                cashback
              </div>
            ))} */}

          {!isExpired && (
            <motion.div
              animate={{ opacity: 1 }}
              className='text-center text-blackWhite px-[8px] pb-[10px] text-[10px] font-normal'
              initial={{ opacity: 0 }}
              style={{ overflowWrap: 'break-word' }}
              transition={{ duration: 0.3, delay: 0.6 }}
            >
              {hideCbTag ? '' : offerCaption}
            </motion.div>
          )}

          {/* {(newUser || oldUser) && (
            <div
              className='text-center text-blackWhite px-[8px] pb-[10px] text-[10px] font-normal'
              style={{ overflowWrap: 'break-word' }}
            >
              New User <span className='font-[700] mr-[5px]'>{newUser}%</span>
              Old User <span className='font-[700]'>{oldUser}%</span>
            </div>
          )} */}

          {/* shows a 0 in mobile UI , so commented out the below */}
          {appliedPrice !== undefined &&
            appliedPrice !== null &&
            appliedPrice > 0 && (
              <motion.div
                animate={{ opacity: 1 }}
                className='h-[27px] w-full flex-center text-[10px] lg:text-sm bg-[#FFC554]'
                initial={{ opacity: 0 }}
                transition={{ duration: 0.3, delay: 0.7 }}
              >
                <p className='text-center block text-black'>
                  Offer Applied Price
                  <span className='font-bold ml-[5px]'>₹{appliedPrice}</span>
                </p>
              </motion.div>
            )}

          <motion.button
            className={clsx(
              isGlobalLoading && '!bg-transparent border border-primary',
              'h-[45px] w-full flex-center rounded-b-[6px] font-semibold text-[12px] bg-primary text-white transition-colors duration-300 hover:bg-primaryDark'
            )}
            disabled={isGlobalLoading}
            onClick={offerProductHandler}
            transition={{ duration: 0.2 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {isGlobalLoading ? (
              <LoadingGif className='!h-[30px]' />
            ) : (
              'GRAB Deal'
            )}
          </motion.button>
        </motion.div>
        <motion.div
          animate={{ opacity: 1, x: 0 }}
          className='hidden lg:flex grow flex-col justify-between h-full min-h-[245px]'
          initial={{ opacity: 0, x: 20 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <motion.div
            animate={{ opacity: 1 }}
            className='pt-[5px] text-left text-sm font-normal text-[#292B31] dark:text-[#FDFDFE]'
            initial={{ opacity: 0 }}
            style={{ overflowWrap: 'break-word' }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            {offerTitle}
          </motion.div>
          <div className='flex justify-between items-end'>
            <div className='grow'>
              {offerEndsIn && (
                <motion.div
                  animate={{ opacity: 1 }}
                  className='offerEndCont w-max px-[10px] h-[21px] mt-[10px] mb-[26px] text-[8px] font-black text-white bg-[#FF5D5D] flex-center rounded-[2.5px]'
                  initial={{ opacity: 0 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                  whileHover={{ scale: 1.05 }}
                >
                  {isExpired ? (
                    <span className='font-light'>Expired </span>
                  ) : (
                    <OfferCountdown
                      className={'!text-[10px] !text-white !mt-0'}
                      duration={offerEndsIn}
                    />
                  )}
                </motion.div>
              )}
              {!!appliedPrice && (
                <motion.div
                  animate={{ opacity: 1 }}
                  className='text-sm'
                  initial={{ opacity: 0 }}
                  transition={{ duration: 0.3, delay: 0.6 }}
                >
                  <p className='block text-primary'>
                    Offer Applied Price
                    <span className='font-bold ml-[5px]'>₹{appliedPrice}</span>
                  </p>
                </motion.div>
              )}
              {/* {(newUser || oldUser) && (
                <div
                  className='text-left text-blackWhite mt-[25px] text-[12px] font-normal'
                  style={{ overflowWrap: 'break-word' }}
                >
                  New User{' '}
                  <span className='font-[700] mr-[20px]'>{newUser}%</span> Old
                  User <span className='font-[700]'>{oldUser}%</span>
                </div>
              )} */}
              {!isExpired && (
                <motion.div
                  animate={{ opacity: 1 }}
                  className='text-left text-blackWhite mt-[25px] text-[12px] font-normal'
                  initial={{ opacity: 0 }}
                  style={{ overflowWrap: 'break-word' }}
                  transition={{ duration: 0.3, delay: 0.7 }}
                >
                  {hideCbTag ? '' : offerCaption}
                </motion.div>
              )}

              <motion.button
                animate={{ opacity: 1 }}
                className={clsx(
                  isGlobalLoading && '!bg-transparent border border-primary',
                  'h-[47px] w-full text-white text-center mt-[30px] bg-primary rounded-[10px] shadow-sm flex-center disabled:cursor-not-allowed transition-colors duration-300 hover:bg-primaryDark'
                )}
                disabled={isGlobalLoading}
                initial={{ opacity: 0 }}
                onClick={offerProductHandler}
                transition={{ duration: 0.3, delay: 0.8 }}
                whileHover={{
                  scale: 1.03,
                  boxShadow: '0px 6px 15px rgba(0, 0, 0, 0.1)',
                }}
                whileTap={{ scale: 0.97 }}
              >
                {isGlobalLoading ? (
                  <LoadingGif className='!h-[30px]' />
                ) : (
                  'GRAB Deal'
                )}
              </motion.button>
            </div>
            <div className='ml-[13px]'>
              {!isExpired && (offerPercent || offerAmount) ? (
                <motion.div
                  animate={{ opacity: 1, scale: 1 }}
                  className='flex flex-col items-center'
                  initial={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
                >
                  <motion.div
                    className='w-[92px] relative drop-shadow-md'
                    whileHover={{
                      rotate: [0, -5, 5, -5, 0],
                      transition: { duration: 0.5 },
                    }}
                  >
                    <Image
                      alt='offer badge'
                      className='w-[92px]'
                      src={offerBadge}
                    />
                    <div className='text-sm font-normal text-white absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%] text-center'>
                      {offerType}
                      <br />
                      <span className='text-[21px] font-bold block mt-[7px]'>
                        {offerPercent
                          ? `${offerPercent > 0 ? offerPercent : ''}%`
                          : `₹${
                              offerAmount && offerAmount > 0 ? offerAmount : ''
                            }`}
                      </span>
                    </div>
                  </motion.div>
                  <motion.span
                    className='text-sm lg:text-[14px] font-bold text-[#7B6CEE] mt-[5px]'
                    whileHover={{
                      color: '#5F51CF',
                      transition: { duration: 0.2 },
                    }}
                  >
                    {getLastWord(offerCaption || '')}
                  </motion.span>
                </motion.div>
              ) : (
                ''
              )}
              <motion.div
                animate={{ opacity: 1 }}
                initial={{ opacity: 0 }}
                transition={{ duration: 0.3, delay: 0.7 }}
                whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
              >
                <SmartLink
                  href={`/store/${storeName}`}
                  linkType={LinkType.INTERNAL}
                >
                  <div className='relative w-[180px] h-[48px] mt-[30px] shrink-0 rounded-[6px] bg-[#FDFDFE] shadow-md'>
                    <Image
                      alt={'store img'}
                      className='object-contain scale-[80%]'
                      fill
                      src={storeLogoUrl || ''}
                    />
                  </div>
                </SmartLink>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    );
  }
);

export default ProductCard;

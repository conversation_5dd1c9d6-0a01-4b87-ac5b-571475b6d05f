import clsx from 'clsx';
import Image from 'next/image';
import React from 'react';
import ThemeButton from '../atoms/theme-btn';

const TransactionCard = ({
  imgUrl,
  title,
  amount,
  transactionType,
  date,
  time,
}: {
  imgUrl: string;
  title: string;
  transactionType: 'Pending' | 'Success';
  amount: string;
  date: string;
  time: string;
}) => {
  return (
    <div
      className='rounded-[6px] border-[0.5px] dark:border-[#353943] w-full h-[75px] lg:h-[95px] bg-white dark:bg-[#3E424C] px-[12px] lg:px-[18px] py-[6px] flex items-center'
      style={{ boxShadow: '0px 1px 5px 0px rgba(0, 0, 0, 0.07)' }}
    >
      <div className='flex justify-between items-center w-full'>
        <div className='flex'>
          <Image
            alt='img'
            className='max-w-[50px] max-h-[50px] lg:max-w-[80px] lg:max-h-[80px] w-full h-auto object-contain shrink-0'
            height={50}
            src={imgUrl}
            width={50}
          />

          <div className='ml-[15px] lg:ml-[25px] flex flex-col lg:flex-row lg:items-center'>
            <p className='text-[9px] sm:text-[10px] lg:text-xs font-semibold text-blackWhite truncate lg:w-[200px]'>
              {title}
            </p>
            <div className='hidden lg:inline-block w-[1px] h-[36px] bg-[#AEAEAE] dark:bg-[#62687A] ml-[10px] mr-[30px]' />
            <div>
              <div
                className={clsx(
                  transactionType === 'Success'
                    ? 'text-[#23A489]'
                    : 'text-[#FFC554]',
                  'mt-[8px] flex lg:m-auto lg:table truncate'
                )}
              >
                <span className='font-nexa text-[9px] sm:text-[10px] lg:text-xs font-black'>
                  {amount}
                </span>
                <span className='text-[8px] sm:text-[9px] lg:text-xs font-semibold ml-[7px] lg:ml-[15px]'>
                  {transactionType === 'Success'
                    ? 'Your Withdrawal is successful'
                    : 'Your Withdrawal was Pending'}
                </span>
              </div>
              <div className='mt-[5px] shrink-0 truncate flex text-[#8B8B8B] dark:text-[#A6ACBC] text-[7px] lg:text-xs font-normal font-nexa'>
                <span>{date}</span>
                <span className='ml-[10px] lg:ml-[15px]'>{time}</span>
              </div>
            </div>
          </div>
        </div>
        <ThemeButton
          className='bg-transparent !w-[62px] lg:!w-[105px] shrink-0 border-[1px] border-primary dark:border-white !text-primary
          dark:!text-white uppercase'
          onClick={() => console.log('')}
          text='Repeat'
        />
      </div>
    </div>
  );
};

export default TransactionCard;

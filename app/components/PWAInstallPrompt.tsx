'use client';

import type React from 'react';
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import InstallSVG from '../../public/icons/install.svg';
import { useIsIOSDevice } from '@/utils/custom-hooks';

interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed'; platform: string }>;
}

const PWAInstallPrompt: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] =
    useState<BeforeInstallPromptEvent | null>(null);
  const [showPrompt, setShowPrompt] = useState(false);

  const isPwa = () => {
    return ['fullscreen', 'standalone', 'minimal-ui'].some(
      (displayMode) =>
        window.matchMedia(`(display-mode: ${displayMode})`).matches
    );
  };

  const shouldShowPrompt = () => {
    const lastCancelTime = localStorage.getItem('lastPWAPromptCancelTime');
    if (!lastCancelTime) {
      return true;
    }

    const threeDaysInMs = 3 * 24 * 60 * 60 * 1000;
    const timeSinceLastCancel = Date.now() - Number.parseInt(lastCancelTime);
    return timeSinceLastCancel > threeDaysInMs;
  };

  useEffect(() => {
    if (isPwa() || !shouldShowPrompt()) {
      setShowPrompt(false);
      return;
    }
    const timer = setTimeout(() => setShowPrompt(true), 500);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    const handleBeforeInstallPrompt = (e: BeforeInstallPromptEvent) => {
      e.preventDefault();
      setDeferredPrompt(e);
      if (shouldShowPrompt()) {
        setShowPrompt(true);
      }
    };

    window.addEventListener(
      'beforeinstallprompt',
      handleBeforeInstallPrompt as any
    );

    return () => {
      window.removeEventListener(
        'beforeinstallprompt',
        handleBeforeInstallPrompt as any
      );
    };
  }, []);

  const handleInstallClick = () => {
    if (deferredPrompt) {
      deferredPrompt.prompt();
      deferredPrompt.userChoice.then((choiceResult) => {
        if (choiceResult.outcome === 'accepted') {
          console.log('User accepted the install prompt');
        } else {
          console.log('User dismissed the install prompt');
        }
        setDeferredPrompt(null);
        setShowPrompt(false);
      });
    }
  };

  const containerVariants = {
    hidden: {
      y: -100,
      opacity: 0,
      scale: 0.95,
    },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 30,
        mass: 1,
        staggerChildren: 0.1,
      },
    },
    exit: {
      y: -100,
      opacity: 0,
      scale: 0.95,
      transition: {
        duration: 0.3,
        ease: 'easeInOut',
      },
    },
  };

  const childVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 25,
      },
    },
  };

  const handleCancelClick = () => {
    setShowPrompt(false);
    localStorage.setItem('lastPWAPromptCancelTime', Date.now().toString());
  };

  const isIosDevice = useIsIOSDevice();
  if (!showPrompt || isIosDevice) {
    return null;
  }

  return (
    <AnimatePresence>
      {showPrompt && (
        <motion.div
          animate='visible'
          className='fixed top-0 left-0 right-0 z-[1000] p-4 flex justify-end'
          exit='exit'
          initial='hidden'
          variants={containerVariants}
        >
          <motion.div
            className='bg-gradient-to-r from-[#4735ce] to-[#8578ee] text-white rounded-xl shadow-2xl overflow-hidden max-w-md w-full backdrop-blur-lg border border-white/10'
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.2 },
            }}
          >
            <div className='px-4 py-3'>
              <motion.div
                className='flex items-center gap-4 mb-2'
                variants={childVariants}
              >
                <div className='relative'>
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      rotate: [0, 10, 0],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatType: 'reverse',
                    }}
                  >
                    <Image alt='install' className='w-6 h-6' src={InstallSVG} />
                  </motion.div>
                </div>
                <h3 className='text-base font-semibold'>Install Our App</h3>
              </motion.div>

              <motion.p
                className='text-white/90 mb-4 text-sm'
                variants={childVariants}
              >
                Get a faster, enhanced experience with our installable app!
              </motion.p>

              <motion.div
                className='flex items-center justify-end gap-3 text-sm'
                variants={childVariants}
              >
                <motion.button
                  className='px-4 py-1.5 text-white/70 hover:text-white transition-colors'
                  onClick={handleCancelClick}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Maybe Later
                </motion.button>
                <motion.button
                  className='px-4 py-1.5 bg-white text-primary rounded-lg font-medium shadow-lg'
                  onClick={handleInstallClick}
                  whileHover={{
                    scale: 1.05,
                    backgroundColor: '#f0f0f0',
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  Install Now
                </motion.button>
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PWAInstallPrompt;

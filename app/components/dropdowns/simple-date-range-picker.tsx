'use client';
import { useState, useRef, useCallback } from 'react';
import { DateP<PERSON>, <PERSON><PERSON>, Button } from 'antd';
import type { Dayjs } from 'dayjs';
import DateIcon from '../svg/date-icon';
import clsx from 'clsx';

interface DateRangePickerProps {
  rootClassName?: string;
  onChange?: (dates: { startDate?: Date; endDate?: Date } | null) => void;
}

function SimpleDateRangePicker({
  rootClassName,
  onChange,
}: DateRangePickerProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [startDate, setStartDate] = useState<Dayjs | null>(null);
  const [endDate, setEndDate] = useState<Dayjs | null>(null);

  // Use refs to store the dates that will be sent to the parent component
  const startDateRef = useRef<Date | undefined>(undefined);
  const endDateRef = useRef<Date | undefined>(undefined);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  // Function to clear dates
  const handleClear = useCallback(() => {
    console.log('Clear button clicked');

    // Reset local state
    setStartDate(null);
    setEndDate(null);
    startDateRef.current = undefined;
    endDateRef.current = undefined;

    // Close modal first
    setIsModalOpen(false);

    // Use setTimeout to ensure the modal is closed before notifying parent
    setTimeout(() => {
      // Notify parent component directly
      if (onChange) {
        console.log('Sending null to parent to clear date filters');
        onChange(null);
      }
    }, 50);
  }, [onChange]);

  const handleApply = useCallback(() => {
    console.log('Apply button clicked with dates:', {
      startDate: startDate?.format('YYYY-MM-DD') || 'null',
      endDate: endDate?.format('YYYY-MM-DD') || 'null',
    });

    // Update refs with current values
    if (startDate && endDate) {
      startDateRef.current = startDate.toDate();
      endDateRef.current = endDate.toDate();

      // Close modal first
      setIsModalOpen(false);

      // Directly call the callback with the current values
      if (onChange) {
        const dateRange = {
          startDate: startDate.toDate(),
          endDate: endDate.toDate(),
        };

        console.log('Sending date range to parent:', dateRange);
        onChange(dateRange);
      }
    } else {
      // If dates are not set, just close the modal
      setIsModalOpen(false);
    }
  }, [startDate, endDate, onChange]);

  const handleStartDateChange = useCallback((date: Dayjs | null) => {
    console.log('Start date changed:', date?.format('YYYY-MM-DD') || 'null');
    setStartDate(date);
  }, []);

  const handleEndDateChange = useCallback((date: Dayjs | null) => {
    console.log('End date changed:', date?.format('YYYY-MM-DD') || 'null');
    setEndDate(date);
  }, []);

  const disabledStartDate = useCallback(
    (current: Dayjs) => {
      return endDate ? current.isAfter(endDate, 'day') : false;
    },
    [endDate]
  );

  const disabledEndDate = useCallback(
    (current: Dayjs) => {
      return startDate ? current.isBefore(startDate, 'day') : false;
    },
    [startDate]
  );

  return (
    <>
      <button
        className={clsx(
          rootClassName,
          'overflow-hidden bg-white dark:bg-[#3D4049] h-[40px] w-[65px] lg:w-[92px] rounded-[5px] text-[12px] lg:text-[12px] text-[#1C132E] dark:text-white font-medium lg:font-semibold flex justify-evenly items-center lg:dark:bg-[#515662]'
        )}
        onClick={handleOpenModal}
        style={{ boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.07)' }}
        type='button'
      >
        <div>
          <DateIcon className='w-[15px] lg:w-[20px] text-[#7366D9] dark:text-white mr-4' />
        </div>
        <span>Date</span>
      </button>

      <Modal
        footer={[
          <Button key='clear' onClick={handleClear}>
            Clear
          </Button>,
          <Button key='cancel' onClick={handleCancel}>
            Cancel
          </Button>,
          <Button key='apply' onClick={handleApply} type='primary'>
            Apply
          </Button>,
        ]}
        onCancel={handleCancel}
        open={isModalOpen}
        title='Select Date Range'
      >
        <div className='space-y-4 mt-4'>
          <div className='space-y-2'>
            <label className='block text-sm font-medium' htmlFor='start-date'>
              Start Date
            </label>
            <DatePicker
              allowClear={true}
              className='w-full'
              disabledDate={disabledStartDate}
              format='DD-MM-YYYY'
              id='start-date'
              onChange={handleStartDateChange}
              value={startDate}
            />
          </div>

          <div className='space-y-2'>
            <label className='block text-sm font-medium' htmlFor='end-date'>
              End Date
            </label>
            <DatePicker
              allowClear={true}
              className='w-full'
              disabledDate={disabledEndDate}
              format='DD-MM-YYYY'
              id='end-date'
              onChange={handleEndDateChange}
              value={endDate}
            />
          </div>

          <div className='text-xs text-gray-500'>
            <p>Select a date range to filter your results</p>
          </div>
        </div>
      </Modal>
    </>
  );
}

export default SimpleDateRangePicker;

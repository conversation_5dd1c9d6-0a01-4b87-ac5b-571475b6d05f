import React from 'react';
import InfoSVG from '../svg/info-svg';
import SaveSVG, { ShareSVG } from '../svg/save';
import clsx from 'clsx';
import { RWebShare } from 'react-web-share';
import { APP_URL } from '@/config';

const defaultIcons = [
  <InfoSVG
    className='text-primary dark:text-[#A9AFC8] my-[15px] h-[13px]'
    key={1}
  />,
  <SaveSVG
    className='text-primary dark:text-[#A9AFC8] my-[14px] w-[10px] h-[12px]'
    fill={'transparent'}
    key={2}
  />,
  <ShareSVG
    className='text-primary dark:text-[#A9AFC8] my-[14px] w-[10px] h-[12px]'
    key={3}
  />,
];
const defaultItems = [
  {
    key: 1,
    value: 'Info',
  },
  {
    key: 2,
    value: 'Save',
  },
  {
    key: 3,
    value: 'Share',
  },
];
const OfferCardMenu = ({
  icons = defaultIcons,
  items = defaultItems,
  shareUrl,
  cardType = 'offer',
  rootClass,
  onClickItem,
  saved,
  hiddenItems = [], // New prop to specify which items to hide
}: {
  icons?: React.ReactElement[];
  items?: Array<{ key: number; value: string }>;
  rootClass?: string;
  shareUrl?: string;
  cardType?: 'giftcard' | 'store' | 'offer';
  onClickItem?: (key: number, value: string) => void;
  saved?: boolean;
  hiddenItems?: string[];
}) => {
  // const saveIndex = items.findIndex((item) => item.value === 'Save');

  // const modifiedIcons = icons.map((icon, index) => {
  //   if (index === saveIndex && saved) {
  //     return React.cloneElement(icon, {
  //       fill: '#A9AFC8',
  //     });
  //   }
  //   return icon;
  // });

  // Get modified icons while considering the hidden items
  const modifiedIcons = icons
    .map((icon, index) => {
      const itemValue = items[index]?.value; // Get the corresponding item value

      // Check if the current item is hidden
      if (hiddenItems.includes(itemValue)) {
        return null; // Don't render this icon
      }

      // Modify the "Save" icon based on saved state
      if (itemValue === 'Save' && saved) {
        return React.cloneElement(icon, {
          fill: '#A9AFC8',
        });
      }

      return icon; // Return the icon as is for non-hidden items
    })
    .filter(Boolean); // Remove null values

  // Filter items based on hiddenItems
  const filteredItems = items.filter(
    (item) => !hiddenItems.includes(item.value)
  );

  return (
    <div
      className={clsx(
        rootClass,
        'absolute top-[35px] right-[1px] z-[9] pt-[2px]'
      )}
    >
      <div
        className='rounded-[10px] overflow-hidden flex'
        style={{ boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.25)' }}
      >
        <div className='bg-[#F2F2F2] dark:bg-[#393B45] w-[30px] flex flex-col items-center'>
          {modifiedIcons.map((item) => item)}
        </div>
        <div className='max-w-[100px] min-w-[75px] bg-white dark:bg-[#3D404C]'>
          {filteredItems.map((item) => {
            return item.value === 'Share' ? (
              <RWebShare
                data={{
                  text:
                    cardType === 'giftcard'
                      ? 'The perfect gift idea! 🎁 Get a gift card with cashback on Indian Cashback. Surprise someone special! 👉🏻 '
                      : cardType === 'store'
                      ? 'Want to save some extra cash? 💰 \nIndian Cashback has amazing deals! 🎉  \nCheck out this awesome store with Indian Cashback 👉🏻'
                      : 'Want to save some extra cash? 💰 \nIndian Cashback has amazing deals! 🎉  \nCheck out this awesome offer with Indian Cashback 👉🏻 ',
                  url: (() => {
                    const url = encodeURI(`https://${APP_URL}${shareUrl}`);
                    return url || APP_URL;
                  })(),
                  title:
                    cardType === 'giftcard'
                      ? 'Share the giftcard link with your friends'
                      : cardType === 'store'
                      ? 'Share the store link with your friends!'
                      : 'Share the offer link with your friends!',
                }}
                key={item.key}
                onClick={() => onClickItem && onClickItem(item.key, item.value)}
              >
                <div className='my-[5px] py-[10px] text-blackWhite text-[10px] font-medium px-[10px] cursor-pointer hover:bg-[#F2F2F2] dark:hover:bg-[#454957f5]'>
                  {item.value}
                </div>
              </RWebShare>
            ) : (
              <div
                className='my-[5px] py-[10px] text-blackWhite text-[10px] font-medium px-[10px] cursor-pointer hover:bg-[#F2F2F2] dark:hover:bg-[#454957f5]'
                key={item.key}
                onClick={() => onClickItem && onClickItem(item.key, item.value)}
              >
                {item.value === 'Save' && saved ? 'Saved' : item.value}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default OfferCardMenu;

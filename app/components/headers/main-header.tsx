'use client';
import Image from 'next/image';
import CashbackSummary from '../landing/cashback-summary';
import noice from '@/public/img/noise.png';
import RightArrow from '../svg/right-arrow';
import {
  setGlobalSearchActive,
  setShowSearchLeftPanel,
} from '@/redux/slices/global-search-slice';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import SearchSVG from '../svg/search';
import { usePathname } from 'next/navigation';
import CategoriesDropdown from '../misc/categories-dropdown';
import { ArrowUp } from '../svg/arrow-up-down';
import WalletAddSVG from '../svg/wallet-add';
import PendingSVG from '../svg/pending';
import MenuBtnSVG from '../svg/menu-btn';
import { setShowLeftPanel } from '@/redux/slices/main-header-slice';
import LeftPanel from '@/app/(protected-pages)/user/left-panel';
import ThemeToggleButton from '../atoms/theme-toggle-button';
import clsx from 'clsx';
import LoginLogoutSVG from '../svg/login-logout-icon';
import GlobalSearchDesktopDropdown from '../landing/search/global-search-desktop-dropdown';
import {
  setIsUserLogin,
  setLoginModalOpen,
  setUserDetails,
} from '@/redux/slices/auth-slice';
import { useEffect } from 'react';
import type {
  CategoryResponse,
  GetUserProfileResponseItem,
  SubCategoriesByCategoryResponse,
} from '@/services/api/data-contracts';
import {
  setActiveCategoryLoading,
  setSubCategoriesList,
} from '@/redux/slices/categories-list-slice';
import { BASE_URL } from '@/config';
import fetchWrapper from '@/utils/fetch-wrapper';
import type { PromiseStatus } from '@/types/global-types';
import SmartLink from '../common/smart-link';
import { LinkType } from '@/utils/link-utils';
import Link from 'next/link';
import ApprovedSVG from '../svg/approved';

const MainHeader = ({
  categoriesData,
  promiseStatus,
  accessToken,
}: {
  categoriesData: {
    categories: CategoryResponse[];
    subCategories: SubCategoriesByCategoryResponse;
  };
  promiseStatus: PromiseStatus;
  accessToken: string | undefined;
}) => {
  const { isUserLogin, userDetails } = useAppSelector((state) => state.auth);
  const { isGlobalSearchActive } = useAppSelector(
    (state) => state.globalSearch
  );
  const { isShowLeftPanel } = useAppSelector((state) => state.mainHeader);
  const pathname = usePathname();
  const dispatch = useAppDispatch();
  const { activeCategory, subCategoriesList } = useAppSelector(
    (state) => state.categoriesList
  );

  useEffect(() => {
    if (accessToken) {
      dispatch(setIsUserLogin(true));
    }
  }, [dispatch, accessToken]);

  useEffect(() => {
    async function getUserData() {
      try {
        const result = await fetchWrapper<GetUserProfileResponseItem>(
          '/api/proxy/users/me'
        );
        console.log('🚀 ~ getUserData ~ result:', result);
        dispatch(
          setUserDetails({
            ...result,
            mobile: result.mobile?.toString(),
            avatar:
              result.avatar ||
              `https://ui-avatars.com/api/?name=${
                result?.name || 'John Doe'
              }&background=random&rounded=true&format=png`,
          })
        );
        return result;
      } catch (error) {
        // Auth errors are handled globally by fetchWrapper
        // Just log non-auth errors
        console.error('Failed to fetch user data in header:', error);
      }
    }
    if (isUserLogin) {
      getUserData();
      // set user details in store
    }
  }, [isUserLogin, dispatch]);

  const isCategoryLoaded = subCategoriesList[activeCategory] !== undefined;

  useEffect(() => {
    async function getSubCategoriesByCategoryIdData() {
      if (!activeCategory || !BASE_URL || isCategoryLoaded) {
        return;
      }
      dispatch(setActiveCategoryLoading(true));
      const data = await fetchWrapper<SubCategoriesByCategoryResponse>(
        `/api/proxy/context/category/sub-category${activeCategory}`,
        {
          method: 'GET',
          excludeCredentials: true,
        }
      );
      dispatch(setActiveCategoryLoading(false));
      dispatch(
        setSubCategoriesList({
          categoryId: activeCategory,
          subCategoriesList: data,
        })
      );
      return data;
    }
    if (activeCategory) {
      getSubCategoriesByCategoryIdData();
    }
  }, [activeCategory, isCategoryLoaded, dispatch]);

  if (promiseStatus === 'rejected') {
    console.log('promise rejected');
    return null;
  }

  const searchButtonHandler = () => {
    if (window && window.innerWidth < 1024) {
      dispatch(setShowSearchLeftPanel(true));
    } else {
      dispatch(setGlobalSearchActive(true));
    }
  };

  if (pathname === '/redirecting') {
    return null;
  }

  return (
    <header className='relative lg:h-[104px] z-[999]'>
      {/* ------------Mobile Header-------------- */}
      {pathname === '/' && (
        <div className='landingMobileHeader lg:hidden'>
          <div className='flex items-center h-[65px] px-[20px] mainHeader fixed t-0 l-0 w-full z-[999]'>
            <button
              className={clsx(
                isUserLogin && 'bg-[#3A2CAB]',
                'flex gap-x-[7px] items-center justify-evenly px-[10px] h-[43px] rounded-[6px] shrink-0 transition-all duration-300 hover:bg-opacity-90 hover:shadow-md active:scale-80'
              )}
              onClick={() => dispatch(setShowLeftPanel(!isShowLeftPanel))}
              type='button'
            >
              <MenuBtnSVG className='text-white' />
              {isUserLogin && (
                <Image
                  alt=''
                  className='rounded-full'
                  height={29}
                  priority={true}
                  src={userDetails?.avatar || '/temp/profile.png'}
                  width={28}
                />
              )}
            </button>
            <SmartLink
              className='ml-[13px]'
              href={'/'}
              linkType={LinkType.INTERNAL}
            >
              {/* <Image
                alt=''
                className='h-[26px] w-[36px] -mb-[10px]'
                height={29}
                priority={true}
                src='/svg/icb-logo.svg'
                width={28}
              />
              <span className='text-[4px] text-white font-nexa font-normal'>
                Indian
              </span>
              <span className='text-[4px] text-white font-nexa font-normal'>
                CashBack
              </span> */}

              <Image
                alt='logo'
                className='h-[70px] w-[40px]'
                height={150}
                priority={true}
                src='/svg/icb-logo.svg'
                title='logo'
                width={100}
              />
            </SmartLink>
            <div className='ml-auto'>
              <ThemeToggleButton />
            </div>
            <button
              className='ml-3 transition-transform duration-300 rounded-md hover:bg-white/10 p-2 active:scale-95'
              onClick={() => dispatch(setShowSearchLeftPanel(true))}
              type='button'
            >
              <SearchSVG className='text-white w-[21px] h-[19px] ml-auto' />
            </button>
          </div>
          <div className='h-[65px]' />
          {isUserLogin && (
            <>
              <div className='curvedHeader' />
              <CashbackSummary />
            </>
          )}
          <LeftPanel />
        </div>
      )}
      {/* ------------Desktop Header------------ */}
      <div className='desktopHeader hidden lg:block fixed top-0 w-full h-[104px]'>
        <div className='h-[63px] bg-primary flex header-container justify-between items-center'>
          <Image
            alt='logo'
            className='w-full h-[63px] absolute top-0 left-0 z-0 hidden'
            src={noice}
          />
          <Link href={'/'}>
            <div className='flex gap-[8px] items-center cursor-pointer transition-transform duration-300 active:scale-95'>
              <Image
                alt='logo'
                className='h-[90px] w-[200px]'
                height={140}
                priority={true}
                src='/svg/icb-logo-full.svg'
                title='logo'
                width={260}
              />
            </div>
          </Link>
          <div className='flex items-center'>
            {!isGlobalSearchActive && (
              <>
                <button
                  className='xl:hidden cursor-pointer transition-transform duration-300 p-3 rounded-md hover:bg-white/10 active:scale-95'
                  onClick={searchButtonHandler}
                  type='button'
                >
                  <SearchSVG className='text-white w-[21px] h-[19px] shrink-0 mx-auto' />
                </button>
                <div className='relative hidden xl:block cursor-pointer'>
                  <input
                    aria-label='Search'
                    className='relative outline-none border-none grow w-[300px] h-[35px] text-[10px] bg-white dark:bg-black dark:text-white lg:text-xs font-medium pl-[20px] rounded-[5px] pr-[45px] placeholder:text-gray-500 dark:placeholder:text-gray-400'
                    onClick={searchButtonHandler}
                    placeholder='Search Product, coupon, deals, cashback...etc'
                    readOnly
                  />
                  <SearchSVG className='absolute right-3 top-1/2 -translate-y-1/2 text-black dark:text-white w-[21px] h-[19px] shrink-0 mx-auto' />
                </div>
                {isUserLogin ? (
                  <div className='accountSummaryDesktop shrink-0 ml-[30px] flex h-[54px] border-[1px] rounded-[5px]'>
                    <div className='flex divide-x-[1px] divide-[#E8E5FF] py-2'>
                      <div className='h-full flex text-white px-[18px]'>
                        <span className='pe-2 pt-1'>
                          <WalletAddSVG className='text-white w-[15px]' />
                        </span>
                        <div className=''>
                          <p className='text-[14px] font-bold font-nexa'>
                            ₹{' '}
                            {Number(userDetails.balance).toLocaleString(
                              'en-IN',
                              {
                                minimumFractionDigits: 0,
                                maximumFractionDigits: 2,
                              }
                            )}
                          </p>
                          <p className='text-[8px]'>Ready to withdraw</p>
                        </div>
                      </div>
                      <div className=' h-full flex text-white px-[18px]'>
                        <span className='pe-2 pt-1'>
                          <PendingSVG className='text-white w-[15px]' />
                        </span>
                        <div className='text-center'>
                          <p className='text-[14px] font-bold font-nexa'>
                            {userDetails.pendingCount}
                          </p>
                          <p className='text-[8px]'>Pending</p>
                        </div>
                      </div>
                      <div className='h-full flex text-white pl-[18px] pr-[34px]'>
                        <span className='pe-2 pt-1'>
                          <ApprovedSVG className='text-white w-[15px]' />
                        </span>
                        <div className=' text-center'>
                          <p className=' text-[14px] font-bold font-nexa'>
                            {userDetails.confirmedCount}
                          </p>
                          <p className='text-[8px] '>Confirmed</p>
                        </div>
                      </div>
                    </div>
                    <SmartLink
                      href={'/my-profile'}
                      linkType={LinkType.INTERNAL}
                    >
                      <div className='rounded-r-[3px] col-span-1 h-full bg-white lg:flex justify-around items-center pl-[15px] pr-[18px] cursor-pointer transition-all duration-300 hover:bg-gray-100'>
                        <Image
                          alt='profile'
                          className='w-[35px] h-[35px] rounded-full'
                          height={35}
                          src={userDetails?.avatar || '/temp/profile.png'}
                          width={35}
                        />
                        <RightArrow className='text-black ml-[12px] w-[12px]' />
                      </div>
                    </SmartLink>
                  </div>
                ) : (
                  <button
                    className='flex items-center justify-center border border-white px-4 py-3 rounded-md text-white text-xs font-medium ml-4 transition-all duration-300 hover:bg-white hover:bg-white/10 active:scale-95'
                    onClick={() => dispatch(setLoginModalOpen(true))}
                    type='button'
                  >
                    <LoginLogoutSVG className='text-white w-[16px] mr-[10px]' />{' '}
                    Login/Sign Up
                  </button>
                )}
              </>
            )}
            {isGlobalSearchActive && <GlobalSearchDesktopDropdown />}
          </div>
        </div>

        {/* --------------------------Desktop sub-header navbar------------------- */}
        <div className='flex justify-between items-center h-[41px] text-[12px] bg-[#FFC554] header-container'>
          {/* <div className='w-full max-w-[1280px] mx-[60px] h-full min-[1440px]:mx-auto flex justify-center relative'> */}
          <div className='h-full w-[230px] xl:w-[280px] flex justify-between items-center bg-[#FFD27B] px-[15px] group cursor-pointer hover:bg-[#FFD99B] transition-colors duration-300'>
            <div className='flex-center'>
              <Image
                alt='icon'
                className='w-auto h-auto'
                height={25}
                quality={100}
                src='/svg/categories/all.svg'
                width={25}
              />
              <span className='ml-[17px] font-semibold text-sm text-black'>
                Categories
              </span>
            </div>

            <ArrowUp className='rotate-90 group-hover:rotate-180 transition-all duration-300 text-black' />

            <CategoriesDropdown
              data={categoriesData}
              promiseStatus={promiseStatus}
              rootClass='group-hover:translate-y-0'
            />
          </div>
          <div className='flex h-full text-black text-xs gap-x-10 relative'>
            <SmartLink
              className='relative group'
              href='/'
              linkType={LinkType.INTERNAL}
            >
              <div className='mt-[12px] flex justify-center items-center gap-2 relative cursor-pointer transition-transform duration-300 group-hover:translate-y-[-2px]'>
                <p className='font-semibold'>Cashback</p>
              </div>
              {pathname !== '/share-and-earn' && (
                <span className=' bg-[#483AB2]  w-[95px] h-[5px] rounded-t-md absolute bottom-0 left-[-16px] transition-all duration-300' />
              )}
            </SmartLink>
            {/* <Link href='/giftcards' target='_blank'>
                <div className='mt-[12px] flex justify-center items-center gap-2 relative cursor-pointer'>
                  <p>Gift Card</p>
                </div>
              </Link> */}
            <SmartLink
              className='relative group'
              href='https://card.indiancashback.com'
              linkType={LinkType.EXTERNAL}
              target='_blank'
            >
              <div className='mt-[12px] flex justify-center mr-6 items-center gap-2 text-black relative cursor-pointer group transition-transform duration-300 hover:translate-y-[-2px] !pointer-events-none'>
                <div className='flex gap-2'>
                  <p className='shrink-0'>ICB Card</p>
                </div>

                <div className='bg-[#5E50CC] rounded-[4px] w-[34px] h-[20px] shrink-0 text-center text-[8px] font-normal text-white absolute -right-[45px] bottom-[0px] transition-all duration-300 group-hover:bg-[#4A3EB8]'>
                  BETA
                </div>
              </div>
            </SmartLink>
            <SmartLink
              className='relative group'
              href='/share-and-earn'
              linkType={LinkType.INTERNAL}
            >
              <div className='mt-[12px] flex justify-center items-center gap-2 relative cursor-pointer transition-transform duration-300 group-hover:translate-y-[-2px]'>
                Share & Earn
              </div>
              {pathname === '/share-and-earn' && (
                <span className=' bg-[#483AB2]  w-[105px] h-[5px] rounded-t-md absolute bottom-0 left-[-15px] transition-all duration-300' />
              )}
            </SmartLink>
            <SmartLink
              className='relative group'
              href='https://blog.indiancashback.com/en'
              linkType={LinkType.EXTERNAL}
              target='_blank'
            >
              <div className='mt-[12px] flex justify-center items-center gap-2 relative cursor-pointer transition-transform duration-300 group-hover:translate-y-[-2px]'>
                Blogs
              </div>
            </SmartLink>
            {/* <div className="flex justify-center items-center gap-2  hidden">
            <Image src={links} alt="links" className="" />
            <p>All Links</p>
          </div> */}
          </div>
          <div className='h-full flex justify-end items-center min-w-[230px]'>
            <Link
              className='mr-4 w-auto h-8 hover:scale-[1.02] transition-all duration-300 bg-primary text-white pl-3 pr-4 py-2 rounded-md flex gap-2 justify-center items-center'
              href='/referral'
            >
              <div className='coin-loader' />
              Refer & Earn
            </Link>
            <ThemeToggleButton />
          </div>
        </div>
        {/* </div> */}
      </div>
    </header>
  );
};

export default MainHeader;

'use client';

import Image from 'next/image';
import { determineLinkType, type LinkType } from '@/utils/link-utils';
import SmartLink from './smart-link';

interface SmartQuickLinkProps {
  imgURL: string;
  title: string;
  href: string;
  linkType?: LinkType;
}

/**
 * SmartQuickLink - A version of QuickLink that handles SEO-friendly link attributes
 *
 * This component is similar to the existing QuickLink but uses the SmartLink component
 * to add appropriate rel attributes based on the destination URL.
 *
 * @example
 * // Internal link
 * <SmartQuickLink
 *   imgURL="/img/icon.png"
 *   title="My Title"
 *   href="/internal-page"
 * />
 *
 * @example
 * // External sponsored link
 * <SmartQuickLink
 *   imgURL="/img/partner.png"
 *   title="Partner Site"
 *   href="https://partner.com"
 *   linkType={LinkType.EXTERNAL_SPONSORED}
 * />
 */
const SmartQuickLink = ({
  imgURL,
  title,
  href,
  linkType,
}: SmartQuickLinkProps) => {
  const type = determineLinkType(href, linkType);

  return (
    <SmartLink href={href} linkType={type}>
      <div className='flex flex-col gap-y-[2px] items-center justify-center shrink-0 cursor-pointer transition-all duration-300 hover:scale-105'>
        <div className='relative transition-all duration-300 hover:shadow-sm rounded-full p-1'>
          <Image
            alt='Category img'
            className='w-8 h-8 transition-opacity duration-200 hover:opacity-100'
            height={40}
            src={imgURL}
            style={{ opacity: 0.9 }}
            width={40}
          />
        </div>
        <div className='whitespace-nowrap leading-none'>
          <span className='text-xs font-semibold capitalize text-black dark:text-white mr-[8px] whitespace-nowrap transition-colors duration-300'>
            {title}
          </span>
        </div>
      </div>
    </SmartLink>
  );
};

export default SmartQuickLink;

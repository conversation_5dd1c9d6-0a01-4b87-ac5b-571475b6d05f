'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import clsx from 'clsx';
import { useRouter } from 'next/navigation';
import {
  determineLinkType,
  getRelAttribute,
  LinkType,
  isExternalUrl,
} from '@/utils/link-utils';

interface SmartLinkContainerProps {
  iconUrl: string;
  title: string;
  caption: string;
  notificationCount?: number;
  iconClass?: string;
  rootClass?: string;
  href: string;
  linkType?: LinkType;
}

/**
 * SmartLinkContainer - A version of LinkContainer that handles SEO-friendly link attributes
 *
 * This component is similar to the existing LinkContainer but uses the link utilities
 * to add appropriate rel attributes based on the destination URL.
 *
 * @example
 * // Internal link
 * <SmartLinkContainer
 *   iconUrl="/svg/icon.svg"
 *   title="My Title"
 *   caption="My caption text"
 *   href="/internal-page"
 * />
 *
 * @example
 * // External sponsored link
 * <SmartLinkContainer
 *   iconUrl="/svg/icon.svg"
 *   title="Partner Site"
 *   caption="Visit our partner"
 *   href="https://partner.com"
 *   linkType={LinkType.EXTERNAL_SPONSORED}
 * />
 */
const SmartLinkContainer = ({
  iconUrl,
  title,
  caption,
  notificationCount,
  iconClass,
  rootClass,
  href,
  linkType,
}: SmartLinkContainerProps) => {
  const router = useRouter();
  const type = determineLinkType(href, linkType);
  const rel = getRelAttribute(type);
  const isExternal = isExternalUrl(href);

  const handleClick = () => {
    if (isExternal) {
      // For external links, open in new tab
      window.open(href, '_blank', 'noopener');
    } else {
      // For internal links, use router
      router.push(href);
    }
  };

  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className={clsx(
        rootClass,
        'flex lg:flex-col lg:flex-center pl-[13px] py-[15px] pr-[18px] lg:p-[10px] lg:w-[218px] lg:min-h-[154px] bg-white dark:bg-[#3E424C] rounded-[6px]'
      )}
      data-href={href}
      data-link-type={type}
      data-rel={rel}
      initial={{ opacity: 0, y: 20 }}
      onClick={handleClick}
      style={{ boxShadow: '0px 1px 5px 0px rgba(0, 0, 0, 0.07)' }}
      transition={{ duration: 0.4 }}
      whileHover={{
        scale: 1.03,
        boxShadow: '0px 5px 15px rgba(0, 0, 0, 0.1)',
        transition: { duration: 0.3 },
      }}
      whileTap={{ scale: 0.97 }}
      // Add data attributes for debugging and testing
    >
      <motion.div
        animate={{ scale: 1 }}
        initial={{ scale: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        whileHover={{ rotate: 5, scale: 1.1, transition: { duration: 0.2 } }}
      >
        <Image
          alt='icon'
          className={iconClass}
          height={30}
          src={iconUrl}
          width={30}
        />
      </motion.div>
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        className='ml-[12px] lg:ml-0 lg:mt-[13px] flex flex-col lg:flex-center'
        initial={{ opacity: 0, x: -10 }}
        transition={{ duration: 0.4, delay: 0.3 }}
      >
        <div className='flex'>
          <motion.h4
            animate={{ opacity: 1 }}
            className='text-[11px] lg:text-xs font-medium text-blackWhite'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            {title}
          </motion.h4>
          {notificationCount && (
            <div className='ml-[5px] w-[14px] h-[14px] rounded-full bg-[#FF5C5C] flex-center text-[8px] text-white'>
              {notificationCount}
            </div>
          )}
        </div>
        <motion.p
          animate={{ opacity: 1 }}
          className='text-[9px] lg:text-[10px] text-[#AEB5C9] dark:text-[#63697A] mt-[3px] lg:text-center'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          {caption}
        </motion.p>
      </motion.div>
    </motion.div>
  );
};

export default SmartLinkContainer;

import React, { useState, useEffect } from 'react';

interface OfferCountdownProps {
  duration: string | number; // Duration in seconds, milliseconds, or ISO date string
  className?: string;
  label?: string;
  animate?: boolean;
  onExpire?: () => void;
}

interface TimeUnits {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
}

const OfferCountdown: React.FC<OfferCountdownProps> = ({
  duration,
  className = '',
  label = 'Ends in',
  animate = true,
  onExpire,
}) => {
  const [timeLeft, setTimeLeft] = useState<TimeUnits>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });
  const [isExpired, setIsExpired] = useState(false);

  // Parse different duration formats to get total seconds
  const parseDuration = (dur: string | number): number => {
    if (typeof dur === 'number') {
      return dur;
    }

    if (typeof dur === 'string') {
      // Try to parse ISO date string or timestamp
      const timestamp = new Date(dur).getTime();
      if (!isNaN(timestamp)) {
        return Math.max(0, Math.floor((timestamp - Date.now()) / 1000));
      }

      // Try to parse duration string like "2d 5h 30m" or "7200" (seconds)
      if (/^\d+$/.test(dur)) {
        return parseInt(dur);
      }

      // Parse formatted duration string
      let totalSeconds = 0;
      const daysMatch = dur.match(/(\d+)\s*d/i);
      const hoursMatch = dur.match(/(\d+)\s*h/i);
      const minutesMatch = dur.match(/(\d+)\s*m/i);
      const secondsMatch = dur.match(/(\d+)\s*s/i);

      if (daysMatch) {
        totalSeconds += parseInt(daysMatch[1]) * 24 * 60 * 60;
      }
      if (hoursMatch) {
        totalSeconds += parseInt(hoursMatch[1]) * 60 * 60;
      }
      if (minutesMatch) {
        totalSeconds += parseInt(minutesMatch[1]) * 60;
      }
      if (secondsMatch) {
        totalSeconds += parseInt(secondsMatch[1]);
      }

      return totalSeconds;
    }

    return 0;
  };

  // Convert seconds to time units
  const secondsToTimeUnits = (totalSeconds: number): TimeUnits => {
    if (totalSeconds <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }

    const days = Math.floor(totalSeconds / (24 * 60 * 60));
    const hours = Math.floor((totalSeconds % (24 * 60 * 60)) / (60 * 60));
    const minutes = Math.floor((totalSeconds % (60 * 60)) / 60);
    const seconds = totalSeconds % 60;

    return { days, hours, minutes, seconds };
  };

  // Initialize and update countdown
  useEffect(() => {
    let totalSeconds = parseDuration(duration);
    setTimeLeft(secondsToTimeUnits(totalSeconds));

    const timer = setInterval(() => {
      totalSeconds -= 1;

      if (totalSeconds <= 0) {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
        setIsExpired(true);
        clearInterval(timer);
        onExpire?.();
        return;
      }

      setTimeLeft(secondsToTimeUnits(totalSeconds));
    }, 1000);

    return () => clearInterval(timer);
  }, [duration, onExpire]);

  // Format time unit with leading zero
  const formatUnit = (value: number): string =>
    value.toString().padStart(2, '0');

  // Create countdown string
  const createCountdownString = (): string => {
    if (isExpired) {
      return 'EXPIRED';
    }

    const parts = [];
    if (timeLeft.days >= 0) {
      parts.push(`${timeLeft.days}d`);
    }
    if (timeLeft.hours >= 0) {
      parts.push(`${formatUnit(timeLeft.hours)}h`);
    }
    if (timeLeft.minutes >= 0) {
      parts.push(`${formatUnit(timeLeft.minutes)}m`);
    }
    if (timeLeft.seconds >= 0 || parts.length === 0) {
      parts.push(`${formatUnit(timeLeft.seconds)}s`);
    }

    return parts.join(':');
  };

  const animateClass = animate && !isExpired ? 'animate-pulse' : '';
  const baseClasses =
    'text-[11px] sm:text-xs text-[#FF3737] flex items-center justify-center mt-[25px]';
  const combinedClasses = `${baseClasses} ${animateClass} ${className}`.trim();

  return (
    <div className={combinedClasses}>
      <span className='mr-1'>{label} :</span>
      <span className='font-mono font-semibold'>{createCountdownString()}</span>
    </div>
  );
};

export default OfferCountdown;

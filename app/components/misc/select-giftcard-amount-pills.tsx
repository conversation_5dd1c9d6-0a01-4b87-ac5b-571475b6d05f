import React from 'react';
import PillButton from '../atoms/pills';
import clsx from 'clsx';
import { GiftCardAmountsList } from '@/types/global-types';

const SelectGiftcardAmount = ({
  rootClassName,
  cardsList,
  addCustomAmount,
  addGiftCardAmount,
}: {
  rootClassName?: string;
  cardsList: GiftCardAmountsList[];
  addCustomAmount: () => void;
  addGiftCardAmount: (value: GiftCardAmountsList) => void;
}) => {
  return (
    <div
      className={clsx(
        rootClassName,
        'flex flex-col lg:flex-row items-start lg:items-center lg:gap-x-[40px] gap-y-[14px] my-[15px] lg:my-[20px] px-[16px] lg:px-[40px]'
      )}
    >
      <span className='font-pat text-[12px] font-normal lg:text-[14px] text-blackWhite shrink-0'>
        Select An Amount
      </span>
      <div className='w-full flex overflow-auto lg:flex-row-reverse gap-x-[10px] lg:gap-x-[20px] pr-[20px] customScrollbar'>
        <div
          className='h-[35px] px-[15px] text-[8px] lg:text-xs font-medium shrink-0 rounded-[20px] bg-primary flex-center cursor-pointer text-white'
          onClick={() => addCustomAmount()}
        >
          Add a Custom Amount
        </div>
        {cardsList && (
          <div className='flex gap-x-[10px] lg:gap-x-[20px] shrink-0'>
            {cardsList.map((item, index) => (
              <PillButton
                className='!font-semibold !font-nexa pt-[5px]'
                isSelected={item?.isSelected}
                key={index}
                onClick={() => addGiftCardAmount(item)}
                text={`₹ ${item?.amount}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SelectGiftcardAmount;

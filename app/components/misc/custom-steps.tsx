import long_arrow from '@/public/svg/long-arrow.svg';
import Image from 'next/image';
import React from 'react';

function CustomSteps({
  currentStep,
  steps,
}: {
  currentStep: number;
  steps: Array<{ step: number; content: string }>;
}) {
  return (
    <div className='flex justify-center items-start lg:items-center w-full pt-5 lg:py-0 gap-[7px]'>
      {steps.map((item, index) => (
        <React.Fragment key={index}>
          <div className='lg:w-24 items-center w-min flex-col flex gap-2  '>
            <span
              className={` ${
                currentStep >= item.step && 'bg-[#FFC554] !text-black'
              }  lg:w-10 lg:h-10 h-[30px] w-[30px] rounded-full mx-auto border-[2px] border-[#FFC554]  flex justify-center items-center text-xs lg:text-lg text-blackWhite font-bold  `}
            >
              {item?.step}
            </span>
            <p
              className={`lg:text-[10px] w-min lg:w-max text-[10px] font-semibold text-center transition-all duration-200 ${
                currentStep === item?.step
                  ? 'text-[#1A1A1F] dark:text-white opacity-100' // Current step - fully visible
                  : currentStep > item?.step
                  ? 'text-[#4F4F4F] dark:text-[#8C94A7] opacity-80' // Completed steps - muted
                  : 'text-[#999] dark:text-[#666] opacity-60' // Future steps - more muted
              } `}
            >
              {item?.content}
            </p>
          </div>
          {steps[steps.length - 1].step > item.step && (
            <Image
              alt='arrow'
              className='w-[23px] lg:w-auto mt-[10px] lg:-mt-4 '
              src={long_arrow}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  );
}

export default CustomSteps;

import clsx from 'clsx';
import React from 'react';

const RatingStars = ({
  rating,
  starClassName,
  wrapperClassName,
}: {
  rating: number;
  starClassName?: string;
  wrapperClassName?: string;
}) => {
  return (
    <div className={clsx(wrapperClassName, 'flex gap-x-[5px]')}>
      {[...Array(5)].map((item, index) => (
        <svg
          className={starClassName}
          fill='none'
          height='13'
          key={index}
          viewBox='0 0 14 13'
          width='14'
          xmlns='http://www.w3.org/2000/svg'
        >
          <g clipPath='url(#clip0_0_58185)'>
            <path
              d='M6.99984 3.64476L10.6048 1.62435L9.64817 5.43227L12.8332 7.99435L8.639 8.32477L6.99984 11.916L5.36067 8.32477L1.1665 7.99435L4.3515 5.43227L3.39484 1.62435L6.99984 3.64476Z'
              fill={index + 1 <= rating ? '#FDC800' : '#B8B8B8'}
            />
          </g>
          <defs>
            <clipPath id='clip0_0_58185'>
              <rect
                fill='currentColor'
                height='13'
                transform='matrix(1 0 0 -1 0 13)'
                width='14'
              />
            </clipPath>
          </defs>
        </svg>
      ))}
    </div>
  );
};

export default RatingStars;

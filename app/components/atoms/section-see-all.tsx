import React from 'react';
import RoundArrow from '../svg/round-arrow';
import clsx from 'clsx';

const SectionSeeAllBtn = ({
  onClick,
  text,
  isMobile = true,
}: {
  onClick?: () => void;
  text?: string;
  isMobile?: boolean;
}) => {
  return (
    <div
      className={clsx(
        isMobile ? 'lg:hidden' : 'hidden lg:flex',
        'h-[40px] mt-[13px] lg:mt-[22px] w-full bg-white dark:bg-[#1E2025] lg:!bg-transparent flex justify-end items-center rounded-b-[10px]'
      )}
    >
      <button
        className={clsx(
          'min-w-[93px] min-h-[30px] lg:dark:bg-[#5F6575] text-[#292B31] dark:text-white hover:scale-105 transition-transform duration-200 hover:font-medium shrink-0 drop-shadow flex items-center justify-center gap-x-[9px] text-xs px-2 font-medium rounded-[4px] lg:mt-[14px] cursor-pointer '
        )}
        onClick={onClick}
        type='button'
      >
        <span>{text ? text : 'See All'}</span>
        <RoundArrow />
      </button>
    </div>
  );
};

export default SectionSeeAllBtn;

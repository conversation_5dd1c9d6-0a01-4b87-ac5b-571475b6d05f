import React from 'react';
import CrossSVG from '../svg/cross';
import { motion } from 'framer-motion';

const ClearAllBtn = ({
  style,
  onClick,
}: {
  style?: object;
  onClick?: () => void;
}) => {
  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className='w-[69px] h-[24px] flex items-center justify-center gap-x-[10px] rounded-[5px] bg-[#E8E8E8] dark:bg-[#44474E] cursor-pointer'
      exit={{ opacity: 0, y: -5 }}
      initial={{ opacity: 0, y: 5 }}
      onClick={onClick}
      style={style}
      transition={{ type: 'spring', stiffness: 400, damping: 17 }}
      whileHover={{ scale: 1.05, backgroundColor: 'rgba(232, 232, 232, 0.8)' }}
      whileTap={{ scale: 0.95 }}
    >
      <motion.span
        animate={{ opacity: 1 }}
        className='text-blackWhite text-[8px] font-normal'
        initial={{ opacity: 0 }}
        transition={{ delay: 0.1 }}
      >
        Clear All
      </motion.span>
      <motion.div
        animate={{ opacity: 1, rotate: 0 }}
        initial={{ opacity: 0, rotate: 45 }}
        transition={{ delay: 0.2 }}
      >
        <CrossSVG className='w-[6px] text-blackWhite' />
      </motion.div>
    </motion.div>
  );
};

export default ClearAllBtn;

'use client';
import Image from 'next/image';
import React, { useState } from 'react';
import { clsx } from 'clsx';
import { AutoComplete } from 'antd';
import { RegisterOptions } from 'react-hook-form';

const InputFieldWithIcon = ({
  name,
  iconUrl,
  placeholder,
  isDisabled,
  validationSchema,
  inputType = 'text',
  defaultValue,
  register,
}: {
  name?: string;
  iconUrl: string;
  placeholder: string;
  isDisabled?: boolean;
  register?: any;
  inputType?: 'password' | 'text' | 'number';
  defaultValue?: string | number;
  validationSchema?: RegisterOptions;
}) => {
  return (
    <div className='relative flex w-full max-w-[457px] h-[41px] lg:h-[50px] rounded-[5px] bg-white dark:bg-body border-[1px] border-primary overflow-hidden'>
      <div className='w-[33px] lg:w-[43px] shrink-0 flex-center border-r-[1px] border-primary'>
        <Image
          alt='icon'
          className='w-[15px] h-[15px]'
          height={15}
          src={iconUrl}
          width={15}
        />
      </div>
      <input
        className={clsx(
          isDisabled && 'cursor-not-allowed',
          'grow h-full outline-none px-[13px] bg-transparent text-blackWhite text-[11px] lg:text-xs font-light'
        )}
        defaultValue={defaultValue}
        disabled={isDisabled}
        name={name}
        placeholder={placeholder || 'Enter'}
        type={inputType}
        {...(register ? register(name, validationSchema) : {})}
      />
    </div>
  );
};

export const InputFieldWithIcon2 = ({
  iconUrl,
  placeholder,
  rootClass,
  label,
  submitText,
  value,
  onChange,
  onSubmit,
}: {
  value?: string;
  iconUrl: string;
  placeholder: string;
  rootClass?: string;
  label?: string;
  submitText?: string;
  onChange?: (input: string) => void;
  onSubmit: () => void;
}) => {
  const [isDisabled, setDisabled] = useState(true);
  return (
    <div className={rootClass}>
      {label && (
        <span className='text-[#767676] dark:text-white text-[8px] sm:text-[9px] lg:text-[12px] font-medium ml-[12px]'>
          {label}
        </span>
      )}
      <div
        className={clsx(
          isDisabled && '!bg-[#DDDDDD] dark:!bg-[#44454af5] cursor-not-allowed',
          'relative flex w-full max-w-[457px] h-[40px] lg:h-[45px] rounded-[5px] bg-white dark:bg-body border-[1px] border-primary overflow-hidden'
        )}
      >
        <div className='w-[33px] lg:w-[43px] shrink-0 flex-center border-r-[1px] border-primary'>
          <Image
            alt='icon'
            className='w-[15px] h-[15px] !stroke-primary'
            height={15}
            src={iconUrl}
            width={15}
          />
        </div>
        <input
          className={clsx(
            isDisabled && 'cursor-not-allowed',
            'grow h-full outline-none px-[13px] bg-transparent text-blackWhite text-[9px] sm:text-[10px] lg:text-xs font-light'
          )}
          disabled={isDisabled}
          id=''
          onChange={(e) => onChange && onChange(e.target.value)}
          placeholder={placeholder || ''}
          type='text'
          value={value}
        />
        <div
          className='w-[60px] lg:w-[70px] text-white text-[10px] lg:text-xs shrink-0 flex-center cursor-pointer bg-primary'
          onClick={() => {
            setDisabled(!isDisabled);
            if (!isDisabled) {
              onSubmit();
            }
          }}
        >
          {isDisabled ? 'Edit' : submitText || 'Save'}
        </div>
      </div>
    </div>
  );
};

export const InputFieldNormal = ({
  name,
  placeholder,
  label,
  rootClass,
  isDisabled,
  inputType = 'text',
  validationSchema,
  register,
  defaultValue,
}: {
  placeholder?: string;
  label?: string;
  rootClass?: string;
  isDisabled?: boolean;
  inputType?: 'password' | 'text' | 'number';
  name?: string;
  validationSchema?: RegisterOptions;
  register?: any;
  defaultValue?: string | number;
  inputHeight?: number;
}) => {
  return (
    <div className={rootClass}>
      {label && (
        <span className='text-[#767676] dark:text-white text-[8px] sm:text-[9px] lg:text-[12px] font-medium ml-[12px] mb-[2px]'>
          {label}
        </span>
      )}
      <div
        className={clsx(
          `relative flex w-full max-w-[457px] h-[40px] lg:h-[45px]  rounded-[5px] bg-white dark:bg-body border-[1px] border-primary overflow-hidden lg:mt-[3px]`,
          isDisabled && `!bg-[#DDDDDD] dark:!bg-[#797E90] border-none`
        )}
      >
        {inputType === 'number' && (
          <div className='text-[10px] lg:text-xs bg-primary text-white flex-center px-[10px] lg:text-[15px]'>
            +91
          </div>
        )}
        <input
          className={clsx(
            isDisabled && 'cursor-not-allowed',
            'grow h-full outline-none px-[13px] bg-transparent text-blackWhite text-[11px] lg:text-xs font-light'
          )}
          defaultValue={defaultValue}
          disabled={isDisabled}
          multiple={3}
          name={name}
          placeholder={placeholder || 'Enter'}
          // ref={register({
          //   required: 'Required',
          //   pattern: {
          //     value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
          //     message: 'invalid email address',
          //   },
          // })}
          type={inputType}
          {...(register ? register(name, validationSchema) : {})}
        />
      </div>
    </div>
  );
};

export const TextAreaNormal = ({
  name,
  placeholder,
  label,
  rootClass,
  isDisabled,
  inputType = 'text',
  validationSchema,
  inputClass,
  register,
  defaultValue,
}: {
  placeholder?: string;
  label?: string;
  rootClass?: string;
  isDisabled?: boolean;
  inputType?: 'password' | 'text' | 'number';
  name?: string;
  validationSchema?: RegisterOptions;
  register?: any;
  defaultValue?: string | number;
  inputHeight?: number;
  inputClass?: string;
}) => {
  return (
    <div className={rootClass}>
      {label && (
        <span className='text-[#767676] dark:text-white text-[8px] sm:text-[9px] lg:text-[12px] font-medium ml-[12px] mb-[2px]'>
          {label}
        </span>
      )}
      <div
        className={clsx(
          `relative flex w-full max-w-[457px] h-[40px] lg:h-[45px]  rounded-[5px] bg-white dark:bg-body border-[1px] border-primary overflow-hidden lg:mt-[3px]`,
          isDisabled && `!bg-[#DDDDDD] dark:!bg-[#797E90] border-none`,
          inputClass
        )}
      >
        <textarea
          className={clsx(
            isDisabled && 'cursor-not-allowed',
            'grow w-full h-full outline-none px-[13px] bg-transparent text-blackWhite pt-2 pb-2  text-[11px] lg:text-xs font-light'
          )}
          defaultValue={defaultValue}
          disabled={isDisabled}
          multiple={20}
          name={name}
          placeholder={placeholder || 'Enter'}
          rows={20}
          style={{ resize: 'none' }}
          type={inputType}
          {...(register ? register(name, validationSchema) : {})}
        />
      </div>
    </div>
  );
};

export const InputFieldWithEdit = ({
  name,
  placeholder,
  label,
  rootClass,
  inputType = 'text',
  validationSchema,
  register,
  defaultValue,
  isDisabled,
  setDisabled,
}: {
  placeholder?: string;
  label?: string;
  rootClass?: string;
  inputType?: 'password' | 'text' | 'number';
  name?: string;
  validationSchema?: RegisterOptions;
  register?: any;
  defaultValue?: string | number;
  isDisabled?: boolean;
  setDisabled?: () => void;
}) => {
  return (
    <div className={rootClass}>
      {label && (
        <span className='text-[#767676] dark:text-white text-[8px] sm:text-[9px] lg:text-[12px] font-medium ml-[12px]'>
          {label}
        </span>
      )}
      <div
        className={clsx(
          isDisabled && '!bg-[#DDDDDD] dark:!bg-[#262833] border-none',
          'relative lg:mt-[3px] flex w-full max-w-[457px] h-[40px] lg:h-[45px] rounded-[5px] bg-white dark:bg-body border-[1px] border-primary overflow-hidden'
        )}
      >
        <input
          className={clsx(
            isDisabled && 'cursor-not-allowed',
            'grow h-full outline-none px-[13px] bg-transparent text-blackWhite text-[11px] lg:text-xs font-light'
          )}
          defaultValue={defaultValue}
          disabled={isDisabled}
          name={name}
          placeholder={placeholder || 'Enter'}
          type={inputType}
          {...(register ? register(name, validationSchema) : {})}
        />

        <div
          className='w-[33px] lg:w-[43px] shrink-0 flex-center cursor-pointer'
          onClick={() => setDisabled && setDisabled()}
        >
          {isDisabled ? (
            <Image
              alt='icon'
              className='w-[15px] h-[15px]'
              height={15}
              src={'/svg/pencil.svg'}
              width={15}
            />
          ) : (
            <div className='w-[15px] h-[15px] rounded-full bg-primary text-white text-[10px] flex-center'>
              -
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const InputAutoComplete = ({
  label,
  rootClass,
  isDisabled,
}: {
  label?: string;
  rootClass?: string;
  isDisabled?: boolean;
}) => {
  return (
    <div className={rootClass}>
      {label && (
        <span className='text-[#767676] dark:text-white text-[8px] sm:text-[9px] lg:text-[12px] font-medium ml-[12px]'>
          {label}
        </span>
      )}
      <AutoComplete
        className={clsx(
          isDisabled && 'autocomplete-disabled',
          'block h-[40px] lg:h-[45px] max-w-[457px]'
        )}
        disabled={isDisabled}
        options={[{ value: 'HDFC' }, { value: 'ICICI' }, { value: 'Axis' }]}
        placeholder='Select Bank'
        // onSelect={(val) => alert(val)}
        // onSearch={(text) => setAnotherOptions(getPanelValue(text))}
        // onChange={onChange}
        value={'HDFC'}
      />
    </div>
  );
};

export const TextAreaWithIcon = ({
  name,
  iconUrl,
  placeholder,
  isDisabled,
  validationSchema,
  inputType = 'text',
  register,
}: {
  name?: string;
  iconUrl: string;
  placeholder: string;
  isDisabled?: boolean;
  register?: any;
  inputType?: 'password' | 'text' | 'number';
  validationSchema?: RegisterOptions;
}) => {
  return (
    <div className='relative flex w-full max-w-[457px] h-[70px] lg:h-[90px] rounded-[5px] bg-white dark:bg-body border-[1px] border-primary overflow-hidden'>
      <div className='w-[33px] lg:w-[43px] shrink-0 flex-center border-r-[1px] border-primary'>
        <Image
          alt='icon'
          className='w-[15px] h-[15px]'
          height={15}
          src={iconUrl}
          width={15}
        />
      </div>
      <textarea
        className='grow h-full outline-none bg-transparent pt-[10px] px-[13px] text-blackWhite text-[8px] sm:text-[9px] lg:text-xs font-light'
        disabled={isDisabled}
        name={name}
        placeholder={placeholder || ''}
        type={inputType}
        {...(register ? register(name, validationSchema) : {})}
      />
    </div>
  );
};

export default InputFieldWithIcon;

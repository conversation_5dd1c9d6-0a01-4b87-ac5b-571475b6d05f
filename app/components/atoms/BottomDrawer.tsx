'use client';
import React from 'react';
import { Drawer } from 'antd';
import FilterSVG from '../svg/filter';
import CrossSVG from '../svg/cross';
import clsx from 'clsx';

const BottomDrawer = ({
  open,
  onClose,
  titleIcon,
  title = 'Filter',
  children,
  heightClass,
  topClass,
  maskClosable = true,
  sectionClass,
}: {
  open: boolean;
  onClose?: () => void;
  titleIcon?: React.ReactNode;
  title?: string;
  children: React.ReactNode;
  heightClass?: string;
  topClass?: string;
  maskClosable?: boolean;
  sectionClass?: string;
}) => {
  return (
    <Drawer
      className='rounded-t-[25px] font-pop'
      classNames={{ body: 'bg-container !p-0 scrollbarNone' }}
      closable={false}
      destroyOnClose={true}
      height={'auto'}
      maskClosable={maskClosable}
      onClose={onClose}
      open={open}
      placement={'bottom'}
      rootClassName='outline-none'
      styles={{
        mask: { background: 'rgba(0, 0, 0, 0.65)' },
        wrapper: {
          maxHeight: heightClass || '90svh',
          top: topClass || 'calc(100% - 90svh)',
          borderRadius: '25px 25px 0 0',
        },
      }}
    >
      <section className={clsx(sectionClass, 'bg-container px-[16px]')}>
        <div className='sticky top-[0] pt-[7px] pb-[12px] z-[99] bg-container w-full'>
          <div className='handle m-auto h-[3px] w-[46px] rounded-[2.5px] bg-[#CED0D7] dark:bg-[#2A2D35]' />
          <div className='flex items-center justify-between mt-[12px]'>
            <div className='flex items-center pl-[9px]'>
              {titleIcon ?? (
                <FilterSVG className='text-primary w-[10px] h-[11px]' />
              )}
              <h4 className='font-pat text-xs text-[#06020E] dark:text-[#E7E9EB] font-normal ml-[10px] leading-none'>
                {title}
              </h4>
            </div>
            <CrossSVG
              className='w-[10px] text-[#06020E] dark:text-[#E7E9EB] mr-[15px]'
              onClick={onClose}
            />
          </div>
        </div>
        <div className=''>{children}</div>
      </section>
    </Drawer>
  );
};

export default BottomDrawer;

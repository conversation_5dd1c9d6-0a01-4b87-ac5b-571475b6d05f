import Image from 'next/image';
import day from '@/public/svg/day.svg';
import night from '@/public/svg/night.svg';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';

function ThemeToggleButton() {
  const { setTheme, resolvedTheme } = useTheme('light');
  const [themeBut, setThemeBut] = useState(false);
  useEffect(() => {
    if (resolvedTheme === 'dark') {
      setThemeBut(true);
    } else {
      setThemeBut(false);
    }
  }, [resolvedTheme]);

  return (
    <div
      className={`bg-white duration-500 relative w-[40px] h-[20px] rounded-full flex justify-center gap-1 items-center cursor-pointer`}
      onClick={() => setTheme(resolvedTheme === 'dark' ? 'light' : 'dark')}
      style={{ boxShadow: '0px 1px 7px 0px rgba(0, 0, 0, 0.25)' }}
    >
      <Image
        alt='day'
        className='w-[11px] h-[11px]  dark:visible invisible '
        src={night}
      />
      <span
        className={` ${
          themeBut ? 'translate-x-0' : '-translate-x-5'
        } bg-[#6D56CF]  absolute right-[3px] w-[15px] h-[15px] rounded-full  duration-300 `}
      />
      <Image
        alt='day'
        className='w-[11px] h-[11px] dark:invisible visible '
        src={day}
      />
    </div>
  );
}

export default ThemeToggleButton;

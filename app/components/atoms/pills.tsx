import React from 'react';
import clsx from 'clsx';
import Image from 'next/image';
import CrossSVG from '../svg/cross';
import { ArrowUp } from '../svg/arrow-up-down';

const PillButton = ({
  className,
  children,
  isSelected,
  useArrow,
  text,
  imgUrl,
  onClick,
}: {
  className?: string;
  children?: React.ReactNode;
  isSelected?: boolean;
  useArrow?: boolean;
  text: string;
  imgUrl?: string;
  onClick?: () => void;
}) => {
  return (
    <div
      className={clsx(
        className,
        isSelected && '!bg-primary !text-white',
        imgUrl && '!pl-0',
        'h-[35px] text-[9px] lg:text-xs font-medium px-[15px] shrink-0 rounded-[20px] bg-white dark:bg-[#515662] flex items-center justify-center cursor-pointer dark:text-white hover:bg-[#7366d93d]'
      )}
      onClick={onClick}
    >
      {imgUrl && (
        <div className='relative w-[34px] h-[34px] rounded-full bg-white mr-[5px]'>
          <Image alt='icon' className='object-contain' fill src={imgUrl} />
        </div>
      )}

      {text}
      {children}

      {useArrow && (
        <ArrowUp className='h-[14px] rotate-[90deg] text-[#988BFC] ml-[3px]' />
      )}
    </div>
  );
};

export const SelectedPill = ({
  className,
  text,
  onClick,
}: {
  className?: string;
  text: string;
  onClick?: () => void;
}) => {
  return (
    <div
      className={clsx(
        className,
        'relative h-[24px] text-[10px] font-medium pl-[10px] pr-[24px] shrink-0 bg-primary flex-center cursor-pointer text-white rounded-[27.5px] border-[1px] border-[#988BFC] max-w-[123px]'
      )}
    >
      <span className='truncate'>{text}</span>
      <div
        className='w-[24px] h-[24px] absolute right-[-4px] top-[50%] translate-y-[-50%] flex-center rounded-[27.5px] border-[1px] bg-primary border-[#988BFC]'
        onClick={onClick}
      >
        <CrossSVG className='w-[8px] text-white' />
      </div>
    </div>
  );
};

export default PillButton;

'use client';
import React from 'react';
import clsx from 'clsx';

type ShimmerProps = {
  animation?: 'pulse' | 'wave';
  className?: string;
  height?: string;
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';
  width?: string;
};

const getRadiusClass = (rounded: ShimmerProps['rounded']) => {
  switch (rounded) {
    case 'none':
      return 'rounded-none';
    case 'sm':
      return 'rounded-[3px]';
    case 'md':
      return 'rounded-[5px]';
    case 'lg':
      return 'rounded-[8px]';
    case 'full':
      return 'rounded-full';
    default:
      return 'rounded-[3px]';
  }
};

const ShimmerEffect = ({
  animation = 'pulse',
  className,
  height,
  rounded = 'sm',
  width,
}: ShimmerProps) => {
  const radiusClass = getRadiusClass(rounded);
  const animationClass =
    animation === 'pulse' ? 'animate-pulse' : 'shimmer-wave';

  return (
    <div
      className={clsx(
        'bg-gray-200 dark:bg-gray-700 flex-shrink-0',
        radiusClass,
        animationClass,
        className
      )}
      style={{
        height,
        width,
      }}
    />
  );
};

// Predefined shimmer components for common use cases
export const CircleShimmer = ({
  className,
  size = '40px',
}: {
  className?: string;
  size?: string;
}) => (
  <ShimmerEffect
    className={className}
    height={size}
    rounded='full'
    width={size}
  />
);

export const RectShimmer = ({
  className,
  height = '14px',
  width = '90%',
}: {
  className?: string;
  height?: string;
  width?: string;
}) => (
  <ShimmerEffect
    className={clsx('mx-auto', className)}
    height={height}
    width={width}
  />
);

export const ImageShimmer = ({
  className,
  height = '100px',
  width = '100%',
}: {
  className?: string;
  height?: string;
  width?: string;
}) => (
  <ShimmerEffect
    className={className}
    height={height}
    rounded='md'
    width={width}
  />
);

export default ShimmerEffect;

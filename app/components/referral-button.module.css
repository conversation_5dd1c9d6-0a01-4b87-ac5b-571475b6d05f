.loader {
  position: relative;
  width: 24px;
  height: 24px;
}

.loader:after {
  content: '₹';
  position: absolute;
  display: block;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  text-align: center;
  line-height: 20px;
  font-size: 16px;
  font-weight: bold;
  background: #ffd700;
  color: #daa520;
  border: 2px double;
  box-sizing: border-box;
  box-shadow: 1px 1px 1px 0.5px rgba(0, 0, 0, 0.1);
  animation: coinFlip 4s cubic-bezier(0, 0.2, 0.8, 1) infinite;
  transform-style: preserve-3d;
}

@keyframes coinFlip {
  0%,
  100% {
    animation-timing-function: cubic-bezier(0.5, 0, 1, 0.5);
  }

  0% {
    transform: rotateY(0deg);
  }

  50% {
    transform: rotateY(1800deg);
    animation-timing-function: cubic-bezier(0, 0.5, 0.5, 1);
  }

  100% {
    transform: rotateY(3600deg);
  }
}

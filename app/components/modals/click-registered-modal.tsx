'use client';
import React, { useEffect, useState } from 'react';
import CrossSVG from '../svg/cross';
import { Modal } from 'antd';
import { useWindowSize } from 'usehooks-ts';
import BottomDrawer from '../atoms/BottomDrawer';
import Image from 'next/image';
import CopySVG from '../svg/copy';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  setProceedWithoutCb,
  setSelectedOffer,
  setShowClickRegisModal,
} from '@/redux/slices/global-slice';
import { copyToClipboard } from '@/utils/helpers';
import ThemeButton from '../atoms/theme-btn';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import clsx from 'clsx';
import { toast } from 'react-toastify';
import ICBLogo from '../svg/icb-logo';
import { motion } from 'framer-motion';
import SmartLink from '../common/smart-link';
import { LinkType } from '@/utils/link-utils';

const ClickRegisteredModal = () => {
  const { width = 0 } = useWindowSize({ initializeWithValue: false });
  const { showClickRegisModal, selectedOffer } = useAppSelector(
    (state) => state.global
  );
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  return (
    <>
      {width <= 768 && (
        <BottomDrawer
          heightClass='70svh'
          maskClosable={false}
          onClose={() => {
            dispatch(setProceedWithoutCb(false));
            dispatch(setShowClickRegisModal(false));
            //clear the couponCode on close
            dispatch(setSelectedOffer({ ...selectedOffer, couponCode: '' }));
          }}
          open={showClickRegisModal}
          sectionClass='!px-0'
          title=''
          titleIcon={''}
          topClass='calc(100% - 70svh)'
        >
          {isUserLogin ? <ModalBody /> : <ModalBodyNonLogin />}
        </BottomDrawer>
      )}
      {width >= 768 && (
        <Modal
          cancelText=''
          centered
          classNames={{
            content: '!bg-container !p-0',
            header: '!bg-container !text-blackWhite',
          }}
          closeIcon={<CrossSVG className='text-blackWhite w-[16px]' />}
          destroyOnClose={true}
          footer={<></>}
          maskClosable={false}
          okText=''
          onCancel={() => {
            dispatch(setProceedWithoutCb(false));
            dispatch(setShowClickRegisModal(false));
            //clear the couponCode on close
            dispatch(setSelectedOffer({ ...selectedOffer, couponCode: '' }));
          }}
          open={showClickRegisModal}
          title={<></>}
          width={700}
        >
          {isUserLogin ? <ModalBody /> : <ModalBodyNonLogin />}
        </Modal>
      )}
    </>
  );
};

const ModalBody = () => {
  const { clickGeneratedData, selectedOffer } = useAppSelector(
    (state) => state.global
  );
  return (
    <motion.div
      animate={{ opacity: 1 }}
      className='pt-0 lg:pt-[40px] p-[40px]'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className='flex justify-start items-center'>
        <motion.div
          animate={{ opacity: 1, x: 0 }}
          className='relative w-[100px] h-[200px] hidden lg:block shrink-0'
          initial={{ opacity: 0, x: -50 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Image
            alt=''
            className='object-contain h-auto'
            fill
            src={'/img/happy-boy.png'}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='grow text-center'
          initial={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <motion.p
            animate={{ scale: 1 }}
            className='text-[#1EB091] text-base font-bold text-center'
            initial={{ scale: 0.8 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            Congrats !! <br /> Your cashback click has been registered.
          </motion.p>
          <motion.div
            animate={{ opacity: 1, scale: 1 }}
            className='relative w-[130px] h-[75px] mx-auto'
            initial={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <Image
              alt=''
              className='object-contain'
              fill
              src={clickGeneratedData?.logo || ''}
            />
          </motion.div>

          {clickGeneratedData?.offer && (
            <motion.p
              animate={{ opacity: 1 }}
              className='text-sm text-blackWhite mt-[10px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              {clickGeneratedData.offer}
            </motion.p>
          )}
          {selectedOffer?.couponCode && (
            <motion.button
              animate={{ opacity: 1, y: 0 }}
              className='min-w-[217px] h-[65px] flex-center rounded-[9px] border-[2px] border-primary border-dashed shadow mt-[20px] mx-auto transition duration-300 hover:shadow-lg active:shadow-md active:border-dotted bg-white/5 backdrop-blur-sm'
              initial={{ opacity: 0, y: 20 }}
              onClick={() => copyToClipboard(selectedOffer?.couponCode || '')}
              transition={{ duration: 0.5, delay: 0.6 }}
              whileHover={{
                scale: 1.03,
                boxShadow: '0px 6px 15px rgba(0, 0, 0, 0.1)',
              }}
              whileTap={{ scale: 0.97 }}
            >
              <span className='text-base text-blackWhite font-semibold'>
                {selectedOffer.couponCode}
              </span>
              <motion.div
                animate={{ rotate: [0, 10, 0] }}
                transition={{ duration: 1, repeat: 2, repeatDelay: 3 }}
              >
                <CopySVG className='text-primary w-[19px] h-[19px] ml-[10px]' />
              </motion.div>
            </motion.button>
          )}
          <motion.div
            animate={{ opacity: 1 }}
            className='text-base text-blackWhite mt-[18px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            Click ID :{' '}
            <span className='font-semibold'>
              {clickGeneratedData?.referenceId}
            </span>{' '}
          </motion.div>
        </motion.div>
      </div>
      <motion.p
        animate={{ opacity: 1 }}
        className='text-blackWhite text-xs mt-[20px]'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
      >
        Your cashback will be tracked within{' '}
        <span className='font-semibold'>72 Hours</span> after successful
        ordering. You can check the list of transactions at your{' '}
        <SmartLink
          className='text-[#4E3FC5] font-semibold hover:underline transition-all duration-300'
          href={'/click-history'}
          linkType={LinkType.INTERNAL}
        >
          Account page
        </SmartLink>
        . If the transaction is not tracked even after 72 hours, you can submit
        a missing cashback report.
      </motion.p>
    </motion.div>
  );
};

const ModalBodyNonLogin = () => {
  const { clickGeneratedData, selectedOffer } = useAppSelector(
    (state) => state.global
  );
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const [copiedNotify, setCopiedNotify] = useState(false);
  const dispatch = useAppDispatch();

  useEffect(() => {
    const notifyTimer = setTimeout(() => {
      setCopiedNotify(false);
    }, 2000);

    return () => {
      clearTimeout(notifyTimer);
    };
  }, [copiedNotify]);

  return (
    <motion.div
      animate={{ opacity: 1 }}
      className='py-10'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className='flex justify-start items-center'>
        <motion.div
          animate={{ opacity: 1, x: 0 }}
          className={clsx(
            selectedOffer?.couponCode && 'top-20',
            'absolute left-5 top-10 z-10'
          )}
          initial={{ opacity: 0, x: -50 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className='relative w-[100px] h-[200px] lg:w-[138px] lg:h-[249px] hidden lg:block shrink-0'>
            <Image
              alt=''
              className='object-contain h-auto'
              fill
              src={'/img/sad-boy.png'}
            />
          </div>
        </motion.div>
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='grow text-center'
          initial={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          {isUserLogin ? (
            <motion.p
              animate={{ scale: 1 }}
              className='text-[#1EB091] text-base font-bold text-center'
              initial={{ scale: 0.8 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              Congrats !! <br /> Your cashback click has been registered.
            </motion.p>
          ) : (
            <motion.p
              animate={{ scale: 1 }}
              className='text-[#ff4141] dark:text-white text-base font-bold text-center lg:pl-20'
              initial={{ scale: 0.8 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              Oops! <br /> You missed chance to earn {clickGeneratedData?.offer}{' '}
              !
            </motion.p>
          )}

          {isUserLogin && (
            <>
              <motion.div
                animate={{ opacity: 1, scale: 1 }}
                className='relative w-[130px] h-[75px] mx-auto'
                initial={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <Image
                  alt=''
                  className='object-contain'
                  fill
                  src={clickGeneratedData?.logo || ''}
                />
              </motion.div>
              {clickGeneratedData?.offer && (
                <motion.p
                  animate={{ opacity: 1 }}
                  className='text-sm text-blackWhite mt-[10px]'
                  initial={{ opacity: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                >
                  {clickGeneratedData.offer}
                </motion.p>
              )}
            </>
          )}

          {selectedOffer?.couponCode && (
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='lg:pl-20'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <motion.button
                className={clsx(
                  copiedNotify && '!bg-[#079307] !border-solid !text-white',
                  'min-w-[217px] h-[65px] flex-center rounded-[9px] border-[2px] border-primary border-dashed shadow mt-[20px] mx-auto transition duration-300 hover:shadow-lg active:shadow-md bg-white/5 backdrop-blur-sm'
                )}
                onClick={() => {
                  copyToClipboard(selectedOffer?.couponCode || '');
                  setCopiedNotify(true);
                  toast.success('Successfully copied coupon code.', {
                    toastId: 'coupon-copied',
                  });
                }}
                whileHover={
                  !copiedNotify
                    ? {
                        scale: 1.03,
                        boxShadow: '0px 6px 15px rgba(0, 0, 0, 0.1)',
                      }
                    : {}
                }
                whileTap={!copiedNotify ? { scale: 0.97 } : {}}
              >
                <span
                  className={clsx(
                    copiedNotify && '!text-white',
                    'text-base text-blackWhite font-semibold'
                  )}
                >
                  {selectedOffer.couponCode}
                </span>
                <motion.div
                  animate={
                    copiedNotify ? { rotate: 0 } : { rotate: [0, 10, 0] }
                  }
                  transition={{ duration: 1, repeat: 2, repeatDelay: 3 }}
                >
                  <CopySVG
                    className={clsx(
                      copiedNotify && '!text-white',
                      'text-primary w-[19px] h-[19px] ml-[10px]'
                    )}
                  />
                </motion.div>
              </motion.button>
            </motion.div>
          )}

          {!isUserLogin && (
            <>
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className='bg-[#FFC554] py-5 flex-center w-full mt-5 lg:pl-20'
                initial={{ opacity: 0, y: 20 }}
                style={{ boxShadow: '0px 4px 18px 0px rgba(0, 0, 0, 0.03)' }}
                transition={{ duration: 0.5, delay: 0.6 }}
                whileHover={{
                  y: -5,
                  boxShadow: '0px 8px 24px 0px rgba(0, 0, 0, 0.08)',
                }}
              >
                <div className='font-medium text-sm text-center text-black flex flex-col'>
                  <motion.span
                    animate={{ opacity: 1 }}
                    initial={{ opacity: 0 }}
                    transition={{ delay: 0.7, duration: 0.5 }}
                  >
                    Join
                  </motion.span>
                  <motion.div
                    animate={{ opacity: 1 }}
                    className='flex gap-[8px] items-center cursor-pointer'
                    initial={{ opacity: 0 }}
                    onClick={() => dispatch(setLoginModalOpen(true))}
                    transition={{ delay: 0.8, duration: 0.5 }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <motion.div
                      animate={{ rotate: 0, opacity: 1 }}
                      initial={{ rotate: -10, opacity: 0 }}
                      transition={{ delay: 0.9, duration: 0.5, type: 'spring' }}
                    >
                      <ICBLogo className='text-[#4d3ec1] w-10 h-10' />
                    </motion.div>
                    <motion.p
                      animate={{ x: 0, opacity: 1 }}
                      className='text-[#4d3ec1] text-[22px] font-normal pt-[12px]'
                      initial={{ x: 20, opacity: 0 }}
                      transition={{ delay: 1, duration: 0.5 }}
                    >
                      indian
                      <span className='font-bold'>cashback.com</span>{' '}
                    </motion.p>
                  </motion.div>{' '}
                  <motion.span
                    animate={{ opacity: 1 }}
                    className='mt-2'
                    initial={{ opacity: 0 }}
                    transition={{ delay: 1.1, duration: 0.5 }}
                  >
                    and never miss any cashback
                  </motion.span>
                </div>
              </motion.div>
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className={clsx(
                  selectedOffer?.couponCode ? 'pt-5' : 'pt-10',
                  'lg:pl-20 lg:pt-0'
                )}
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 1.2 }}
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <ThemeButton
                    className='!h-[40px] !w-[113px] mt-5 mx-auto text-sm'
                    onClick={() => dispatch(setLoginModalOpen(true))}
                    text='JOIN NOW'
                  />
                </motion.div>
              </motion.div>
            </>
          )}

          {isUserLogin && (
            <motion.div
              animate={{ opacity: 1 }}
              className='text-base text-blackWhite mt-[18px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              Click ID :{' '}
              <span className='font-semibold'>
                {clickGeneratedData?.referenceId}
              </span>{' '}
            </motion.div>
          )}
        </motion.div>
      </div>
      {isUserLogin && (
        <motion.p
          animate={{ opacity: 1 }}
          className='text-blackWhite text-xs mt-[20px]'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          Your cashback will be tracked within{' '}
          <span className='font-semibold'>72 Hours</span> after successful
          ordering. You can check the list of transactions at your{' '}
          <SmartLink
            className='text-[#4E3FC5] font-semibold hover:underline transition-all duration-300'
            href={'/transactions'}
            linkType={LinkType.INTERNAL}
          >
            Account page
          </SmartLink>
          . If the transaction is not tracked even after 72 hours, you can
          submit a missing cashback report.
        </motion.p>
      )}
    </motion.div>
  );
};

export default ClickRegisteredModal;

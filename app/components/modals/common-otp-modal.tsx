import React from 'react';
import BottomDrawer from '@/app/components/atoms/BottomDrawer';
import { useWindowSize } from 'usehooks-ts';
import CrossSVG from '@/app/components/svg/cross';
import { Modal } from 'antd';

const CommonOtpModal = ({
  title = '',
  isOpen,
  onClose,
  children,
}: {
  isOpen: boolean;
  title?: string;
  onClose: () => void;
  children?: React.ReactNode;
}) => {
  const { width = 0 } = useWindowSize({ initializeWithValue: false });

  return (
    <>
      {width <= 768 && (
        <BottomDrawer
          heightClass='40svh'
          maskClosable={false}
          onClose={onClose}
          open={isOpen}
          sectionClass='!px-0'
          title={title}
          titleIcon={''}
          topClass='calc(100% - 40svh)'
        >
          {children}
        </BottomDrawer>
      )}
      {width >= 1024 && (
        <Modal
          cancelText=''
          centered
          classNames={{
            content: '!bg-container',
            header: '!bg-container !text-blackWhite',
          }}
          closeIcon={<CrossSVG className='text-blackWhite w-[16px]' />}
          destroyOnClose={true}
          footer={<></>}
          maskClosable={false}
          okText=''
          onCancel={onClose}
          open={isOpen}
          rootClassName='!h-[300px]'
          title={
            <span className='text-[14px] text-blackWhite'>
              {title ?? <></>}
            </span>
          }
          width={560}
        >
          {children}
        </Modal>
      )}
    </>
  );
};
export default CommonOtpModal;

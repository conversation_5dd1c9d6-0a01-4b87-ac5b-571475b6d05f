'use client';
import type React from 'react';
import type { TopLink } from './link-generator-data';

interface LinkGeneratorTableProps {
  links: TopLink[];
}

const LinkGeneratorTable: React.FC<LinkGeneratorTableProps> = ({
  links,
}: {
  links: TopLink[];
}) => {
  console.log('links', links);
  return (
    <div className='overflow-x-auto w-full max-w-6xl rounded-lg shadow-lg'>
      <table className='min-w-full bg-white dark:bg-[#212327]'>
        <thead className='table-header-group'>
          <tr className='text-xs border-b border-gray-200 dark:border-gray-700'>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-left min-w-[200px]'>
              Date
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-left min-w-[120px]'>
              Product
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-left min-w-[100px]'>
              Store
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-center min-w-[80px]'>
              Clicks
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-center min-w-[100px]'>
              Conversations
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-right min-w-[120px]'>
              Commission Earned
            </th>
          </tr>
        </thead>
        <tbody className='text-sm'>
          {links.map((link, index) => (
            <tr
              className='table-row border-b border-gray-200 dark:border-gray-700 mb-0 rounded-none bg-white dark:bg-[#212327]'
              key={`${link.productName}-${index}`}
            >
              {/* Date Column */}
              <td className='table-cell py-3 px-4'>
                <span className='inline text-left'>
                  {link.createdAt
                    ? new Date(link.createdAt).toLocaleDateString('en-GB', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric',
                      })
                    : 'N/A'}
                </span>
              </td>
              {/* Product Column */}
              <td className='table-cell py-3 px-4 text-left'>
                <span className='inline text-left'>{link.productName}</span>
              </td>
              {/* Store Column */}
              <td className='table-cell py-3 px-4 text-left'>
                <span className='whitespace-nowrap'>{link.storeName}</span>
              </td>
              <td className='table-cell py-3 px-4 text-center'>
                <span className='whitespace-nowrap font-medium'>
                  {link.clicks}
                </span>
              </td>
              <td className='table-cell py-3 px-4 text-center'>
                <span className='whitespace-nowrap font-medium'>
                  {link.conversations}
                </span>
              </td>
              <td className='table-cell py-3 px-4 text-right'>
                <span className='whitespace-nowrap font-bold text-green-600 dark:text-green-400'>
                  ₹{link.cashbackEarned}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default LinkGeneratorTable;

export const ApprovedStatus = () => {
  return (
    <div className='px-4 py-2 rounded-full text-sm text-black bg-[#7AE453] w-[105px] text-center m-auto'>
      Approved
    </div>
  );
};

export const PendingStatus = () => {
  return (
    <div className='px-4 py-2 rounded-full text-sm text-black bg-[#FFBF41] w-[105px] text-center m-auto'>
      Pending
    </div>
  );
};

export const RejectedStatus = () => {
  return (
    <div className='px-4 py-2 rounded-full text-sm text-black bg-red-500 w-[105px] text-center m-auto'>
      Rejected
    </div>
  );
};

'use client';
import Image from 'next/image';
import React from 'react';
import { useState, useEffect, useCallback } from 'react';
import { Copy, Share } from 'lucide-react';
import Telegram from '../svg/social/telegram';
import { toast } from 'react-toastify';
import WhatsappSVG from '../svg/social/whatsapp';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import fetchWrapper from '@/utils/fetch-wrapper';
import { useSearchParams } from 'next/navigation';

const LinkGeneratorHero = () => {
  return (
    <section className='w-full h-[730px] md:h-[630px] mb-[16vh] sm:mb-[10vh] bg-gradient-to-r from-[#7366D9] to-[#574ABE] flex flex-col md:flex-row items-center justify-center px-4 md:px-8 py-6 rounded-lg shadow-lg'>
      {/* Hero content goes here */}
      <div className='text-center py-8 h-full w-full flex flex-col items-center justify-start gap-y-3 order-2 md:order-1'>
        <Image
          alt='referral hero'
          className='w-auto h-24 md:h-32 object-cover'
          height={300}
          src={'/temp/link-generator/hero.png'}
          width={300}
        />
        <h1 className='text-white text-2xl md:text-3xl lg:text-5xl font-medium mb-2 md:mb-4'>
          Share & Earn with IndianCashback
        </h1>
        <p className='text-white text-sm md:text-base lg:text-xl mb-4 md:mb-6'>
          Paste a product link, generate your unique commission link, and start
          earning when others shop through it.
        </p>
        <LinkGeneratorHeroCard />
      </div>
    </section>
  );
};

export default LinkGeneratorHero;

const LinkGeneratorHeroCard = () => {
  const [productLink, setProductLink] = useState('');
  const [generatedLink, setGeneratedLink] = useState('');
  const [processedText, setProcessedText] = useState('');
  const [storeName, setStoreName] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [isSingleUrl, setIsSingleUrl] = useState(true);
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();

  const handleGenerateLink = useCallback(async () => {
    if (!productLink.trim()) {
      toast.error('Please enter a valid product link');
      return;
    }

    // Check if user is logged in only after hydration
    if (isHydrated && !isUserLogin) {
      // Show login popup if not logged in
      dispatch(setLoginModalOpen(true));
      return;
    }

    // User is logged in, proceed with link generation
    try {
      setIsGenerating(true);
      const response = await fetchWrapper<{
        processedText: string;
        linkDetails: Array<{
          linkId: string;
          originalUrl: string;
          generatedUrl: string;
          storeName: string;
          createdAt: string;
          shortUrl: string;
        }>;
        totalLinksProcessed: number;
      }>('/api/proxy/links/generate-multi', {
        method: 'POST',
        body: JSON.stringify({ text: productLink }),
      });

      if (response?.linkDetails && response.linkDetails.length > 0) {
        // Use the first link's details for single link generation
        const firstLink = response.linkDetails[0];
        setGeneratedLink(firstLink.shortUrl);
        setStoreName(firstLink.storeName);
        setProcessedText(response.processedText);

        // Check if input is a single URL (no additional text)
        const trimmedInput = productLink.trim();
        const isOnlyUrl =
          response.linkDetails.length === 1 &&
          (trimmedInput === firstLink.originalUrl ||
            trimmedInput.replace(/\s+/g, '') ===
              firstLink.originalUrl.replace(/\s+/g, ''));
        setIsSingleUrl(isOnlyUrl);

        toast.success(
          `Link generated successfully! ${
            response.totalLinksProcessed > 1
              ? `(${response.totalLinksProcessed} links processed)`
              : ''
          }`
        );
      } else {
        toast.error('Failed to generate link. Please try again.');
      }
    } catch (error) {
      console.error('Error generating link:', error);
      toast.error('An error occurred while generating the link');
    } finally {
      setIsGenerating(false);
    }
  }, [productLink, isHydrated, isUserLogin, dispatch]);

  // Handle hydration to prevent SSR/client mismatch
  useEffect(() => {
    setIsHydrated(true);

    // Check for product link from URL parameters
    const linkParam = searchParams.get('link');
    if (linkParam) {
      const decodedLink = decodeURIComponent(linkParam);
      setProductLink(decodedLink);
      // Auto-trigger link generation after a short delay to ensure component is ready
      setTimeout(() => {
        handleGenerateLink();
      }, 800);
    }
  }, [handleGenerateLink, searchParams]);

  const handleShare = () => {
    if (!generatedLink) {
      toast.error('Please generate a link first');
      return;
    }

    // Use different content based on whether it's a single URL or contains multiple URLs/text
    const shareContent = isSingleUrl
      ? {
          title: 'My ICB Cashback Link',
          text: `Hey! I found the product you were looking for on ${storeName}. Looks like a great deal — thought you'd want to check it out!`,
          url: generatedLink,
        }
      : {
          title: 'My ICB Cashback Links',
          text: processedText,
          url: '',
        };

    // Only use navigator.share if it exists and we're on the client side
    if (typeof window !== 'undefined' && navigator?.share) {
      navigator.share(shareContent);
    } else {
      // Fallback: copy to clipboard if navigator.share is not available
      copyToClipboard();
    }
  };

  const handleTelegramShare = () => {
    if (!generatedLink) {
      toast.error('Please generate a link first');
      return;
    }

    const shareText = isSingleUrl
      ? `Hey! I found the product you were looking for on ${storeName}. Looks like a great deal — thought you'd want to check it out!`
      : processedText;

    const shareUrl = isSingleUrl ? generatedLink : '';

    // Ensure window is available before opening
    if (typeof window !== 'undefined') {
      const telegramUrl = isSingleUrl
        ? `https://t.me/share/url?url=${encodeURIComponent(
            shareUrl
          )}&text=${encodeURIComponent(shareText)}`
        : `https://t.me/share/url?text=${encodeURIComponent(shareText)}`;

      window.open(telegramUrl, '_blank');
    }
  };

  const handleWhatsAppShare = () => {
    if (!generatedLink) {
      toast.error('Please generate a link first');
      return;
    }

    const shareText = isSingleUrl
      ? `Hey! I found the product you were looking for on ${storeName}. Looks like a great deal — thought you'd want to check it out! ${generatedLink}`
      : processedText;

    // Ensure window is available before opening
    if (typeof window !== 'undefined') {
      window.open(
        `https://wa.me/?text=${encodeURIComponent(shareText)}`,
        '_blank'
      );
    }
  };

  const copyToClipboard = () => {
    if (!generatedLink) {
      toast.error('Please generate a link first');
      return;
    }

    const textToCopy = isSingleUrl ? generatedLink : processedText;

    // Only use clipboard API if it exists and we're on the client side
    if (typeof window !== 'undefined' && navigator?.clipboard) {
      navigator.clipboard.writeText(textToCopy);
      toast.success(
        isSingleUrl
          ? 'Link copied to clipboard'
          : 'Processed text copied to clipboard'
      );
    } else {
      // Fallback for older browsers or SSR
      try {
        const textArea = document.createElement('textarea');
        textArea.value = textToCopy;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        toast.success(
          isSingleUrl
            ? 'Link copied to clipboard'
            : 'Processed text copied to clipboard'
        );
      } catch (error) {
        toast.error('Failed to copy. Please copy manually.');
      }
    }
  };

  return (
    <div className='w-full bg-[#EEEEEE] dark:bg-[#212327] p-4 sm:p-6 md:p-8 rounded-lg min-h-[500px] sm:min-h-[550px] md:h-[410px] shadow-md'>
      <div className='max-w-3xl mx-auto'>
        <h1 className='text-xl sm:text-2xl md:text-3xl font-bold mb-4 md:mb-6'>
          Generate your commission link
        </h1>

        <p className='mb-2 text-sm md:text-base'>
          Paste Any Valid Product Link From Supported Stores
        </p>

        <div className='flex flex-col sm:flex-row mb-6 md:mb-8 gap-2 sm:gap-0 w-full'>
          {isInputFocused ? (
            <textarea
              autoFocus
              className='w-full sm:flex-1 p-3 rounded-lg sm:rounded-l-lg sm:rounded-r-none focus:outline-none text-sm md:text-base bg-white dark:bg-[#3b3b3b] border border-gray-200 dark:border-gray-600 sm:border-r-0 min-h-[80px] resize-none'
              onBlur={() => setIsInputFocused(false)}
              onChange={(e) => setProductLink(e.target.value)}
              placeholder='eg. https://www.flipkart.com/apple-iphone-13...'
              value={productLink}
            />
          ) : (
            <input
              className='w-full sm:flex-1 p-3 rounded-lg sm:rounded-l-lg sm:rounded-r-none focus:outline-none text-sm md:text-base bg-white dark:bg-[#3b3b3b] border border-gray-200 dark:border-gray-600 sm:border-r-0'
              onChange={(e) => setProductLink(e.target.value)}
              onFocus={() => setIsInputFocused(true)}
              placeholder='eg. https://www.flipkart.com/apple-iphone-13...'
              type='text'
              value={productLink}
            />
          )}
          <button
            className='w-full sm:w-auto sm:flex-shrink-0 bg-indigo-500 text-white px-4 sm:px-6 py-3 rounded-lg sm:rounded-r-lg sm:rounded-l-none hover:bg-indigo-600 disabled:bg-indigo-300 disabled:cursor-not-allowed text-sm md:text-base min-w-[120px] sm:min-w-[100px] md:min-w-[120px] font-medium active:bg-indigo-700 touch-manipulation'
            disabled={isGenerating}
            onClick={handleGenerateLink}
            type='button'
          >
            {isGenerating ? 'Generating...' : 'Generate'}
          </button>
        </div>

        <p className='mb-2 text-sm md:text-base'>
          {isSingleUrl
            ? 'Your ICB commission Link'
            : 'Your processed text with ICB commission links'}
        </p>
        <div className='flex items-stretch rounded-lg mb-6 md:mb-8 w-full overflow-hidden border border-gray-200 dark:border-gray-600'>
          {isSingleUrl ? (
            <input
              className='flex-1 min-w-0 focus:outline-none p-3 bg-white dark:bg-[#3b3b3b] text-sm md:text-base border-none'
              placeholder='Your generated link will appear here'
              readOnly
              type='text'
              value={generatedLink}
            />
          ) : (
            <textarea
              className='flex-1 min-w-0 focus:outline-none p-3 bg-white dark:bg-[#3b3b3b] text-sm md:text-base border-none min-h-[80px] resize-none'
              placeholder='Your processed text with commission links will appear here'
              readOnly
              value={processedText}
            />
          )}
          <button
            className='flex-shrink-0 text-indigo-500 bg-white dark:bg-[#3b3b3b] px-3 py-3 w-12 md:w-14 flex items-center justify-center hover:bg-gray-50 dark:hover:bg-[#4a4a4a] transition-colors disabled:opacity-50 disabled:cursor-not-allowed border-l border-gray-200 dark:border-gray-600'
            disabled={!generatedLink}
            onClick={copyToClipboard}
            title='Copy to clipboard'
            type='button'
          >
            <Copy
              className={`h-4 w-4 md:h-5 md:w-5 ${
                !generatedLink ? 'opacity-50' : ''
              }`}
            />
          </button>
        </div>

        <div className='flex flex-col md:flex-row justify-center gap-3 sm:gap-2 text-sm font-medium w-full'>
          <button
            className='w-full md:w-[155px] flex items-center justify-center gap-2 bg-indigo-500 text-white px-4 sm:px-6 py-3 rounded-lg hover:bg-indigo-600 hover:scale-105 transition-all duration-300 disabled:bg-indigo-300 disabled:hover:scale-100 disabled:cursor-not-allowed'
            disabled={!generatedLink || isGenerating}
            onClick={handleShare}
            type='button'
          >
            <Share className='h-4 w-4 md:h-5 md:w-5' />
            Share
          </button>

          <button
            className='w-full md:w-[155px] flex items-center justify-center gap-2 bg-white text-primary px-4 sm:px-6 py-3 rounded-lg border hover:bg-gray-50 hover:scale-105 transition-all duration-300 disabled:bg-gray-100 disabled:text-gray-400 disabled:hover:scale-100 disabled:cursor-not-allowed'
            disabled={!generatedLink || isGenerating}
            onClick={handleTelegramShare}
            type='button'
          >
            <Telegram
              className={`h-5 w-5 md:h-6 md:w-6 ${
                !generatedLink || isGenerating
                  ? 'text-gray-400'
                  : 'text-blue-500'
              }`}
            />
            Telegram
          </button>

          <button
            className='w-full md:w-[155px] flex items-center justify-center gap-2 bg-white text-primary px-4 sm:px-6 py-3 rounded-lg border hover:bg-gray-50 hover:scale-105 transition-all duration-300 disabled:bg-gray-100 disabled:text-gray-400 disabled:hover:scale-100 disabled:cursor-not-allowed'
            disabled={!generatedLink || isGenerating}
            onClick={handleWhatsAppShare}
            type='button'
          >
            <WhatsappSVG
              className={`h-5 w-5 md:h-6 md:w-6 ${
                !generatedLink || isGenerating
                  ? 'text-gray-400'
                  : 'text-green-500'
              }`}
            />
            WhatsApp
          </button>
        </div>
      </div>
    </div>
  );
};

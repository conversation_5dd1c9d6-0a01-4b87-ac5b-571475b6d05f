'use client';
import React, { useState, useEffect } from 'react';
import LinkGeneratorTable from './LinkGeneratorTable';
import clsx from 'clsx';
import { Store } from 'lucide-react';
import SimpleDateRangePicker from '../dropdowns/simple-date-range-picker';
import ToolbarDropdown from '../atoms/toolbar-dropdown';
import ThemeButton from '../atoms/theme-btn';
import { useAppSelector } from '@/redux/hooks';
import fetchWrapper from '@/utils/fetch-wrapper';
import { toast } from 'react-toastify';

// Define interfaces for API responses
interface UserLinkResponse {
  linkId: string;
  shortUrl: string;
  storeName: string;
  totalClicks: number;
  convertedClicks: number;
  totalCashbackEarned: number;
  status?: string;
  createdAt: string;
}

const LinkGeneratorPerformance = () => {
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const [isHydrated, setIsHydrated] = useState(false);
  const [userLinks, setUserLinks] = useState<UserLinkResponse[]>([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    storeId: '',
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
    page: 1,
    limit: 10,
  });

  // Fetch user links on initial load
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Fetch user links on initial load
  useEffect(() => {
    if (isHydrated && isUserLogin) {
      fetchUserLinks();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isHydrated, isUserLogin]);

  // Function to fetch user links with current filters
  const fetchUserLinks = async () => {
    if (!isUserLogin) {
      return;
    }

    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      console.log('Current filters for API call:', {
        storeId: filters.storeId,
        startDate: filters.startDate
          ? filters.startDate.toISOString()
          : undefined,
        endDate: filters.endDate ? filters.endDate.toISOString() : undefined,
        page: filters.page,
        limit: filters.limit,
      });

      if (filters.storeId) {
        queryParams.append('storeId', filters.storeId);
      }

      // Make sure dates are properly formatted for the API
      if (filters.startDate) {
        const startDateStr = filters.startDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        console.log('Adding startDate to API call:', startDateStr);
        queryParams.append('startDate', startDateStr);
      }

      if (filters.endDate) {
        const endDateStr = filters.endDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        console.log('Adding endDate to API call:', endDateStr);
        queryParams.append('endDate', endDateStr);
      }

      queryParams.append('page', filters.page.toString());
      queryParams.append('limit', filters.limit.toString());

      console.log('Fetching links with params:', queryParams.toString());

      const response = await fetchWrapper<{
        items: UserLinkResponse[];
        total: number;
        page: number;
        pages: number;
        limit: number;
      }>(`/api/proxy/links?${queryParams.toString()}`);

      if (response?.items) {
        setUserLinks(response.items);
        console.log('Links fetched:', response.items.length);
      }
    } catch (error) {
      console.error('Error fetching user links:', error);
      toast.error('Failed to load your links. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch user links with a specific store ID
  const fetchUserLinksWithStoreId = async (storeId: string) => {
    if (!isUserLogin) {
      return;
    }

    try {
      setLoading(true);
      const queryParams = new URLSearchParams();

      console.log('Fetching with store ID and current filters:', {
        storeId: storeId,
        startDate: filters.startDate
          ? filters.startDate.toISOString()
          : undefined,
        endDate: filters.endDate ? filters.endDate.toISOString() : undefined,
        page: filters.page,
        limit: filters.limit,
      });

      if (storeId) {
        queryParams.append('storeId', storeId);
      }

      // Make sure dates are properly formatted for the API
      if (filters.startDate) {
        const startDateStr = filters.startDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        console.log('Adding startDate to store ID API call:', startDateStr);
        queryParams.append('startDate', startDateStr);
      }

      if (filters.endDate) {
        const endDateStr = filters.endDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        console.log('Adding endDate to store ID API call:', endDateStr);
        queryParams.append('endDate', endDateStr);
      }

      queryParams.append('page', filters.page.toString());
      queryParams.append('limit', filters.limit.toString());

      console.log(
        'Fetching links with store ID:',
        storeId,
        queryParams.toString()
      );

      const response = await fetchWrapper<{
        items: UserLinkResponse[];
        total: number;
        page: number;
        pages: number;
        limit: number;
      }>(`/api/proxy/links?${queryParams.toString()}`);

      if (response?.items) {
        setUserLinks(response.items);
        console.log('Links fetched with store ID:', response.items.length);
      }
    } catch (error) {
      console.error('Error fetching user links:', error);
      toast.error('Failed to load your links. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleGenerateNewLink = () => {
    if (!isUserLogin) {
      // Show login popup if not logged in
      toast.info('Please log in to generate a new link');
      return;
    }

    // Navigate to the top of the page where the link generator is
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <section className='w-full py-8 md:py-16 lg:py-24 flex flex-col items-center gap-y-4 bg-[#EEEEEE] dark:bg-[#212327] rounded-lg shadow-lg px-4 md:px-8'>
      <h2 className='text-base md:text-lg font-medium text-primary text-left w-full max-w-6xl'>
        Your Shared Links Performance
      </h2>

      <div className='flex flex-col md:flex-row items-start sm:items-center justify-between gap-4 sm:gap-2 w-full max-w-6xl my-3'>
        <div className='flex flex-col sm:flex-row items-start sm:items-center justify-start gap-2 w-full sm:w-auto'>
          <ToolbarDropdown
            className={clsx(
              '!flex py-3 px-3 !w-full sm:!w-[140px] !justify-center !items-center'
            )}
            items={[
              {
                label: 'All Stores',
                key: 'all',
              },

              {
                label: 'Flipkart',
                key: '66f206ec28628113b48568ea',
              },
            ]}
            name='All Stores'
            onClick={(item) => {
              const storeId = typeof item === 'string' ? item : item.key;
              const storeIdValue = storeId === 'all' ? '' : storeId.toString();

              // Update links filters
              const newFilters = {
                ...filters,
                storeId: storeIdValue,
              };
              setFilters(newFilters);

              // Fetch updated data with the new storeId directly
              fetchUserLinksWithStoreId(storeIdValue);
            }}
          >
            <Store className='w-[14px] lg:w-[20px] mr-4 text-[#7366D9] dark:text-white transition-transform duration-300 hover:scale-110' />
          </ToolbarDropdown>

          {/* Date picker */}
          <SimpleDateRangePicker
            onChange={(dates) => {
              console.log('Date picker onChange called with dates:', dates);

              if (dates === null) {
                // Handle clearing dates
                console.log('Received clear dates signal from date picker');

                // Create new filter objects without dates
                const newFilters = {
                  ...filters,
                  startDate: undefined,
                  endDate: undefined,
                };

                // Update state
                setFilters(newFilters);

                // Fetch links without date filters
                fetchUserLinks();

                return;
              }

              if (dates?.startDate && dates?.endDate) {
                console.log('Setting new date filters:', {
                  startDate: dates.startDate.toISOString(),
                  endDate: dates.endDate.toISOString(),
                });

                // Create new filter objects
                const newFilters = {
                  ...filters,
                  startDate: dates.startDate,
                  endDate: dates.endDate,
                };

                // Update state
                setFilters(newFilters);

                // Fetch links with new date filters
                fetchUserLinks();
              } else {
                console.log(
                  'Date picker onChange called but dates are incomplete:',
                  dates
                );
              }
            }}
            rootClassName={clsx(
              'flex !w-full sm:!w-[140px] !justify-center !items-center'
            )}
          />
        </div>
        <ThemeButton
          className='!w-full md:!w-auto sm:!flex-shrink-0 !h-[44px] !text-sm !font-medium min-w-[160px]'
          onClick={handleGenerateNewLink}
          text='Generate New Link'
        />
      </div>
      {isHydrated && isUserLogin ? (
        loading ? (
          <div className='w-full max-w-6xl bg-white dark:bg-[#212327] p-4 md:p-6 rounded-lg text-center'>
            <p className='text-base md:text-lg'>Loading...</p>
          </div>
        ) : userLinks.length > 0 ? (
          <div className='w-full max-w-6xl overflow-x-auto'>
            <LinkGeneratorTable
              links={userLinks.map((link) => ({
                productName: link.shortUrl || 'Unknown',
                storeName: link.storeName || 'Unknown',
                clicks: link.totalClicks || 0,
                conversations: link.convertedClicks || 0,
                cashbackEarned: link.totalCashbackEarned || 0,
                status: 'approved' as 'approved' | 'pending' | 'rejected', // Default to approved since status is missing
                createdAt: link?.createdAt,
              }))}
            />
          </div>
        ) : (
          <div className='w-full max-w-6xl bg-white dark:bg-[#212327] p-4 md:p-6 rounded-lg text-center'>
            <p className='text-base md:text-lg'>No data to show</p>
            <p className='text-sm text-gray-500 mt-2'>
              Try changing your filters or generate new links
            </p>
          </div>
        )
      ) : isHydrated ? (
        <div className='w-full max-w-6xl bg-white dark:bg-[#212327] p-4 md:p-6 rounded-lg text-center'>
          <p className='text-base md:text-lg'>
            Please log in to view your shared links performance
          </p>
        </div>
      ) : (
        <div className='w-full max-w-6xl bg-white dark:bg-[#212327] p-4 md:p-6 rounded-lg text-center'>
          <p className='text-base md:text-lg'>Loading...</p>
        </div>
      )}
    </section>
  );
};

export default LinkGeneratorPerformance;

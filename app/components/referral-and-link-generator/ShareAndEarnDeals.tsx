'use client';

import React, { useMemo, useRef, useState, useEffect } from 'react';
import CommonContainer from '../common-container';
import MainOfferCard from '../cards/main-offer-card';
import PlusIcon from '../svg/plus-icon';
import CopySVG from '../svg/copy';
import { LeftRoundButton, RightRoundButton } from '../atoms/rounded-buttons';
import { formatIndRs, sideScroll } from '@/utils/helpers';
import { useOverflowing } from '@/utils/custom-hooks';
import type { ShareAndEarnOfferDto } from '@/services/api/data-contracts';
import { useWindowSize } from 'usehooks-ts';
import fetchWrapper from '@/utils/fetch-wrapper';
import { toast } from 'react-toastify';

// Generate placeholder array for skeleton loading
const generatePlaceholderArray = (count: number) => Array(count).fill(null);

const ShareAndEarnDeals = () => {
  const [offers, setOffers] = useState<ShareAndEarnOfferDto[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  const { width = 0 } = useWindowSize();

  // Limit offers to 6 on mobile
  const displayOffers = useMemo(() => {
    if (offers.length > 0 && offers.length > 6 && width < 640) {
      return offers.slice(0, 6);
    }
    return offers;
  }, [offers, width]);

  const isOverflowing: boolean = useOverflowing(containerRef, offers);

  // Fetch share and earn offers
  useEffect(() => {
    const fetchOffers = async () => {
      try {
        setLoading(true);
        const data = await fetchWrapper<ShareAndEarnOfferDto[]>(
          '/api/proxy/links/share-and-earn/offers',
          {
            method: 'GET',
          }
        );

        setOffers(data);
      } catch (err) {
        console.error('Error fetching share and earn offers:', err);
        setError('Failed to load offers');
      } finally {
        setLoading(false);
      }
    };

    fetchOffers();
  }, []);

  // If error occurred, don't render anything
  if (error && !loading) {
    return null;
  }

  // If no offers and not loading, don't render
  if (!loading && displayOffers.length === 0) {
    return null;
  }

  const handleCopyShareLink = async (
    offer: ShareAndEarnOfferDto,
    event: React.MouseEvent
  ) => {
    event.preventDefault();
    event.stopPropagation();

    const shareMessage = `Hey! I found the product you were looking for on ${offer.storeName}. Looks like a great deal — thought you'd want to check it out!\n\n${offer.shortUrl}`;

    try {
      await navigator.clipboard.writeText(shareMessage);
      toast.success('Share message copied to clipboard!');
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
      toast.error('Failed to copy message');
    }
  };

  return (
    <CommonContainer className='shareAndEarnDealsSection lg:flex lg:rounded-none'>
      <div className='overflow-hidden grow customScrollbar relative'>
        {isOverflowing && (
          <LeftRoundButton
            classCont='absolute -left-0 top-1/2 transform -translate-y-1/2 z-10'
            onClick={() => sideScroll(containerRef.current, 10, 400, -10)}
          />
        )}

        {isOverflowing && (
          <RightRoundButton
            classCont='absolute right-2 top-1/2 transform -translate-y-1/2 z-10'
            onClick={() => sideScroll(containerRef.current, 10, 400, 10)}
          />
        )}
        <div
          className='mt-[20px] grid grid-cols-2 md:grid-cols-3 gap-4 lg:gap-6 lg:flex relative lg:mt-[25px] px-[8px] pt-[10px] lg:pb-[30px] lg:mx-[30px] overflow-x-auto overflow-y-visible w-full lg:w-[calc(100%-60px)] customScrollbar'
          ref={containerRef}
        >
          {loading
            ? // Render skeleton loaders when loading
              generatePlaceholderArray(6).map((_, index) => (
                <MainOfferCard
                  duration=''
                  isLoading={true}
                  isOfferUpto={false}
                  key={`skeleton-share-earn-${index}`}
                  offerTitle=''
                  productImgUrl=''
                  showNewBadge={false}
                  storeImgUrl=''
                  storeName=''
                  uid={index + 2000}
                >
                  {/* Empty children for skeleton */}
                  <span />
                  <span />
                  <span />
                  <span />
                </MainOfferCard>
              ))
            : displayOffers.map((offer) => (
                <MainOfferCard
                  duration={offer.endDate}
                  hideCbTag={offer.hideCbTag}
                  isAutoGenerated={offer.isAutoGenerated}
                  isOfferUpto={Boolean(offer?.offerCaption?.trim())}
                  key={offer.uid}
                  offerTitle={offer.offerTitle}
                  productImgUrl={offer.productImage || ''}
                  showNewBadge={false}
                  storeImgUrl={offer.storeLogoUrl}
                  storeName={offer.storeName}
                  uid={offer.uid}
                >
                  {/* first child */}
                  <p>{offer.offerTitle}</p>
                  {/* second child */}
                  <p className='text-primary dark:text-white font-medium text-[9px]'>
                    {offer.salePrice > 0 && (
                      <>
                        Offer Applied Price
                        <span className='font-nexa font-black ml-[4px] text-[10px] sm:text-[11px]'>
                          {formatIndRs(offer.salePrice)}
                        </span>
                      </>
                    )}
                  </p>
                  {/* third child */}
                  <>
                    <PlusIcon className='text-black shrink-0' />
                    <p className='text-[10px] text-black font-bold leading-none ml-[4px] mt-[2px] truncate'>
                      {offer.offerCaption}
                    </p>
                  </>
                  {/* fourth child */}
                  <button
                    className='bg-[#7366D9] p-[4px] h-full rounded-b-[6px] relative hover:bg-[#6355C8] active:bg-[#7366D9] w-full'
                    onClick={(event) => handleCopyShareLink(offer, event)}
                    type='button'
                  >
                    <div className='border-dashed border-[0.5px] border-[#E0DCFF] rounded-b-[4px] h-full flex items-center justify-center'>
                      <span className='text-[10px] sm:text-[12px] font-semibold text-white'>
                        Copy Link
                      </span>
                      <CopySVG className='text-white absolute top-[16px] right-[12px]' />
                    </div>
                  </button>
                </MainOfferCard>
              ))}
        </div>
      </div>
    </CommonContainer>
  );
};

export default ShareAndEarnDeals;


export const topReferrersData = {
  all: [
    {
      rank: 1,
      user: '<PERSON>',
      totalReferrals: 142,
      cashbackEarned: 3242,
      badge: 'gold',
      avatar: `https://ui-avatars.com/api/?name=${'<PERSON>'}&background=random&rounded=true&format=png`
    },
    {
      rank: 2,
      user: '<PERSON><PERSON><PERSON>',
      totalReferrals: 98,
      cashbackEarned: 1820,
      badge: 'silver',
      avatar: `https://ui-avatars.com/api/?name=${'<PERSON><PERSON><PERSON>'}&background=random&rounded=true&format=png`
    },
    {
      rank: 3,
      user: '<PERSON><PERSON><PERSON>',
      totalReferrals: 87,
      cashbackEarned: 1420,
      badge: 'bronze',
      avatar: `https://ui-avatars.com/api/?name=${'<PERSON><PERSON><PERSON>'}&background=random&rounded=true&format=png`
    },
    {
      rank: 4,
      user: 'Soham S.',
      totalReferrals: 65,
      cashbackEarned: 1082,
      avatar: `https://ui-avatars.com/api/?name=${'Soham <PERSON>.'}&background=random&rounded=true&format=png`
    },
    {
      rank: 5,
      user: 'Akshay <PERSON>',
      totalReferrals: 54,
      cashbackEarned: 890,
      avatar: `https://ui-avatars.com/api/?name=${'Akshay S.'}&background=random&rounded=true&format=png`
    },
  ],
  month: [
    {
      rank: 1,
      user: 'Raj S.',
      totalReferrals: 42,
      cashbackEarned: 1242,
      badge: 'gold',
      avatar: `https://ui-avatars.com/api/?name=${'Raj S.'}&background=random&rounded=true&format=png`
    },
    {
      rank: 2,
      user: 'Neema Jacob',
      totalReferrals: 38,
      cashbackEarned: 982,
      badge: 'silver',
      avatar: `https://ui-avatars.com/api/?name=${'Neema Jacob'}&background=random&rounded=true&format=png`
    },
    {
      rank: 3,
      user: 'Anjali S.',
      totalReferrals: 35,
      cashbackEarned: 820,
      badge: 'bronze',
      avatar: `https://ui-avatars.com/api/?name=${'Anjali S.'}&background=random&rounded=true&format=png`
    },
    {
      rank: 4,
      user: 'Soham S.',
      totalReferrals: 29,
      cashbackEarned: 682,
      avatar: `https://ui-avatars.com/api/?name=${'Soham S.'}&background=random&rounded=true&format=png`
    },
    {
      rank: 5,
      user: 'Akshay S.',
      totalReferrals: 24,
      cashbackEarned: 582,
      avatar: `https://ui-avatars.com/api/?name=${'Akshay S.'}&background=random&rounded=true&format=png`
    },
  ],
  week: [
    {
      rank: 1,
      user: 'Neema Jacob',
      totalReferrals: 15,
      cashbackEarned: 420,
      badge: 'gold',
      avatar: `https://ui-avatars.com/api/?name=${'Neema Jacob'}&background=random&rounded=true&format=png`
    },
    {
      rank: 2,
      user: 'Raj S.',
      totalReferrals: 12,
      cashbackEarned: 380,
      badge: 'silver',
      avatar: `https://ui-avatars.com/api/?name=${'Raj S.'}&background=random&rounded=true&format=png`
    },
    {
      rank: 3,
      user: 'Akshay S.',
      totalReferrals: 10,
      cashbackEarned: 320,
      badge: 'bronze',
      avatar: `https://ui-avatars.com/api/?name=${'Akshay S.'}&background=random&rounded=true&format=png`
    },
    {
      rank: 4,
      user: 'Anjali S.',
      totalReferrals: 8,
      cashbackEarned: 260,
      avatar: `https://ui-avatars.com/api/?name=${'Anjali S.'}&background=random&rounded=true&format=png`
    },
    {
      rank: 5,
      user: 'Soham S.',
      totalReferrals: 6,
      cashbackEarned: 180,
      avatar: `https://ui-avatars.com/api/?name=${'Soham S.'}&background=random&rounded=true&format=png`
    },
  ]
};

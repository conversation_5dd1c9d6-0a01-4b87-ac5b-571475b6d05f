'use client';

import React, { useState } from 'react';
import { Slider } from 'antd';

const CalculateMonthlyProfit = () => {
  const [electronics, setElectronics] = useState(10);
  const [beautyFashion, setBeautyFashion] = useState(15);
  const [grocery, setGrocery] = useState(30);
  const [medicines, setMedicines] = useState(5);
  const [cards, setCards] = useState(3);
  const [travel, setTravel] = useState(2);

  // Calculate estimated profit based on category values
  const calculateProfit = () => {
    // const baseRate = 100; // Base calculation rate
    const profit =
      electronics * 350 +
      beautyFashion * 155 +
      grocery * 80 +
      medicines * 140 +
      travel * 250 +
      cards * 1200;
    return Math.round(profit);
  };

  const categories = [
    {
      name: 'Electronics',
      value: electronics,
      setter: setElectronics,
      max: 50,
      color: '#574abe',
    },
    {
      name: 'Beauty & Fashion & Accessories',
      value: beautyFashion,
      setter: setBeautyFashion,
      max: 40,
      color: '#574abe',
    },
    {
      name: 'Grocery & Home Essentials',
      value: grocery,
      setter: setGrocery,
      max: 60,
      color: '#574abe',
    },
    {
      name: 'Medicines & Healthcare',
      value: medicines,
      setter: setMedicines,
      max: 25,
      color: '#574abe',
    },
    {
      name: 'Cards & Subscriptions',
      value: cards,
      setter: setCards,
      max: 20,
      color: '#574abe',
    },
    {
      name: 'Travel',
      value: travel,
      setter: setTravel,
      max: 15,
      color: '#574abe',
    },
  ];

  return (
    <div className='w-full p-6 my-8'>
      <div className='text-center mb-8'>
        <h2 className='text-3xl font-bold text-blackWhite mb-2'>
          Calculate Your Monthly Earnings
        </h2>
        <p className='text-blackWhite'>Choose Number Of Orders</p>
      </div>

      <div className='max-w-4xl mx-auto'>
        {/* Sliders Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-8 mb-8'>
          {/* Left Column */}
          <div className='space-y-8'>
            {categories.slice(0, 3).map((category) => (
              <div className='space-y-4' key={category.name}>
                <div className='flex justify-between items-center'>
                  <h3 className='text-lg font-semibold text-blackWhite'>
                    {category.name}
                  </h3>
                  <span className='bg-[#574abe] text-white px-3 py-1 rounded-full text-sm font-medium'>
                    {category.value}
                  </span>
                </div>
                <Slider
                  className='custom-slider'
                  handleStyle={{
                    borderColor: category.color,
                    backgroundColor: category.color,
                  }}
                  max={category.max}
                  min={0}
                  onChange={category.setter}
                  trackStyle={{ backgroundColor: category.color }}
                  value={category.value}
                />
              </div>
            ))}
          </div>

          {/* Right Column */}
          <div className='space-y-8'>
            {categories.slice(3).map((category) => (
              <div className='space-y-4' key={category.name}>
                <div className='flex justify-between items-center'>
                  <h3 className='text-lg font-semibold text-blackWhite'>
                    {category.name}
                  </h3>
                  <span className='bg-[#574abe] text-white px-3 py-1 rounded-full text-sm font-medium'>
                    {category.value}
                  </span>
                </div>
                <Slider
                  className='custom-slider'
                  handleStyle={{
                    borderColor: category.color,
                    backgroundColor: category.color,
                  }}
                  max={category.max}
                  min={0}
                  onChange={category.setter}
                  trackStyle={{ backgroundColor: category.color }}
                  value={category.value}
                />
              </div>
            ))}
          </div>
        </div>

        {/* Profit Display */}
        <div className='text-center bg-gray-50 dark:bg-[#212327] rounded-lg p-6'>
          <h3 className='text-xl font-semibold text-blackWhite mb-2'>
            Estimated Earnings:
            <span className='text-3xl font-bold text-[#574abe] ml-2'>
              ₹{calculateProfit().toLocaleString()}*
            </span>
          </h3>
          <p className='text-sm text-blackWhite'>*T&Cs Apply</p>
        </div>
      </div>

      <style jsx>{`
        .custom-slider .ant-slider-track {
          background-color: #574abe !important;
        }
        .custom-slider .ant-slider-handle {
          border-color: #574abe !important;
          background-color: #574abe !important;
        }
        .custom-slider .ant-slider-handle:focus {
          border-color: #574abe !important;
          box-shadow: 0 0 0 5px rgba(239, 68, 68, 0.12) !important;
        }
        .custom-slider .ant-slider-handle:hover {
          border-color: #574abe !important;
        }
      `}</style>
    </div>
  );
};

export default CalculateMonthlyProfit;

'use client';
import React from 'react';
import Image from 'next/image';
import clsx from 'clsx';
import { ArrowDown } from 'lucide-react';

interface AnalyticsItem {
  title: string;
  value: string;
  type: 'increment' | 'decrement';
  ratePercentage: number;
  subTitle: string;
  image: string;
}

interface PerformanceAnalyticsCardsProps {
  analytics: AnalyticsItem[];
}

const PerformanceAnalyticsCards = ({
  analytics,
}: PerformanceAnalyticsCardsProps) => {
  return (
    <section className='w-full py-4 lg:py-4 flex flex-col items-center gap-y-4  px-4 md:px-8'>
      <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4 mb-6 w-full max-w-6xl'>
        {analytics.map((item) => (
          <div
            className='bg-white dark:bg-[#212327] rounded-lg p-3 md:p-4 w-full'
            key={item.title}
          >
            <div className='flex items-start gap-x-2 w-full justify-between'>
              <div className='flex flex-col items-start gap-y-1 md:gap-y-1.5 flex-1'>
                <h3 className='text-base md:text-lg font-semibold text-left w-full'>
                  {item.value}
                </h3>
                <p className='text-xs md:text-sm font-light text-left w-full text-gray-600 dark:text-gray-300'>
                  {item.title}
                </p>
              </div>
              <Image
                alt={item.title}
                className='mt-1 w-8 h-8 md:w-9 md:h-9 flex-shrink-0'
                height={100}
                src={item.image}
                width={100}
              />
            </div>
            <p
              className={clsx(
                'text-xs font-light text-left w-full flex items-center gap-x-1 mt-2',
                item.type === 'increment' ? 'text-green-500' : 'text-red-500'
              )}
            >
              <ArrowDown
                className={clsx(
                  'w-3 h-3 md:w-4 md:h-4 flex-shrink-0',
                  item.type === 'increment' ? 'rotate-180' : ''
                )}
              />
              <span className='truncate'>
                {item.ratePercentage}% {item.subTitle}
              </span>
            </p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default PerformanceAnalyticsCards;

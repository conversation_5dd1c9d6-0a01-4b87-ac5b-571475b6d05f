import React from 'react';

const AllLinks = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 16 16'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M5.32641 10.8137C4.79441 10.7647 4.26941 10.5197 3.86341 10.0927C2.93941 9.11966 2.93941 7.52366 3.86341 6.55066L5.39641 4.94066C5.61288 4.71028 5.87424 4.52669 6.16438 4.40119C6.45452 4.27568 6.76729 4.21094 7.08341 4.21094C7.39953 4.21094 7.7123 4.27568 8.00244 4.40119C8.29258 4.52669 8.55394 4.71028 8.77041 4.94066C9.69441 5.91366 9.69441 7.50966 8.77041 8.48266L8.00741 9.28766'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.2'
      />
      <path
        d='M10.6741 5.18555C11.2061 5.23455 11.7311 5.47955 12.1371 5.90655C13.0611 6.87955 13.0611 8.47555 12.1371 9.44855L10.6041 11.0585C10.3876 11.2889 10.1263 11.4725 9.83614 11.598C9.546 11.7235 9.23323 11.7883 8.91711 11.7883C8.60099 11.7883 8.28822 11.7235 7.99808 11.598C7.70794 11.4725 7.44658 11.2889 7.23011 11.0585C6.30611 10.0855 6.30611 8.48955 7.23011 7.51655L7.99311 6.71155'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.2'
      />
      <path
        d='M5.9 15H10.1C13.6 15 15 13.6 15 10.1V5.9C15 2.4 13.6 1 10.1 1H5.9C2.4 1 1 2.4 1 5.9V10.1C1 13.6 2.4 15 5.9 15Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='1.2'
      />
    </svg>
  );
};

export default AllLinks;

import React from 'react';

const QuickAccessMask = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 72 84'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <mask
        height='84'
        id='mask0_2116_14435'
        maskUnits='userSpaceOnUse'
        style={{ maskType: 'alpha' }}
        width='72'
        x='0'
        y='0'
      >
        <rect
          fill='url(#paint0_linear_2116_14435)'
          height='84'
          rx='10'
          width='72'
        />
      </mask>
      <g mask='url(#mask0_2116_14435)'>
        <path
          d='M12.4918 -15.6754C17.0521 -15.0825 21.3624 -13.2499 24.9534 -10.3771C28.5444 -7.50426 31.2785 -3.70138 32.858 0.61758C34.4375 4.93654 34.8018 9.60605 33.9114 14.1177C33.021 18.6294 30.91 22.8104 27.8081 26.2055C24.7062 29.6005 20.7323 32.0796 16.3192 33.3727C11.906 34.6659 7.22267 34.7235 2.779 33.5394C-1.66466 32.3554 -5.69834 29.9749 -8.88289 26.6573C-12.0674 23.3396 -14.2808 19.2119 -15.282 14.7235L-2.27023 11.8209C-1.80003 13.9288 -0.760582 15.8673 0.734951 17.4253C2.23048 18.9834 4.12479 20.1013 6.21163 20.6573C8.29847 21.2134 10.4979 21.1863 12.5704 20.579C14.6429 19.9718 16.5091 18.8075 17.9658 17.2131C19.4225 15.6187 20.4139 13.6553 20.8321 11.5365C21.2503 9.41769 21.0791 7.22478 20.3374 5.1965C19.5956 3.16823 18.3116 1.38231 16.6252 0.0331798C14.9388 -1.31595 12.9146 -2.17658 10.773 -2.45502L12.4918 -15.6754Z'
          fill='#5D51C5'
        />
      </g>
      <defs>
        <linearGradient
          gradientUnits='userSpaceOnUse'
          id='paint0_linear_2116_14435'
          x1='77.8065'
          x2='-27.7609'
          y1='84'
          y2='-5.34093'
        >
          <stop stopColor='#3F30B8' />
          <stop offset='1' stopColor='#A196F5' />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default QuickAccessMask;

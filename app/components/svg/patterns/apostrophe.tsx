import React from 'react';

const Apostrophe = ({ ...props }) => {
  return (
    <svg
      fill='none'
      height='71'
      viewBox='0 0 77 71'
      width='77'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M41.6761 55.9629C39.1972 62.0604 34.9108 67.0728 28.8169 71L13.3239 63.2489L1.23944 51.3122C5.37089 48.9352 8.10798 45.7831 9.4507 41.8559L0 37.2052V5.42576L28.8169 2.94541L32.8451 4.9607V2.63536L61.507 0L77 7.75109V38.1354C77 45.0597 75.6573 50.9505 72.9718 55.8079C70.3897 60.5619 66.6714 64.6441 61.8169 68.0546L46.3239 60.3035L41.6761 55.9629ZM34.3944 4.03056V32.7096L44.7746 31.7795C44.7746 36.0167 44 39.5822 42.4507 42.476C41.0047 45.2664 38.9906 47.4367 36.4085 48.9869L46.3239 58.4432C50.5587 55.4461 53.8639 51.6739 56.2394 47.1266C58.7183 42.5793 59.9577 36.9985 59.9577 30.3843V1.70524L34.3944 4.03056ZM1.5493 6.82096V35.5L11.9296 34.5699C11.9296 38.8071 11.2066 42.3726 9.76056 45.2664C8.31455 48.0568 6.30047 50.2271 3.71831 51.7773L13.4789 61.3886C17.8169 58.3916 21.1737 54.6194 23.5493 50.0721C26.0282 45.5247 27.2676 39.944 27.2676 33.3297V4.65065L1.5493 6.82096Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default Apostrophe;

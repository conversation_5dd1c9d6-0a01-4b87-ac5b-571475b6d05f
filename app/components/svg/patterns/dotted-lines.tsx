const DottedLine1 = ({ ...props }) => {
  return (
    <svg
      fill='none'
      height='89'
      viewBox='0 0 190 89'
      width='190'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M187.5 1C189 20 196.5 55.8707 152.797 55.8707C109.094 55.8707 4.9999 33 5 79.5'
        stroke='currentColor'
        strokeDasharray='2 2'
      />
      <path
        d='M8.3002 81.4751L5.58353 84.7351C5.2627 85.1201 4.7377 85.1201 4.41686 84.7351L1.7002 81.4751'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeMiterlimit='10'
        strokeWidth='1.5'
      />
      <path
        d='M122.403 57.0429L120.033 54.219C119.753 53.8855 119.814 53.4063 120.169 53.154L123.174 51.0181'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeMiterlimit='10'
        strokeWidth='1.5'
      />
    </svg>
  );
};

const DottedLine2 = ({ ...props }) => {
  return (
    <svg
      fill='none'
      height='87'
      viewBox='0 0 189 87'
      width='189'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M184 78.5C184 59.5 192.5 23.6293 148.797 23.6293C105.094 23.6293 0.999905 46.5 1 0'
        stroke='currentColor'
        strokeDasharray='2 2'
      />
      <path
        d='M187.3 79.4751L184.584 82.7351C184.263 83.1201 183.738 83.1201 183.417 82.7351L180.7 79.4751'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeMiterlimit='10'
        strokeWidth='1.5'
      />
      <path
        d='M81.8895 25.3923L84.8915 27.7448C85.246 28.0226 85.3061 28.5378 85.025 28.8897L82.6445 31.8696'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeMiterlimit='10'
        strokeWidth='1.5'
      />
    </svg>
  );
};

const DottedLine3 = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 137 124'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M1 1C3.78926 43.4821 33.9132 43.4821 53.438 47.8393C72.9628 52.1964 130.979 56.5536 136 123'
        stroke='currentColor'
        strokeDasharray='2 2'
      />
    </svg>
  );
};

const DottedLine4 = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 137 120'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M1 119C3.78926 77.9107 33.9132 77.9107 53.438 73.6964C72.9628 69.4821 130.979 65.2679 136 0.999996'
        stroke='currentColor'
        strokeDasharray='2 2'
      />
    </svg>
  );
};

export { DottedLine1, DottedLine2, DottedLine3, DottedLine4 };

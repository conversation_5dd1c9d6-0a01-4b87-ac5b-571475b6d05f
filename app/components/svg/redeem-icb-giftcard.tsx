import React from 'react';
import { motion } from 'framer-motion';

const <PERSON><PERSON><PERSON><PERSON><PERSON>bGiftcard = ({ ...props }) => {
  return (
    <motion.svg
      fill='none'
      viewBox='0 0 14 18'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
      animate={{ scale: 1, opacity: 1 }}
      initial={{ scale: 0.9, opacity: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.1, rotate: 5 }}
      whileTap={{ scale: 0.9 }}
    >
      <motion.path
        animate={{ opacity: 1 }}
        d='M11.6129 11.0314V14.5154C11.6129 15.7986 10.5735 16.838 9.2903 16.838H4.64518C3.36197 16.838 2.32262 15.7986 2.32262 14.5154V11.0314C2.32262 10.712 2.58391 10.4508 2.90326 10.4508H4.04712C4.36647 10.4508 4.62776 10.712 4.62776 11.0314V12.8547C4.62837 13.163 4.75073 13.4587 4.96821 13.6772C5.1857 13.8958 5.48071 14.0197 5.78904 14.0218C6.00969 14.0218 6.23033 13.9579 6.42194 13.8302L6.97355 13.4702L7.48451 13.8128C7.8387 14.0508 8.2916 14.0799 8.66902 13.8766C9.05224 13.6734 9.2903 13.2844 9.2903 12.8489V11.0314C9.2903 10.712 9.55159 10.4508 9.87094 10.4508H11.0322C11.3516 10.4508 11.6129 10.712 11.6129 11.0314ZM12.4838 8.12812V8.70877C12.4838 9.3475 12.1761 9.87009 11.3225 9.87009H2.61294C1.72456 9.87009 1.45166 9.3475 1.45166 8.70877V8.12812C1.45166 7.48939 1.72456 6.9668 2.61294 6.9668H11.3225C12.1761 6.9668 12.4838 7.48939 12.4838 8.12812Z'
        fill='currentColor'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      />
      <motion.path
        animate={{ opacity: 1 }}
        d='M6.7581 6.96897H3.55297C3.4575 6.86541 3.40595 6.72884 3.4092 6.58802C3.41245 6.44721 3.47024 6.31315 3.57039 6.21411L4.3949 5.38958C4.49925 5.28639 4.64008 5.22852 4.78683 5.22852C4.93358 5.22852 5.07441 5.28639 5.17876 5.38958L6.7581 6.96897ZM10.3772 6.96897H7.1721L8.75144 5.38958C8.85579 5.28639 8.99662 5.22852 9.14337 5.22852C9.29013 5.22852 9.43096 5.28639 9.53531 5.38958L10.3598 6.21411C10.5688 6.42315 10.5747 6.75413 10.3772 6.96897ZM8.11216 10.4523C8.43151 10.4523 8.6928 10.7136 8.6928 11.033V12.8505C8.6928 13.315 8.17603 13.5937 7.7928 13.3324L7.27023 12.984C7.17542 12.9216 7.06439 12.8883 6.95088 12.8883C6.83736 12.8883 6.72633 12.9216 6.63152 12.984L6.08572 13.344C5.7025 13.5995 5.19154 13.3208 5.19154 12.8621V11.033C5.19154 10.7136 5.45282 10.4523 5.77218 10.4523H8.11216Z'
        fill='currentColor'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      />
      <motion.g
        animate={{ opacity: 1 }}
        clipPath='url(#clip0_0_1)'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      >
        <motion.path
          animate={{ opacity: 1, scale: 1 }}
          d='M8.25803 3.64681C8.18617 4.226 7.7115 4.70067 7.13231 4.77252C6.78175 4.81607 6.45514 4.72027 6.20038 4.53301C6.0545 4.42632 6.08933 4.19987 6.26353 4.14761C6.58702 4.04876 6.88129 3.87198 7.12048 3.6328C7.35966 3.39361 7.53644 3.09934 7.63529 2.77585C7.68755 2.60383 7.914 2.56899 8.02069 2.7127C8.21555 2.98263 8.30041 3.3166 8.25803 3.64681Z'
          fill='currentColor'
          initial={{ opacity: 0, scale: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        />
        <motion.path
          animate={{ opacity: 1 }}
          d='M5.66028 0.435547C4.70004 0.435547 3.92053 1.21506 3.92053 2.17529C3.92053 3.13552 4.70004 3.91503 5.66028 3.91503C6.62051 3.91503 7.40002 3.13552 7.40002 2.17529C7.39784 1.21506 6.62051 0.435547 5.66028 0.435547ZM5.4556 1.93142L5.98035 2.11432C6.16979 2.18182 6.26124 2.31464 6.26124 2.51932C6.26124 2.75448 6.07398 2.94827 5.84535 2.94827H5.82576V2.95915C5.82576 3.04843 5.75173 3.12246 5.66245 3.12246C5.57318 3.12246 5.49915 3.04843 5.49915 2.95915V2.94609C5.25746 2.9352 5.06367 2.7327 5.06367 2.48013C5.06367 2.39085 5.1377 2.31682 5.22697 2.31682C5.31625 2.31682 5.39028 2.39085 5.39028 2.48013C5.39028 2.55851 5.44689 2.62166 5.51657 2.62166H5.84318C5.89326 2.62166 5.93245 2.57593 5.93245 2.51932C5.93245 2.44311 5.91939 2.43876 5.86931 2.42134L5.34455 2.23843C5.1573 2.17311 5.06367 2.04029 5.06367 1.83344C5.06367 1.59828 5.25092 1.40449 5.47955 1.40449H5.49915V1.39578C5.49915 1.30651 5.57318 1.23248 5.66245 1.23248C5.75173 1.23248 5.82576 1.30651 5.82576 1.39578V1.40885C6.06745 1.41973 6.26124 1.62223 6.26124 1.87481C6.26124 1.96408 6.18721 2.03811 6.09793 2.03811C6.00866 2.03811 5.93463 1.96408 5.93463 1.87481C5.93463 1.79642 5.87802 1.73328 5.80834 1.73328H5.48173C5.43165 1.73328 5.39246 1.779 5.39246 1.83562C5.39028 1.90965 5.40334 1.914 5.4556 1.93142Z'
          fill='currentColor'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        />
      </motion.g>
      <defs>
        <clipPath id='clip0_0_1'>
          <rect
            fill='currentColor'
            height='5.22593'
            transform='translate(3.48511)'
            width='5.22576'
          />
        </clipPath>
      </defs>
    </motion.svg>
  );
};

export const RedeemHistory = ({ ...props }) => {
  return (
    <motion.svg
      fill='none'
      viewBox='0 0 16 16'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
      animate={{ scale: 1, opacity: 1 }}
      initial={{ scale: 0.9, opacity: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.1, rotate: 5 }}
      whileTap={{ scale: 0.9 }}
    >
      <motion.path
        animate={{ opacity: 1 }}
        d='M6.81015 9.89454C7.44394 9.89454 9.42346 9.89454 9.42346 9.89454C9.94838 9.89454 10.3737 9.46925 10.3737 8.94434C10.3737 8.41943 9.94838 7.99414 9.42346 7.99414C8.94836 7.99414 7.99816 7.99414 5.62263 7.99414C3.24664 7.99414 2.49466 8.9839 1.66311 9.81545L0.154314 11.1333C0.056657 11.2183 0 11.3418 0 11.4712V15.8512C0 15.9098 0.0341879 15.963 0.0874074 15.9874C0.140627 16.0114 0.203128 16.0031 0.247566 15.965L3.16363 13.4649C3.26667 13.3775 3.40339 13.3404 3.5362 13.3643L8.0802 14.1905C8.39711 14.2482 8.72377 14.1754 8.98696 13.9893C8.98696 13.9893 14.7873 9.95657 15.2151 9.6001C15.6233 9.22559 15.6184 8.68212 15.2439 8.27389C14.8689 7.8647 14.1696 7.95161 13.7033 8.29683C13.2761 8.65327 10.3571 10.5718 10.3571 10.5718H6.81018L6.79896 10.5766C6.61196 10.5708 6.46596 10.414 6.4718 10.2275C6.47864 10.0405 6.6349 9.89401 6.82143 9.90035L6.81015 9.89454Z'
        fill='currentColor'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      />
      <motion.path
        animate={{ opacity: 1 }}
        d='M11.0959 0C10.2316 0 9.47668 0.461912 9.0592 1.15139C10.3942 1.64115 11.3512 2.92144 11.3512 4.4239C11.3512 4.53474 11.3449 4.64412 11.3346 4.75253C12.5383 4.6324 13.4782 3.61726 13.4782 2.3819C13.4782 1.06642 12.4118 0 11.0959 0Z'
        fill='currentColor'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      />
      <motion.path
        animate={{ opacity: 1 }}
        d='M7.94814 3.78906H7.57263V4.54542H7.94814C8.18592 4.54542 8.3793 4.37548 8.3793 4.167C8.37927 3.959 8.18592 3.78906 7.94814 3.78906Z'
        fill='currentColor'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      />
      <motion.path
        animate={{ opacity: 1 }}
        d='M7.86295 1.70117C6.35952 1.70117 5.14026 2.92044 5.14026 4.42386C5.14026 5.92779 6.35952 7.14655 7.86295 7.14655C9.36637 7.14655 10.5856 5.92779 10.5856 4.42386C10.5856 2.92044 9.36641 1.70117 7.86295 1.70117ZM7.94842 4.9644H7.57291V5.51469C7.57291 5.62066 7.48698 5.7061 7.38151 5.7061H7.28532C7.17935 5.7061 7.09391 5.62066 7.09391 5.51469V3.5596C7.09391 3.45413 7.17935 3.36819 7.28532 3.36819H7.94842C8.45086 3.36819 8.85909 3.7261 8.85909 4.16605C8.85906 4.60649 8.45086 4.9644 7.94842 4.9644Z'
        fill='currentColor'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      />
    </motion.svg>
  );
};
export default RedeemIcbGiftcard;

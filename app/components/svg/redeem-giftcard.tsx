import React from 'react';
import { motion } from 'framer-motion';

const RedeemGiftcard = ({ ...props }) => {
  return (
    <motion.svg
      fill='none'
      viewBox='0 0 15 20'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
      animate={{ scale: 1, opacity: 1 }}
      initial={{ scale: 0.9, opacity: 0 }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.1, rotate: 5 }}
      whileTap={{ scale: 0.9 }}
    >
      <motion.path
        animate={{ opacity: 1 }}
        d='M12.5 12.459V16.209C12.5 17.5902 11.3813 18.709 10 18.709H5C3.61875 18.709 2.5 17.5902 2.5 16.209V12.459C2.5 12.1152 2.78125 11.834 3.125 11.834H4.35625C4.7 11.834 4.98125 12.1152 4.98125 12.459V14.4215C4.9819 14.7534 5.11361 15.0716 5.34771 15.3069C5.58181 15.5421 5.89936 15.6754 6.23125 15.6777C6.46875 15.6777 6.70625 15.609 6.9125 15.4715L7.50625 15.084L8.05625 15.4527C8.4375 15.709 8.925 15.7402 9.33125 15.5215C9.74375 15.3027 10 14.884 10 14.4152V12.459C10 12.1152 10.2812 11.834 10.625 11.834H11.875C12.2188 11.834 12.5 12.1152 12.5 12.459ZM13.4375 9.33398V9.95898C13.4375 10.6465 13.1062 11.209 12.1875 11.209H2.8125C1.85625 11.209 1.5625 10.6465 1.5625 9.95898V9.33398C1.5625 8.64648 1.85625 8.08398 2.8125 8.08398H12.1875C13.1062 8.08398 13.4375 8.64648 13.4375 9.33398Z'
        fill='currentColor'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      />
      <motion.path
        animate={{ opacity: 1 }}
        d='M7.27434 8.0843H3.82434C3.72157 7.97283 3.66609 7.82583 3.66959 7.67426C3.67309 7.52269 3.73529 7.3784 3.84309 7.2718L4.73059 6.3843C4.84291 6.27323 4.9945 6.21094 5.15247 6.21094C5.31043 6.21094 5.46202 6.27323 5.57434 6.3843L7.27434 8.0843ZM11.17 8.0843H7.71997L9.41997 6.3843C9.53229 6.27323 9.68388 6.21094 9.84184 6.21094C9.9998 6.21094 10.1514 6.27323 10.2637 6.3843L11.1512 7.2718C11.3762 7.4968 11.3825 7.85305 11.17 8.0843ZM8.73184 11.8337C9.07559 11.8337 9.35684 12.1149 9.35684 12.4587V14.4149C9.35684 14.9149 8.80059 15.2149 8.38809 14.9337L7.82559 14.5587C7.72354 14.4915 7.60403 14.4557 7.48184 14.4557C7.35965 14.4557 7.24014 14.4915 7.13809 14.5587L6.55059 14.9462C6.13809 15.2212 5.58809 14.9212 5.58809 14.4274V12.4587C5.58809 12.1149 5.86934 11.8337 6.21309 11.8337H8.73184Z'
        fill='currentColor'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      />
      <motion.path
        animate={{ opacity: 1, scale: 1 }}
        d='M8.88728 4.51018C8.80994 5.13361 8.299 5.64455 7.67557 5.7219C7.29822 5.76877 6.94666 5.66565 6.67244 5.46408C6.51541 5.34924 6.55291 5.10549 6.74041 5.04924C7.08862 4.94284 7.40537 4.75256 7.66283 4.4951C7.92029 4.23764 8.11058 3.92089 8.21697 3.57268C8.27322 3.38752 8.51697 3.35002 8.63182 3.50471C8.84157 3.79526 8.9329 4.15474 8.88728 4.51018Z'
        fill='currentColor'
        initial={{ opacity: 0, scale: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      />
      <motion.path
        animate={{ opacity: 1 }}
        d='M6.09141 1.05273C5.05781 1.05273 4.21875 1.8918 4.21875 2.92539C4.21875 3.95898 5.05781 4.79805 6.09141 4.79805C7.125 4.79805 7.96406 3.95898 7.96406 2.92539C7.96172 1.8918 7.125 1.05273 6.09141 1.05273ZM5.87109 2.66289L6.43594 2.85977C6.63984 2.93242 6.73828 3.07539 6.73828 3.2957C6.73828 3.54883 6.53672 3.75742 6.29063 3.75742H6.26953V3.76914C6.26953 3.86523 6.18984 3.94492 6.09375 3.94492C5.99766 3.94492 5.91797 3.86523 5.91797 3.76914V3.75508C5.65781 3.74336 5.44922 3.52539 5.44922 3.25352C5.44922 3.15742 5.52891 3.07773 5.625 3.07773C5.72109 3.07773 5.80078 3.15742 5.80078 3.25352C5.80078 3.33789 5.86172 3.40586 5.93672 3.40586H6.28828C6.34219 3.40586 6.38437 3.35664 6.38437 3.2957C6.38437 3.21367 6.37031 3.20898 6.31641 3.19023L5.75156 2.99336C5.55 2.92305 5.44922 2.78008 5.44922 2.55742C5.44922 2.3043 5.65078 2.0957 5.89687 2.0957H5.91797V2.08633C5.91797 1.99023 5.99766 1.91055 6.09375 1.91055C6.18984 1.91055 6.26953 1.99023 6.26953 2.08633V2.10039C6.52969 2.11211 6.73828 2.33008 6.73828 2.60195C6.73828 2.69805 6.65859 2.77773 6.5625 2.77773C6.46641 2.77773 6.38672 2.69805 6.38672 2.60195C6.38672 2.51758 6.32578 2.44961 6.25078 2.44961H5.89922C5.84531 2.44961 5.80313 2.49883 5.80313 2.55977C5.80078 2.63945 5.81484 2.64414 5.87109 2.66289Z'
        fill='currentColor'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
      />
    </motion.svg>
  );
};

export default RedeemGiftcard;

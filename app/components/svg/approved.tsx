import React from 'react';

const ApprovedSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 15 15'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M8.5 6.49993L7.12378 8.02624L6.74886 7.65132C6.44892 7.35138 5.99901 7.35138 5.69908 7.65132C5.39914 7.95126 5.39914 8.40117 5.69908 8.70111L6.74886 9.61014C6.89883 9.76011 7.0488 9.83509 7.27375 9.83509H7.34874C7.57369 9.83509 7.79865 9.68512 7.87363 9.53515L9.77474 7.32476C9.99969 6.94984 9.92471 6.49993 9.54979 6.27497C9.17486 6.05002 8.72495 6.125 8.5 6.49993Z'
        fill='currentColor'
      />
      <path
        d='M14.0971 5.40183C13.7972 5.10189 13.6472 4.72697 13.6472 4.35204C13.6472 2.70238 12.2975 1.35265 10.6478 1.35265C10.2729 1.35265 9.89798 1.20269 9.59804 0.902746C8.39829 -0.297009 6.52367 -0.297009 5.32392 0.902746C5.09896 1.20269 4.72404 1.35265 4.34911 1.35265C2.69945 1.35265 1.34972 2.70238 1.34972 4.35204C1.34972 4.72697 1.19976 5.10189 0.899817 5.40183C-0.299939 6.60159 -0.299939 8.4762 0.899817 9.67596C1.19976 9.9759 1.34972 10.3508 1.34972 10.7257C1.34972 12.3754 2.69945 13.7251 4.34911 13.7251C4.72404 13.7251 5.09896 13.8751 5.3989 14.175C5.99878 14.6999 6.74862 14.9999 7.49847 14.9999C8.24832 14.9999 9.07315 14.6999 9.59804 14.1001C9.89798 13.8001 10.2729 13.6502 10.6478 13.6502C12.2975 13.6502 13.6472 12.3004 13.6472 10.6508C13.6472 10.2758 13.7972 9.90091 14.0221 9.60097C15.2969 8.4762 15.2969 6.5266 14.0971 5.40183ZM13.0473 8.55119C12.4475 9.15106 12.1475 9.90091 12.1475 10.6508C12.1475 11.4756 11.4727 12.1505 10.6478 12.1505C9.823 12.1505 9.07315 12.4504 8.54826 13.0503C7.94838 13.6501 6.97358 13.6501 6.44869 13.0503C5.84881 12.4504 5.09896 12.1505 4.34911 12.1505C3.52428 12.1505 2.84942 11.4756 2.84942 10.6508C2.84942 9.82593 2.54948 9.07608 1.9496 8.55119C1.34972 7.95131 1.34972 7.05149 1.9496 6.45162C2.54948 5.85174 2.84942 5.10189 2.84942 4.35204C2.84942 3.52721 3.52428 2.85235 4.34911 2.85235C5.17395 2.85235 5.92379 2.55241 6.44869 1.95253C6.74862 1.65259 7.12355 1.50262 7.49847 1.50262C7.8734 1.50262 8.24832 1.65259 8.54826 1.95253C9.14814 2.55241 9.89798 2.85235 10.6478 2.85235C11.4727 2.85235 12.1475 3.52721 12.1475 4.35204C12.1475 5.17688 12.4475 5.92672 13.0473 6.45162C13.6472 7.05149 13.6472 7.95131 13.0473 8.55119Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default ApprovedSVG;

import React from 'react';

const PendingSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 13 13'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M1.2247 3.67306C1.33625 3.72884 1.65232 3.89617 1.99761 3.50839C2.13572 3.3145 2.27383 3.12061 2.43851 2.92937C2.6324 2.70892 2.60318 2.35035 2.38273 2.15646C2.16228 1.96257 1.80372 1.99179 1.60983 2.21224C1.41593 2.43269 1.22204 2.68236 1.05737 2.92937C0.895351 3.17638 0.977688 3.50573 1.2247 3.67306Z'
        fill='currentColor'
      />
      <path
        d='M6.61308 1.0757C7.13898 1.0757 7.66222 1.15803 8.16155 1.32536C8.49621 1.42364 8.74057 1.21381 8.85212 0.993358C8.93446 0.69057 8.76979 0.385125 8.49356 0.302788C7.88532 0.108898 7.27709 0 6.6423 0C6.33951 0 6.08984 0.220451 6.08984 0.525895C6.08984 0.83134 6.31029 1.0757 6.61308 1.0757Z'
        fill='currentColor'
      />
      <path
        d='M11.5333 4.44629C11.7272 4.91641 11.8361 5.41309 11.8919 5.90977C11.8919 5.90977 11.9238 6.52597 12.5001 6.43566C13.0712 6.36129 12.9968 5.80087 12.9968 5.80087C12.941 5.19264 12.7763 4.5844 12.5559 4.03195C12.4178 3.75572 12.115 3.61761 11.8388 3.72916C11.5599 3.86727 11.4218 4.17006 11.5333 4.44629Z'
        fill='currentColor'
      />
      <path
        d='M9.54341 2.01613C9.98431 2.31892 10.3721 2.68014 10.7041 3.09448C10.9511 3.40258 11.3681 3.27509 11.4504 3.17682C11.6709 2.98293 11.7267 2.62437 11.5328 2.40391C11.145 1.9338 10.6749 1.49289 10.1516 1.13167C9.90197 0.966998 9.57262 1.02012 9.40529 1.26979C9.23796 1.51945 9.29374 1.8488 9.54341 2.01613Z'
        fill='currentColor'
      />
      <path
        d='M3.57465 1.93113C4.01555 1.6549 4.51489 1.43445 5.01156 1.29633C5.31435 1.214 5.48168 0.908551 5.39935 0.632323C5.31701 0.329535 5.01156 0.162204 4.73534 0.244542C4.1271 0.409216 3.54809 0.658883 3.02219 0.990888C2.77252 1.15556 2.69019 1.48757 2.85752 1.73723C2.96642 1.90457 3.20015 2.06924 3.57465 1.93113Z'
        fill='currentColor'
      />
      <path
        d='M12.4435 7.12642C12.1408 7.04409 11.8645 7.23798 11.8088 7.54077C11.753 7.79043 11.583 8.37211 11.5803 8.42788C10.8499 10.3269 9.09694 11.6629 7.08366 11.8781C5.10226 12.0852 3.17398 11.1902 2.05578 9.5806L3.07836 9.80637C3.35459 9.86214 3.65738 9.69481 3.71315 9.39202C3.76893 9.1158 3.6016 8.81301 3.29881 8.75723L1.14476 8.28711C1.00665 8.26055 0.576372 8.28711 0.480755 8.70145L0.010636 10.8847C-0.0451408 11.1609 0.12219 11.4637 0.424978 11.5195C0.762295 11.554 1.00399 11.3548 1.08899 11.0786L1.25366 10.3296C2.48341 11.9949 4.70385 13.2273 7.19522 12.9591C9.70783 12.6882 11.838 10.9963 12.6667 8.62177C12.6932 8.55272 12.8181 7.9737 12.8605 7.76387C12.9429 7.45843 12.749 7.1822 12.4435 7.12642Z'
        fill='currentColor'
      />
      <path
        d='M4.37228 7.04391H6.91942C7.22221 7.04391 7.44531 6.79424 7.47188 6.49145V2.71457C7.47188 2.41178 7.22221 2.16211 6.91942 2.16211C6.61663 2.16211 6.36696 2.41178 6.36696 2.71457V5.93899H4.37228C4.06949 5.93899 3.81982 6.18866 3.81982 6.49145C3.81982 6.79424 4.06684 7.04391 4.37228 7.04391Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default PendingSVG;

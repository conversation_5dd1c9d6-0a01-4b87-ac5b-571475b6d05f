import React from 'react';

const Support24SVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 13 14'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M10.1667 11.1163H9.70222C9.21333 11.1163 8.74889 11.3024 8.40667 11.6386L7.36167 12.6533C6.885 13.1156 6.10889 13.1156 5.63222 12.6533L4.58722 11.6386C4.245 11.3024 3.77444 11.1163 3.29167 11.1163H2.83333C1.81889 11.1163 1 10.3178 1 9.33321V2.78311C1 1.7985 1.81889 1 2.83333 1H10.1667C11.1811 1 12 1.7985 12 2.78311V9.33321C12 10.3118 11.1811 11.1163 10.1667 11.1163Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeMiterlimit='10'
      />
      <path
        d='M4 5.80557C4 5.36288 4.38 5.00111 4.845 5.00111C5.31 5.00111 5.69 5.36288 5.69 5.80557C5.69 6.70048 4.355 6.79568 4.06 7.64775C4 7.82387 4.155 8 4.35 8H5.69M8.52 7.99524V5.2772C8.52027 5.21705 8.49991 5.15847 8.462 5.11035C8.4241 5.06223 8.37072 5.02722 8.31 5.01063C8.24886 4.99419 8.18379 4.99688 8.12438 5.0183C8.06497 5.03973 8.01436 5.07875 7.98 5.12963C7.62 5.68181 7.23 6.31015 6.89 6.86232C6.835 6.95277 6.835 7.07177 6.89 7.16221C6.945 7.25266 7.05 7.30978 7.165 7.30978H9'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
};

export default Support24SVG;

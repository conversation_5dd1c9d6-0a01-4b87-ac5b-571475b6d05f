import React from 'react';

const ICBLogoPrimary = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 38 27'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M33.328 8.38327C31.5545 7.02643 29.3827 6.29281 27.1497 6.29622C26.4912 6.29615 25.8343 6.3595 25.188 6.48538C23.6058 6.79449 22.1201 7.47624 20.854 8.47411C20.8195 8.50114 20.7856 8.53161 20.7517 8.55633V3.79464C20.7276 2.8267 20.3435 1.90252 19.6744 1.20263C19.0054 0.502742 18.0994 0.077428 17.1336 0.00977406C17.0444 0.00344967 16.953 0 16.8622 0V17.4622C16.7942 17.8327 16.6926 18.1962 16.5586 18.5483C16.1174 19.7126 15.3419 20.7206 14.3296 21.4454V21.4454C13.4138 22.1011 12.3389 22.4989 11.217 22.5975C10.0951 22.6962 8.96726 22.4919 7.95119 22.0061C6.93513 21.5202 6.06806 20.7706 5.44043 19.8355C4.81281 18.9003 4.44763 17.8139 4.38301 16.6895C4.37956 16.6579 4.37668 16.5895 4.37438 16.517V16.1916C4.37783 16.0703 4.38358 15.9501 4.39278 15.8305C4.46983 14.8637 4.76909 13.9278 5.26725 13.0956C5.7654 12.2634 6.44899 11.5575 7.26473 11.0329C8.08048 10.5083 9.00635 10.1791 9.97019 10.0711C10.934 9.96302 11.9098 10.079 12.8215 10.41C13.2826 9.18991 14.1103 8.14267 15.1908 7.41219C14.3594 6.9857 13.4731 6.67613 12.557 6.49228V6.49228C11.1371 6.20892 9.67319 6.23083 8.26243 6.55656C6.85166 6.88229 5.52631 7.50438 4.37438 8.38155V6.37729H0.483154V26.7505C1.25383 26.7508 2.00727 26.5224 2.64801 26.0942C3.28874 25.6659 3.78793 25.0571 4.08231 24.3449C4.17737 24.4254 4.27434 24.5045 4.37323 24.5823C6.14626 25.9379 8.31687 26.6709 10.5487 26.6677C11.2213 26.6678 11.8922 26.6017 12.5518 26.4705V26.4705C14.1247 26.156 15.6006 25.4729 16.8582 24.4771L16.8858 24.4553C17.6329 23.8603 18.293 23.1636 18.8469 22.3855L18.8549 22.3958C18.8745 22.3682 18.8946 22.3383 18.9124 22.3113C19.0274 22.1515 19.1338 21.9876 19.2344 21.8215C19.2574 21.7847 19.2816 21.7479 19.3034 21.7099C19.4675 21.4378 19.6181 21.158 19.7553 20.8705C19.7869 20.8044 19.8174 20.7377 19.8484 20.671C19.8795 20.6043 19.9117 20.5278 19.9422 20.456C20.0135 20.2887 20.0807 20.119 20.1417 19.9477C20.1664 19.881 20.1888 19.8138 20.213 19.7459C20.2388 19.6666 20.2647 19.5872 20.2894 19.5073C20.3142 19.4274 20.3389 19.348 20.3613 19.267C20.3837 19.1859 20.4067 19.1054 20.4268 19.0244C20.447 18.9433 20.4659 18.8657 20.4843 18.7863C20.5056 18.6943 20.5246 18.6023 20.5418 18.5092C20.5574 18.4339 20.5723 18.358 20.585 18.2792C20.5976 18.2005 20.6149 18.1113 20.6269 18.0268C20.6269 18.0216 20.6298 18.0165 20.6309 18.0107C20.6407 17.932 20.6522 17.8532 20.6626 17.7733C20.6729 17.6934 20.6838 17.6008 20.6913 17.5174C20.7091 17.3564 20.7201 17.1943 20.7304 17.0322C20.7304 17.0103 20.7304 16.9879 20.7304 16.9655C20.7311 16.9624 20.7311 16.9593 20.7304 16.9563C20.7339 16.8896 20.735 16.8246 20.7373 16.7573C20.7373 16.7418 20.7373 16.7263 20.7373 16.7102C20.7373 16.6746 20.7402 16.6383 20.7402 16.6033V15.903C20.8523 14.2764 21.5911 12.7569 22.801 11.664C24.011 10.5712 25.5977 9.99038 27.2272 10.0438C28.8567 10.0972 30.4019 10.7807 31.5377 11.9504C32.6734 13.1201 33.3111 14.6848 33.3165 16.3152V16.3417C33.3147 17.4915 32.9978 18.6188 32.4004 19.6013C31.803 20.5837 30.9479 21.3837 29.9279 21.9144C28.9078 22.4451 27.7619 22.6862 26.6145 22.6116C25.4671 22.5369 24.3621 22.1493 23.4194 21.4909C22.7674 22.6486 21.896 23.6682 20.854 24.4927C22.1203 25.4903 23.6059 26.172 25.188 26.4814C25.8343 26.6073 26.4912 26.6706 27.1497 26.6705C29.8485 26.6707 32.437 25.5999 34.347 23.6931C36.2569 21.7864 37.3321 19.1997 37.3365 16.5009V16.4612C37.3345 14.8972 36.9718 13.3547 36.2766 11.9537C35.5813 10.5527 34.5723 9.33085 33.328 8.38327V8.38327Z'
        fill='#574ABE'
      />
      <path
        d='M2.40499 4.89007C2.88066 4.89007 3.34564 4.74902 3.74114 4.48475C4.13664 4.22049 4.44489 3.84488 4.62692 3.40542C4.80895 2.96597 4.85657 2.48241 4.76378 2.01588C4.67098 1.54936 4.44193 1.12083 4.10558 0.784486C3.76924 0.448141 3.34071 0.219088 2.87419 0.12629C2.40766 0.0334933 1.9241 0.0811202 1.48464 0.263148C1.04519 0.445177 0.669579 0.753431 0.405315 1.14893C0.141051 1.54443 6.87632e-08 2.00941 6.87632e-08 2.48507C-7.54662e-05 2.80092 0.06208 3.11369 0.182916 3.40551C0.303751 3.69734 0.480899 3.96249 0.704238 4.18583C0.927577 4.40917 1.19273 4.58632 1.48455 4.70715C1.77637 4.82799 2.08914 4.89014 2.40499 4.89007V4.89007Z'
        fill='#574ABE'
      />
    </svg>
  );
};

export default ICBLogoPrimary;

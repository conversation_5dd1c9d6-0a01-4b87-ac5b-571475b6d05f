import React from 'react';

const CashbackHandSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 25 17'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        clipRule='evenodd'
        d='M6.55745 15.7938C6.94503 16.0115 7.38573 16.1291 7.83863 16.1291C10.3418 16.1291 14.8525 16.1291 16.6493 16.1291C17.3852 16.1291 18.0863 15.8191 18.581 15.2756L22.6162 10.8441C24.3294 9.12312 24.5549 7.57803 24.2109 6.57207C23.6152 4.83189 21.1974 4.15168 19.4163 5.93279C19.4163 5.93279 17.4105 7.93861 16.4428 8.90625C16.2791 9.06999 16.0579 9.16144 15.8271 9.16144H14.6913C14.7645 8.90886 14.8063 8.62057 14.8063 8.29048C14.8063 5.68195 12.2013 5.6776 12.1943 5.6776H7.83863C7.38573 5.6776 6.94503 5.79517 6.55745 6.01291C6.09323 5.2874 5.27975 4.80664 4.3548 4.80664H2.61288C1.1697 4.80664 0 5.97634 0 7.41952V14.3872C0 15.8304 1.1697 17.0001 2.61288 17.0001H4.3548C5.27975 17.0001 6.09323 16.5193 6.55745 15.7938ZM5.22575 7.41952V14.3872C5.22575 14.868 4.83556 15.2582 4.3548 15.2582H2.61288C2.13211 15.2582 1.74192 14.868 1.74192 14.3872V7.41952C1.74192 6.93875 2.13211 6.54856 2.61288 6.54856H4.3548C4.83556 6.54856 5.22575 6.93875 5.22575 7.41952ZM6.96767 8.29048V13.5162C6.96767 13.747 7.05912 13.9691 7.22286 14.132C7.38573 14.2957 7.60783 14.3872 7.83863 14.3872H16.6493C16.8949 14.3872 17.1283 14.2836 17.2938 14.1024C18.4896 12.789 21.3411 9.65701 21.355 9.64221L21.3699 9.62827C22.4115 8.58573 22.7712 7.74438 22.5631 7.13558C22.327 6.44665 21.3533 6.45972 20.6478 7.16433C20.6478 7.16433 18.642 9.17015 17.6744 10.1378C17.184 10.6281 16.5195 10.9034 15.8271 10.9034H9.58055C9.09978 10.9034 8.70959 10.5132 8.70959 10.0324C8.70959 9.55163 9.09978 9.16144 9.58055 9.16144H12.1934C12.1934 9.16144 13.0644 9.16144 13.0644 8.29048C13.0644 7.42039 12.1952 7.41952 12.1934 7.41952H7.83863C7.60783 7.41952 7.38573 7.51097 7.22286 7.67471C7.05912 7.83758 6.96767 8.05967 6.96767 8.29048Z'
        fill='currentColor'
        fillRule='evenodd'
      />
      <path
        clipRule='evenodd'
        d='M8 4C8.46327 1.71776 10.481 0 12.9 0C15.3189 0 17.3367 1.71776 17.8 4H15.7293C15.3174 2.83481 14.2062 2 12.9 2C11.5938 2 10.4825 2.83481 10.0707 4H8Z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  );
};

export default CashbackHandSVG;

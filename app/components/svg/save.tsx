import React from 'react';

const SaveSVG = ({ ...props }) => {
  return (
    <svg
      fill='currentColor'
      height='14'
      viewBox='0 0 13 14'
      width='13'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M1.7949 13.2158L1.79419 13.2161C1.33745 13.4625 1.04734 13.4153 0.90807 13.3359C0.774706 13.2598 0.6 13.0511 0.6 12.5634V2.70129C0.602075 2.1517 0.829125 1.62064 1.23901 1.22522C1.64964 0.829083 2.21038 0.602042 2.79998 0.6H9.801C11.0291 0.6 12 1.56704 12 2.70023V12.5634C12 13.0504 11.8259 13.2562 11.6926 13.3316C11.5509 13.4118 11.2565 13.4581 10.7939 13.2146L7.26057 11.3151C7.26029 11.315 7.26 11.3148 7.25972 11.3146C6.96431 11.155 6.61418 11.093 6.29888 11.093C5.9825 11.093 5.63265 11.1554 5.33573 11.3123L5.33572 11.3123L5.3319 11.3143L1.7949 13.2158Z'
        stroke='currentColor'
        strokeWidth='1.2'
      />
    </svg>
  );
};

export const ShareSVG = ({ ...props }) => {
  return (
    <svg
      fill='currentColor'
      height='17'
      viewBox='0 0 16 17'
      width='16'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M12.5498 5.50009C13.7925 5.50009 14.7999 4.49271 14.7999 3.25004C14.7999 2.00738 13.7925 1 12.5498 1C11.3072 1 10.2998 2.00738 10.2998 3.25004C10.2998 4.49271 11.3072 5.50009 12.5498 5.50009Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='2'
      />
      <path
        d='M3.55083 10.7501C4.79349 10.7501 5.80087 9.74271 5.80087 8.50004C5.80087 7.25738 4.79349 6.25 3.55083 6.25C2.30816 6.25 1.30078 7.25738 1.30078 8.50004C1.30078 9.74271 2.30816 10.7501 3.55083 10.7501Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='2'
      />
      <path
        d='M12.5498 16.0001C13.7925 16.0001 14.7999 14.9927 14.7999 13.75C14.7999 12.5074 13.7925 11.5 12.5498 11.5C11.3072 11.5 10.2998 12.5074 10.2998 13.75C10.2998 14.9927 11.3072 16.0001 12.5498 16.0001Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='2'
      />
      <path
        d='M5.49219 9.63281L10.6148 12.6179'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='2'
      />
      <path
        d='M10.6073 4.38281L5.49219 7.36787'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
        strokeWidth='2'
      />
    </svg>
  );
};

export const ShareSVG2 = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 10 12'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M8 4C8.82843 4 9.5 3.32843 9.5 2.5C9.5 1.67157 8.82843 1 8 1C7.17157 1 6.5 1.67157 6.5 2.5C6.5 3.32843 7.17157 4 8 4Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M2 7.5C2.82843 7.5 3.5 6.82843 3.5 6C3.5 5.17157 2.82843 4.5 2 4.5C1.17157 4.5 0.5 5.17157 0.5 6C0.5 6.82843 1.17157 7.5 2 7.5Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M8 11C8.82843 11 9.5 10.3284 9.5 9.5C9.5 8.67157 8.82843 8 8 8C7.17157 8 6.5 8.67157 6.5 9.5C6.5 10.3284 7.17157 11 8 11Z'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M3.29492 6.75488L6.70992 8.74488'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M6.70492 3.25488L3.29492 5.24488'
        stroke='currentColor'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
};

export default SaveSVG;

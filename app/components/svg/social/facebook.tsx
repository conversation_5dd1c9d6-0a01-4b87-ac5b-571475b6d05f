import React from 'react';

const FacebookSVG = ({ ...props }) => {
  return (
    <svg
      {...props}
      fill='none'
      viewBox='0 0 27 26'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M27 13.0779C27 10.5786 26.2606 8.13159 24.8696 6.02668C23.4785 3.92178 21.494 2.24709 19.151 1.2009C16.8079 0.154709 14.2045 -0.219168 11.6489 0.123535C9.09332 0.466237 6.69262 1.51117 4.73104 3.13461C2.76946 4.75804 1.32915 6.892 0.580646 9.28382C-0.167859 11.6756 -0.193214 14.2252 0.507583 16.6305C1.20838 19.0358 2.60598 21.1963 4.53491 22.856C6.46384 24.5158 8.84331 25.6053 11.3916 25.9957V16.8553H7.96182V13.0779H11.3916V10.1972C11.3178 9.52435 11.3976 8.84406 11.6254 8.20448C11.8533 7.5649 12.2235 6.9817 12.71 6.4962C13.1964 6.0107 13.7871 5.6348 14.4402 5.39512C15.0934 5.15545 15.7929 5.05787 16.4893 5.10931C17.502 5.12296 18.5123 5.20838 19.5122 5.36489V8.58351H17.8085C17.5184 8.54635 17.2234 8.57284 16.9454 8.66101C16.6673 8.74917 16.4133 8.89677 16.202 9.09288C15.9908 9.28899 15.8278 9.52859 15.725 9.79396C15.6223 10.0593 15.5824 10.3437 15.6084 10.626V13.0779H19.3534L18.7542 16.8596H15.6084V26C18.7843 25.5126 21.6766 23.9434 23.7648 21.5746C25.853 19.2058 27.0002 16.1929 27 13.0779Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default FacebookSVG;

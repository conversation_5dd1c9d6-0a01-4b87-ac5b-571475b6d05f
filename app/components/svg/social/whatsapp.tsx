import React from 'react';

const WhatsappSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 24 24'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M21.9798 11.41C21.6398 5.60996 16.3698 1.13996 10.2998 2.13996C6.11981 2.82996 2.76981 6.21996 2.11981 10.4C1.73981 12.82 2.23981 15.11 3.32981 17L2.43981 20.31C2.23981 21.06 2.92981 21.74 3.66981 21.53L6.92981 20.63C8.40981 21.5 10.1398 22 11.9898 22C17.6298 22 22.3098 17.03 21.9798 11.41ZM16.8798 15.72C16.6386 16.2147 16.2266 16.6054 15.7198 16.82C15.4198 16.95 15.0898 17.01 14.7398 17.01C14.2298 17.01 13.6798 16.89 13.1098 16.64C12.5023 16.3732 11.9256 16.0413 11.3898 15.65C10.8098 15.23 10.2698 14.76 9.74981 14.25C9.22981 13.73 8.76981 13.18 8.34981 12.61C7.93981 12.04 7.60981 11.47 7.36981 10.9C7.12981 10.33 7.00981 9.77996 7.00981 9.25996C7.00981 8.91996 7.06981 8.58996 7.18981 8.28996C7.30981 7.97996 7.49981 7.69996 7.76981 7.44996C8.08981 7.12996 8.43981 6.97996 8.80981 6.97996C8.94981 6.97996 9.08981 7.00996 9.21981 7.06996C9.34981 7.12996 9.46981 7.21996 9.55981 7.34996L10.7198 8.98996C10.8098 9.11996 10.8798 9.22996 10.9198 9.33996C10.9698 9.44996 10.9898 9.54996 10.9898 9.64996C10.9898 9.76996 10.9498 9.88996 10.8798 10.01C10.8098 10.13 10.7198 10.25 10.5998 10.37L10.2198 10.77C10.1598 10.83 10.1398 10.89 10.1398 10.97C10.1398 11.01 10.1498 11.05 10.1598 11.09C10.1798 11.13 10.1898 11.16 10.1998 11.19C10.2898 11.36 10.4498 11.57 10.6698 11.83C11.138 12.3801 11.6496 12.8918 12.1998 13.36C12.4598 13.58 12.6798 13.73 12.8498 13.82C12.8798 13.83 12.9098 13.85 12.9398 13.86C12.9798 13.88 13.0198 13.88 13.0698 13.88C13.1598 13.88 13.2198 13.85 13.2798 13.79L13.6598 13.41C13.7898 13.28 13.9098 13.19 14.0198 13.13C14.1398 13.06 14.2498 13.02 14.3798 13.02C14.4798 13.02 14.5798 13.04 14.6898 13.09C14.7998 13.14 14.9198 13.2 15.0398 13.29L16.6998 14.47C16.8298 14.56 16.9198 14.67 16.9798 14.79C17.0298 14.92 17.0598 15.04 17.0598 15.18C16.9998 15.35 16.9598 15.54 16.8798 15.72Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default WhatsappSVG;

import React from 'react';

const ShareAndEarnSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      height='18'
      viewBox='0 0 18 18'
      width='18'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <circle cx='4' cy='4.5' fill='currentColor' r='2.2' />
      <path
        d='M1.5 11.5C1.5 9.5 2.8 8.5 4 8.5C5.2 8.5 6.5 9.5 6.5 11.5V13H1.5V11.5Z'
        fill='currentColor'
      />

      <circle cx='14' cy='4.5' fill='currentColor' r='2.2' />
      <path
        d='M11.5 11.5C11.5 9.5 12.8 8.5 14 8.5C15.2 8.5 16.5 9.5 16.5 11.5V13H11.5V11.5Z'
        fill='currentColor'
      />

      <path
        d='M7.5 6L10.5 6M10.5 6L9.5 5M10.5 6L9.5 7'
        stroke='currentColor'
        stroke-linecap='round'
        stroke-linejoin='round'
        stroke-width='1.2'
      />

      <circle cx='4' cy='16' fill='currentColor' r='1.5' />
      <text
        fill='white'
        font-family='Arial, sans-serif'
        font-size='1.5'
        font-weight='bold'
        text-anchor='middle'
        x='4'
        y='16.5'
      >
        $
      </text>

      <circle cx='9' cy='15' fill='currentColor' r='1.2' />
      <text
        fill='white'
        font-family='Arial, sans-serif'
        font-size='1.2'
        font-weight='bold'
        text-anchor='middle'
        x='9'
        y='15.4'
      >
        $
      </text>

      <circle cx='14' cy='16' fill='currentColor' r='1.5' />
      <text
        fill='white'
        font-family='Arial, sans-serif'
        font-size='1.5'
        font-weight='bold'
        text-anchor='middle'
        x='14'
        y='16.5'
      >
        $
      </text>

      <path
        d='M4 7L4 14.5'
        opacity='0.7'
        stroke='currentColor'
        stroke-dasharray='1.5,1'
        stroke-width='0.8'
      />
      <path
        d='M14 7L14 14.5'
        opacity='0.7'
        stroke='currentColor'
        stroke-dasharray='1.5,1'
        stroke-width='0.8'
      />
      <path
        d='M6.5 9L11.5 9'
        opacity='0.7'
        stroke='currentColor'
        stroke-dasharray='1.5,1'
        stroke-width='0.8'
      />
    </svg>
  );
};

export default ShareAndEarnSVG;

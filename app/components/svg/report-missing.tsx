import React from 'react';

const ReportMissingCashback = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 18 19'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M14.0262 0C11.7894 0 10 1.56357 10 3.48797C10 4.08935 10.1842 4.69072 10.5263 5.21993C10.5526 5.26804 10.5789 5.34021 10.5789 5.41237L10.1316 6.5189C10.0789 6.66323 10.1842 6.78351 10.3158 6.83162C10.3684 6.85567 10.421 6.85567 10.4737 6.83162L11.6578 6.44674C11.7368 6.42268 11.8157 6.42268 11.8684 6.47079C12.4999 6.80756 13.2367 7 13.9735 7C16.2103 7 17.9997 5.41237 17.9997 3.48797C18.026 1.56357 16.2103 0 14.0262 0ZM13.7367 1.37113H14.5262V3.05498L14.4472 4.16151H13.8157L13.763 3.05498V1.37113H13.7367ZM14.1314 5.53265C13.763 5.53265 13.6578 5.34021 13.6578 5.09966C13.6578 4.85911 13.763 4.66667 14.1314 4.66667C14.4998 4.66667 14.6051 4.85911 14.6051 5.09966C14.6314 5.34021 14.4998 5.53265 14.1314 5.53265Z'
        fill='currentColor'
      />
      <path
        d='M5 4C3.1392 4 1.62642 5.59758 1.62642 7.56268C1.62642 9.49027 3.05398 11.0504 4.91477 11.1179C4.97159 11.1104 5.02841 11.1104 5.07102 11.1179H5.12074C5.99386 11.0871 6.82143 10.6989 7.42847 10.0355C8.03551 9.37202 8.37443 8.48525 8.37358 7.56268C8.37358 5.59758 6.8608 4 5 4ZM8.60795 13.1122C6.62642 11.7171 3.39489 11.7171 1.39915 13.1122C0.497159 13.7497 0 14.6123 0 15.5348C0 16.4574 0.497159 17.3124 1.39205 17.9424C2.38636 18.6475 3.69318 19 5 19C6.30682 19 7.61364 18.6475 8.60795 17.9424C9.50284 17.3049 10 16.4499 10 15.5198C9.9929 14.5973 9.50284 13.7422 8.60795 13.1122Z'
        fill='currentColor'
      />
    </svg>
  );
};

export const ReportMissingHistory = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 17 17'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        clipRule='evenodd'
        d='M17 13.828C17 15.5797 15.5799 16.9997 13.8282 16.9997C12.0765 16.9997 10.6565 15.5797 10.6565 13.828C10.6565 12.0763 12.0765 10.6562 13.8282 10.6562C15.5799 10.6562 17 12.0763 17 13.828ZM12.7289 13.109V14.3777C12.7289 14.7278 13.0131 15.012 13.3632 15.012H14.6319C14.9821 15.012 15.2663 14.7278 15.2663 14.3777C15.2663 14.0275 14.9821 13.7433 14.6319 13.7433H13.9976V13.109C13.9976 12.7588 13.7134 12.4746 13.3632 12.4746C13.0131 12.4746 12.7289 12.7588 12.7289 13.109Z'
        fill='currentColor'
        fillRule='evenodd'
      />
      <path
        d='M13.1099 0C10.9388 0 9.20203 1.41365 9.20203 3.15352C9.20203 3.69723 9.38082 4.24094 9.71285 4.7194C9.7384 4.7629 9.76394 4.82814 9.76394 4.89339L9.32973 5.89382C9.27865 6.02431 9.38082 6.13305 9.50852 6.17654C9.55961 6.19829 9.61069 6.19829 9.66177 6.17654L10.8111 5.82857C10.8878 5.80682 10.9644 5.80682 11.0155 5.85032C11.6285 6.1548 12.3436 6.32878 13.0588 6.32878C15.2298 6.32878 16.9666 4.89339 16.9666 3.15352C16.9921 1.41365 15.2298 0 13.1099 0ZM12.8289 1.23966H13.5951V2.76205L13.5185 3.76247H12.9055L12.8544 2.76205V1.23966H12.8289ZM13.212 5.00213C12.8544 5.00213 12.7523 4.82814 12.7523 4.61066C12.7523 4.39318 12.8544 4.21919 13.212 4.21919C13.5696 4.21919 13.6718 4.39318 13.6718 4.61066C13.6973 4.82814 13.5696 5.00213 13.212 5.00213Z'
        fill='currentColor'
      />
      <path
        d='M4.7014 3.76172C2.95173 3.76172 1.52929 5.16375 1.52929 6.88831C1.52929 8.57995 2.87159 9.94907 4.62126 10.0083C4.67469 10.0017 4.72811 10.0017 4.76818 10.0083H4.81493C5.63591 9.98131 6.41405 9.64067 6.98484 9.05842C7.55563 8.47617 7.87431 7.69795 7.87351 6.88831C7.87351 5.16375 6.45107 3.76172 4.7014 3.76172ZM8.09389 11.7585C6.23069 10.5342 3.19214 10.5342 1.31559 11.7585C0.467469 12.318 0 13.075 0 13.8846C0 14.6942 0.467469 15.4446 1.30891 15.9975C2.24385 16.6163 3.47262 16.9256 4.7014 16.9256C5.93017 16.9256 7.15895 16.6163 8.09389 15.9975C8.93533 15.438 9.4028 14.6877 9.4028 13.8715C9.39612 13.0618 8.93533 12.3115 8.09389 11.7585Z'
        fill='currentColor'
      />
    </svg>
  );
};
export default ReportMissingCashback;

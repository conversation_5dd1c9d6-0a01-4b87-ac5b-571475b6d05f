import React from 'react';

const PaymentSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 13 13'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g clipPath='url(#clip0_0_9660)'>
        <path
          d='M2.12891 8.60182L8.60182 2.12891M6.0132 9.90182L6.6632 9.25182M7.47136 8.4442L8.76595 7.14961'
          stroke='currentColor'
          strokeLinecap='round'
          strokeLinejoin='round'
          strokeMiterlimit='10'
        />
        <path
          d='M1.08315 11.9154H11.9165M1.95036 5.54598L5.54703 1.94932C6.69536 0.800983 7.26953 0.795566 8.40703 1.93307L11.0666 4.59265C12.2041 5.73015 12.1987 6.30432 11.0504 7.45265L7.4537 11.0493C6.30536 12.1976 5.7312 12.2031 4.5937 11.0656L1.93411 8.40598C0.796612 7.26848 0.796612 6.69973 1.95036 5.54598V5.54598Z'
          stroke='currentColor'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_0_9660'>
          <rect fill='currentColor' height='13' width='13' />
        </clipPath>
      </defs>
    </svg>
  );
};

export const PaymentHistory = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 19 14'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        clipRule='evenodd'
        d='M14.8032 2.81965H15.5081V5.6393C13.1741 5.6393 11.2786 7.53481 11.2786 9.86877H2.81965V9.16386C2.81965 7.99652 1.87225 7.04912 0.704912 7.04912H0V2.81965H0.704912C1.87225 2.81965 2.81965 1.87225 2.81965 0.704912V0H12.6884V0.704912C12.6884 1.87225 13.6358 2.81965 14.8032 2.81965ZM0 9.16386V8.45895H0.704912C1.09402 8.45895 1.40982 8.77475 1.40982 9.16386V9.86877H0.704912C0.518111 9.86877 0.338357 9.79476 0.206539 9.66223C0.0740153 9.53041 0 9.35066 0 9.16386ZM4.22947 4.93439C4.22947 2.98883 5.80848 1.40982 7.75404 1.40982C9.69959 1.40982 11.2786 2.98883 11.2786 4.93439C11.2786 6.87994 9.69959 8.45895 7.75404 8.45895C5.80848 8.45895 4.22947 6.87994 4.22947 4.93439ZM9.86877 4.93439C9.86877 3.76705 8.92137 2.81965 7.75404 2.81965C6.5867 2.81965 5.6393 3.76705 5.6393 4.93439C5.6393 6.10172 6.5867 7.04912 7.75404 7.04912C8.92137 7.04912 9.86877 6.10172 9.86877 4.93439ZM14.0982 0.704912V0H14.8032C14.99 0 15.1697 0.0740153 15.3015 0.206539C15.4341 0.338357 15.5081 0.518111 15.5081 0.704912V1.40982H14.8032C14.414 1.40982 14.0982 1.09402 14.0982 0.704912ZM0.704912 0H1.40982V0.704912C1.40982 1.09402 1.09402 1.40982 0.704912 1.40982H0V0.704912C0 0.518111 0.0740153 0.338357 0.206539 0.206539C0.338357 0.0740153 0.518111 0 0.704912 0Z'
        fill='currentColor'
        fillRule='evenodd'
      />
      <path
        clipRule='evenodd'
        d='M18.8911 9.86405C18.8911 11.7327 17.3762 13.2476 15.5075 13.2476C13.6388 13.2476 12.1239 11.7327 12.1239 9.86405C12.1239 7.99535 13.6388 6.48047 15.5075 6.48047C17.3762 6.48047 18.8911 7.99535 18.8911 9.86405ZM14.3346 9.09859V10.452C14.3346 10.8256 14.6378 11.1287 15.0113 11.1287H16.3647C16.7383 11.1287 17.0415 10.8256 17.0415 10.452C17.0415 10.0785 16.7383 9.77531 16.3647 9.77531H15.688V9.09859C15.688 8.72504 15.3849 8.42188 15.0113 8.42188C14.6378 8.42188 14.3346 8.72504 14.3346 9.09859Z'
        fill='currentColor'
        fillRule='evenodd'
      />
    </svg>
  );
};
export default PaymentSVG;

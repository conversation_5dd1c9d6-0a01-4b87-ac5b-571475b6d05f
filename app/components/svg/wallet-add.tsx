import React from 'react';

const WalletAddSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 15 15'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M9.0625 10.1191H5.9375C5.68125 10.1191 5.46875 9.90664 5.46875 9.65039C5.46875 9.39414 5.68125 9.18164 5.9375 9.18164H9.0625C9.31875 9.18164 9.53125 9.39414 9.53125 9.65039C9.53125 9.90664 9.31875 10.1191 9.0625 10.1191Z'
        fill='currentColor'
      />
      <path
        d='M7.5 11.7188C7.24375 11.7188 7.03125 11.5062 7.03125 11.25V8.125C7.03125 7.86875 7.24375 7.65625 7.5 7.65625C7.75625 7.65625 7.96875 7.86875 7.96875 8.125V11.25C7.96875 11.5062 7.75625 11.7188 7.5 11.7188Z'
        fill='currentColor'
      />
      <path
        d='M3.25005 6.53742C3.1313 6.53742 3.01255 6.48742 2.9188 6.39992C2.78755 6.26867 2.7438 6.06242 2.8188 5.88742L3.9813 3.11242C4.0063 3.05617 4.0188 3.01867 4.03755 2.98742C4.96255 0.856173 6.1438 0.337423 8.2313 1.13742C8.35005 1.18117 8.4438 1.27492 8.4938 1.39367C8.5438 1.51242 8.5438 1.64367 8.4938 1.76242L6.66255 6.01242C6.58755 6.18117 6.4188 6.29367 6.2313 6.29367H4.45005C4.0938 6.29367 3.7563 6.36242 3.4313 6.49992C3.37505 6.52492 3.31255 6.53742 3.25005 6.53742ZM6.6313 1.71867C5.8563 1.71867 5.3813 2.22492 4.88755 3.37492C4.8813 3.39367 4.8688 3.41242 4.86255 3.43117L4.0438 5.37492C4.1813 5.36242 4.31255 5.35617 4.45005 5.35617H5.9188L7.42505 1.85617C7.1313 1.76242 6.8688 1.71867 6.6313 1.71867Z'
        fill='currentColor'
      />
      <path
        d='M11.4311 6.41834C11.3874 6.41834 11.3374 6.41209 11.2936 6.39959C11.0624 6.33084 10.8061 6.29334 10.5436 6.29334H6.23115C6.0749 6.29334 5.9249 6.21209 5.8374 6.08084C5.75615 5.94959 5.7374 5.78084 5.7999 5.63709L7.6124 1.43084C7.70615 1.20584 7.98115 1.05584 8.2124 1.13084C8.2874 1.15584 8.35615 1.18709 8.43115 1.21834L9.90615 1.83709C10.7686 2.19334 11.3436 2.56834 11.7186 3.01834C11.7936 3.10584 11.8561 3.19959 11.9124 3.29334C11.9811 3.39959 12.0436 3.53084 12.0874 3.66209C12.1061 3.70584 12.1374 3.78709 12.1561 3.87459C12.3311 4.46209 12.2436 5.19334 11.8686 6.13084C11.7936 6.30584 11.6186 6.41834 11.4311 6.41834ZM6.94365 5.35584H10.5499C10.7499 5.35584 10.9436 5.37459 11.1374 5.40584C11.3124 4.86209 11.3499 4.44334 11.2499 4.10584C11.2374 4.04959 11.2249 4.02459 11.2186 3.99959C11.1811 3.89959 11.1561 3.84334 11.1249 3.79334C11.0811 3.72459 11.0499 3.66834 10.9999 3.61209C10.7311 3.28709 10.2561 2.98709 9.5499 2.69959L8.3124 2.18084L6.94365 5.35584Z'
        fill='currentColor'
      />
      <path
        d='M9.9375 14.2189H5.0625C4.8875 14.2189 4.725 14.2064 4.5625 14.1877C2.36875 14.0377 1.11875 12.7877 0.96875 10.5689C0.95 10.4314 0.9375 10.2627 0.9375 10.0939V8.8752C0.9375 7.46895 1.775 6.2002 3.06875 5.6377C3.50625 5.4502 3.975 5.35645 4.45625 5.35645H10.5562C10.9125 5.35645 11.2563 5.40645 11.575 5.50645C13.0437 5.9502 14.075 7.3377 14.075 8.8752V10.0939C14.075 10.2314 14.0688 10.3627 14.0625 10.4877C13.925 12.9314 12.5 14.2189 9.9375 14.2189ZM4.45 6.29395C4.09375 6.29395 3.75625 6.3627 3.43125 6.5002C2.48125 6.9127 1.86875 7.84395 1.86875 8.8752V10.0939C1.86875 10.2252 1.88125 10.3564 1.89375 10.4814C2.0125 12.2627 2.8875 13.1377 4.64375 13.2564C4.8 13.2752 4.925 13.2877 5.05625 13.2877H9.93125C11.9938 13.2877 13.0062 12.3814 13.1062 10.4439C13.1125 10.3314 13.1187 10.2189 13.1187 10.0939V8.8752C13.1187 7.74395 12.3625 6.73145 11.2875 6.4002C11.0562 6.33145 10.8 6.29395 10.5375 6.29395H4.45Z'
        fill='currentColor'
      />
      <path
        d='M1.3999 9.34387C1.14365 9.34387 0.931152 9.13137 0.931152 8.87512V7.04387C0.931152 5.07512 2.3249 3.37512 4.2499 3.00012C4.41865 2.96887 4.59365 3.03137 4.70615 3.16262C4.8124 3.29387 4.84365 3.48137 4.7749 3.63762L3.68115 6.25012C3.63115 6.36262 3.54365 6.45012 3.4374 6.50012C2.4874 6.91262 1.8749 7.84387 1.8749 8.87512C1.86865 9.13137 1.6624 9.34387 1.3999 9.34387ZM3.4999 4.26262C2.6999 4.71262 2.11865 5.50012 1.9374 6.41887C2.2124 6.13762 2.53115 5.90012 2.89365 5.72512L3.4999 4.26262Z'
        fill='currentColor'
      />
      <path
        d='M13.6002 9.34361C13.344 9.34361 13.1315 9.13111 13.1315 8.87486C13.1315 7.74361 12.3752 6.73111 11.3002 6.39986C11.1752 6.36236 11.069 6.27486 11.0127 6.15611C10.9565 6.03736 10.9502 5.89986 11.0002 5.78111C11.294 5.04986 11.369 4.51861 11.2502 4.10611C11.2377 4.04986 11.2252 4.02486 11.219 3.99986C11.1377 3.81861 11.1815 3.60611 11.3252 3.46861C11.469 3.33111 11.6877 3.29986 11.8627 3.39361C13.2252 4.10611 14.069 5.50611 14.069 7.04361V8.87486C14.069 9.13111 13.8565 9.34361 13.6002 9.34361ZM12.0315 5.68111C12.4252 5.86236 12.7752 6.11861 13.069 6.42486C12.9502 5.81236 12.6565 5.25611 12.2252 4.81861C12.194 5.08111 12.1315 5.36861 12.0315 5.68111Z'
        fill='currentColor'
      />
    </svg>
  );
};

export default WalletAddSVG;

import React from 'react';

const UploadSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      viewBox='0 0 18 18'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <g clipPath='url(#clip0_0_10349)'>
        <path
          d='M6.75 12.75V8.25L5.25 9.75M6.75 8.25L8.25 9.75'
          stroke='currentColor'
          strokeLinecap='round'
          strokeLinejoin='round'
          strokeWidth='1.5'
        />
        <path
          d='M16.5 7.5V11.25C16.5 15 15 16.5 11.25 16.5H6.75C3 16.5 1.5 15 1.5 11.25V6.75C1.5 3 3 1.5 6.75 1.5H10.5'
          stroke='currentColor'
          strokeLinecap='round'
          strokeLinejoin='round'
          strokeWidth='1.5'
        />
        <path
          d='M16.5 7.5H13.5C11.25 7.5 10.5 6.75 10.5 4.5V1.5L16.5 7.5Z'
          stroke='currentColor'
          strokeLinecap='round'
          strokeLinejoin='round'
          strokeWidth='1.5'
        />
      </g>
      <defs>
        <clipPath id='clip0_0_10349'>
          <rect fill='currentColor' height='18' width='18' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default UploadSVG;

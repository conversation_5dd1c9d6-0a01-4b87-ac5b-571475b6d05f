import React from 'react';

const StoreSVG = ({ ...props }) => {
  return (
    <svg
      fill='none'
      height='16'
      viewBox='0 0 16 16'
      width='16'
      xmlns='http://www.w3.org/2000/svg'
      {...props}
    >
      <path
        d='M1.48242 7.45508V10.5981C1.48242 13.741 2.74241 15.001 5.88539 15.001H9.65836C12.8013 15.001 14.0613 13.741 14.0613 10.5981V7.45508'
        stroke='black'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M7.77337 7.99995C9.05436 7.99995 9.99935 6.95696 9.87335 5.67597L9.41135 1H6.14238L5.67338 5.67597C5.54738 6.95696 6.49238 7.99995 7.77337 7.99995Z'
        stroke='black'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M12.1916 7.99995C13.6056 7.99995 14.6416 6.85196 14.5016 5.44497L14.3056 3.51998C14.0536 1.69999 13.3536 1 11.5196 1H9.38461L9.87461 5.90696C9.99361 7.06196 11.0366 7.99995 12.1916 7.99995ZM3.32266 7.99995C4.47765 7.99995 5.52064 7.06196 5.63264 5.90696L5.78664 4.35998L6.12264 1H3.98765C2.15367 1 1.45367 1.69999 1.20167 3.51998L1.01267 5.44497C0.872675 6.85196 1.90867 7.99995 3.32266 7.99995ZM7.77463 11.4999C6.60563 11.4999 6.02464 12.0809 6.02464 13.2499V14.9999H9.52461V13.2499C9.52461 12.0809 8.94362 11.4999 7.77463 11.4999Z'
        stroke='black'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  );
};

export default StoreSVG;

import Image from 'next/image';
import React from 'react';
import { motion } from 'framer-motion';
import PendingSVG from '../svg/pending';
import ApprovedSVG from '../svg/approved';
import DateIcon from '../svg/date-icon';
import WalletAddSVG from '../svg/wallet-add';
import type { UserData } from '@/services/api/data-contracts';
import { formatIndRs } from '@/utils/helpers';
import dayjs from 'dayjs';

const ReferralHistoryCard = ({ item }: { item: UserData }) => {
  return (
    <motion.div
      animate={{ opacity: 1 }}
      className='flex flex-col gap-x-[6px] lg:gap-x-[9px] p-[6px] lg:py-[10px] lg:px-[10px] bg-container dark:bg-[#3E424C] rounded-[5px] w-full text-blackWhite'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* ----------items----------  */}
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='rounded-[2.5px] bg-white dark:bg-[#31343D] px-[10px] py-[10px] flex lg:flex items-center lg:grow'
        initial={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.3, delay: 0.1 }}
      >
        <div className='flex items-center gap-x-[6px] grow'>
          <motion.div
            animate={{ opacity: 1, scale: 1 }}
            initial={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <Image
              alt=''
              className='w-[30px] h-[30px] lg:w-[53px] lg:h-[53px] rounded-full overflow-hidden shrink-0'
              height={53}
              src={
                item.avatar ||
                `https://ui-avatars.com/api/?name=${item.name}&background=random&rounded=true&format=png`
              }
              width={53}
            />
          </motion.div>
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className='max-w-[80px] lg:max-w-none lg:ml-[10px] lg:flex lg:items-center lg:gap-x-[10px]'
            initial={{ opacity: 0, x: -10 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          >
            <motion.h4
              animate={{ opacity: 1 }}
              className='text-[8px] lg:text-xs font-semibold truncate'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
            >
              {item.name}
            </motion.h4>
            <motion.div
              animate={{ opacity: 1, scale: 1 }}
              className='w-fit text-[6px] lg:text-[10px] font-semibold mt-[4px] lg:mt-0 rounded-[2.5px] text-white bg-[#69C8B4] px-[9px] py-[3px] lg:px-[15px] lg:py-[5px]'
              initial={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              {item.status}
            </motion.div>
          </motion.div>
        </div>
        <motion.div
          animate={{ opacity: 1, x: 0 }}
          className='flex flex-col justify-start h-full ml-[10px]'
          initial={{ opacity: 0, x: 10 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <motion.div
            animate={{ opacity: 1 }}
            className='font-light lg:hidden flex'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
          >
            <span className='text-[9px]'>Joined On :</span>
            <motion.span
              animate={{ opacity: 1 }}
              className='ml-[4px] text-[10px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.3, delay: 0.6 }}
            >
              {dayjs(item.joined).format('YYYY MMM DD')}
            </motion.span>
          </motion.div>
          <motion.div
            animate={{ opacity: 1 }}
            className='font-light leading-normal flex lg:hidden'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.7 }}
          >
            <span className='text-[9px] font-light'>Total Orders :</span>
            <motion.span
              animate={{ opacity: 1 }}
              className='text-[10px] font-black ml-[4px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.3, delay: 0.8 }}
            >
              {item.totalOrders}
            </motion.span>
          </motion.div>
        </motion.div>
      </motion.div>
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='flex mt-[10px] gap-x-[10px]'
        initial={{ opacity: 0, y: 10 }}
        transition={{ duration: 0.3, delay: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, scale: 1 }}
          className='w-full rounded-[2.5px] bg-white dark:bg-[#31343D] px-[10px] py-[6px] lg:py-[10px] hidden lg:flex-center flex-col lg:grow'
          initial={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.3, delay: 0.6 }}
        >
          <motion.div
            animate={{ scale: 1 }}
            initial={{ scale: 0 }}
            transition={{ duration: 0.3, delay: 0.7 }}
          >
            <WalletAddSVG className='text-blackWhite w-[12px] lg:w-[18px]' />
          </motion.div>
          <motion.span
            animate={{ opacity: 1 }}
            className='text-[7px] lg:text-[10px] font-light mt-[5px] lg:mt-[10px] text-center'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.8 }}
          >
            Total Orders
          </motion.span>
          <motion.span
            animate={{ opacity: 1 }}
            className='text-[8px] lg:text-xs font-black font-nexa mt-[7px] lg:mt-[10px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.9 }}
          >
            {item.totalOrders}
          </motion.span>
        </motion.div>
        <motion.div
          animate={{ opacity: 1, scale: 1 }}
          className='w-full rounded-[2.5px] bg-white dark:bg-[#31343D] px-[10px] py-[6px] lg:py-[10px] hidden lg:flex-center flex-col lg:grow'
          initial={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.3, delay: 0.7 }}
        >
          <motion.div
            animate={{ scale: 1 }}
            initial={{ scale: 0 }}
            transition={{ duration: 0.3, delay: 0.8 }}
          >
            <DateIcon className='text-blackWhite w-[12px] lg:w-[18px]' />
          </motion.div>
          <motion.span
            animate={{ opacity: 1 }}
            className='text-[10px] lg:text-[10px] font-light mt-[5px] lg:mt-[10px] text-center'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.9 }}
          >
            Joined On
          </motion.span>
          <motion.span
            animate={{ opacity: 1 }}
            className='text-[10px] lg:text-xs font-black font-nexa mt-[7px] lg:mt-[10px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 1.0 }}
          >
            {dayjs(item.joined).format('YYYY MMM DD')}
          </motion.span>
        </motion.div>
        <motion.div
          animate={{ opacity: 1, scale: 1 }}
          className='w-full rounded-[2.5px] bg-white dark:bg-[#31343D] px-[10px] py-[6px] lg:py-[10px] flex-center flex-col lg:grow'
          initial={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.3, delay: 0.8 }}
        >
          <motion.div
            animate={{ scale: 1 }}
            initial={{ scale: 0 }}
            transition={{ duration: 0.3, delay: 0.9 }}
          >
            <PendingSVG className='text-blackWhite w-[12px] lg:w-[18px]' />
          </motion.div>
          <motion.span
            animate={{ opacity: 1 }}
            className='text-[9px] lg:text-[10px] font-light mt-[5px] lg:mt-[10px] text-center'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 1.0 }}
          >
            Pending Commission
          </motion.span>
          <motion.span
            animate={{ opacity: 1 }}
            className='text-[10px] lg:text-xs font-black font-nexa mt-[7px] lg:mt-[10px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 1.1 }}
          >
            {formatIndRs(item.totalPendingReferralCommission)}
          </motion.span>
        </motion.div>
        <motion.div
          animate={{ opacity: 1, scale: 1 }}
          className='w-full rounded-[2.5px] bg-white dark:bg-[#31343D] px-[10px] py-[6px] lg:py-[10px] flex-center flex-col lg:grow'
          initial={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.3, delay: 0.9 }}
        >
          <motion.div
            animate={{ scale: 1 }}
            initial={{ scale: 0 }}
            transition={{ duration: 0.3, delay: 1.0 }}
          >
            <ApprovedSVG className='text-blackWhite w-[12px] lg:w-[18px]' />
          </motion.div>
          <motion.span
            animate={{ opacity: 1 }}
            className='text-[9px] lg:text-[10px] font-light mt-[5px] lg:mt-[10px] text-center'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 1.1 }}
          >
            Confirmed Commission
          </motion.span>
          <motion.span
            animate={{ opacity: 1 }}
            className='text-[10px] lg:text-xs font-black font-nexa mt-[7px] lg:mt-[10px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 1.2 }}
          >
            {formatIndRs(item.totalConfirmedReferralCommission)}
          </motion.span>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default ReferralHistoryCard;

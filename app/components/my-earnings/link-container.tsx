import React from 'react';
import RightArrow from '../svg/right-arrow';
import { motion } from 'framer-motion';

const LinkContainerWithoutCaption = ({
  title,
  icon,
  onClick,
}: {
  title: string;
  icon: React.ReactNode;
  onClick?: () => void;
}) => {
  return (
    <motion.div
      className='w-full cursor-pointer max-w-[391px] h-[50px] lg:h-[56px] rounded-[5px] border-[1px] border-primary dark:border-none pl-[15px] pr-[18px] flex items-center justify-between bg-white dark:bg-container'
      onClick={onClick}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.03, boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
      whileTap={{ scale: 0.97 }}
    >
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        className='flex items-center gap-x-[18px]'
        initial={{ opacity: 0, x: -10 }}
        transition={{ duration: 0.3 }}
      >
        {icon}
        <p className='truncate text-[10px] lg:text-xs lg:font-medium text-blackWhite max-w-[80%]'>
          {title}
        </p>
      </motion.div>
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        className='relative'
        initial={{ opacity: 0, x: 10 }}
        transition={{ duration: 0.3, delay: 0.1 }}
        whileHover={{ x: 8, scale: 1.2 }}
      >
        <div className='absolute -inset-2 bg-primary/10 rounded-full animate-pulse' />
        <RightArrow className='text-primary dark:text-white w-[14px] lg:w-[20px] relative z-10' />
      </motion.div>
    </motion.div>
  );
};

export const LinkContainerMyEarnings = ({
  title,
  icon,
  amount,
  onClick,
}: {
  title: string;
  icon: React.ReactNode;
  amount: string;
  onClick?: () => void;
}) => {
  return (
    <motion.div
      className='w-full max-w-[391px] h-[50px] lg:h-[56px] rounded-[4px] pr-[18px] lg:pr-[24px] flex items-center justify-between bg-white dark:bg-[#3e424c] overflow-hidden cursor-pointer'
      onClick={onClick}
      style={{ boxShadow: '0px 2px 4px 0px rgba(0, 0, 0, 0.08)' }}
      transition={{ duration: 0.3 }}
      whileHover={{ scale: 1.03, boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
      whileTap={{ scale: 0.97 }}
    >
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        className='flex h-full items-center gap-x-[9px] lg:gap-x-[14px] mr-[10px]'
        initial={{ opacity: 0, x: -10 }}
        transition={{ duration: 0.3 }}
      >
        <motion.div
          animate={{ opacity: 1, scale: 1 }}
          className='w-[39px] lg:w-[59px] h-full shrink-0 grow-0 flex-center bg-[#F9F9F9] dark:bg-[#31343D]'
          initial={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          {icon}
        </motion.div>
        <motion.p
          animate={{ opacity: 1 }}
          className='text-[10px] lg:font-medium text-blackWhite maxLines2'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          {title}
        </motion.p>
      </motion.div>
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        className='flex items-center'
        initial={{ opacity: 0, x: 10 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      >
        <motion.span
          animate={{ opacity: 1 }}
          className='text-[11px] lg:text-sm font-black font-nexa mr-[12px] whitespace-nowrap'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          {amount}
        </motion.span>
        <motion.div
          animate={{ opacity: 1 }}
          className='relative'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
          whileHover={{ x: 8, scale: 1.2 }}
        >
          <div className='absolute -inset-2 bg-primary/10 rounded-full animate-pulse' />
          <RightArrow className='text-primary dark:text-white w-[14px] lg:w-[20px] relative z-10' />
        </motion.div>
      </motion.div>
    </motion.div>
  );
};
export default LinkContainerWithoutCaption;

import { PaymentType } from '@/services/api/data-contracts';
import { formatIndRs } from '@/utils/helpers';
import clsx from 'clsx';
import dayjs from 'dayjs';
import React from 'react';
import { motion } from 'framer-motion';

const PaymentHistoryCont = ({ data }: { data: PaymentType }) => {
  return (
    <motion.div
      animate={{ opacity: 1 }}
      className='rounded-[5px] overflow-hidden flex w-full shadow-sm'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <motion.div className='flex flex-col w-full'>
        <motion.div
          animate={{ opacity: 1 }}
          className='flex w-full items-center justify-evenly bg-white dark:bg-[#3E424C] h-[60px] lg:h-[70px] shrink-0'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='flex-center flex-col text-[10px] lg:text-xs'
            initial={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.3, delay: 0.2 }}
          >
            <span className='text-[#969696] dark:text-[#ccc9c9df]'>
              Ref No:
            </span>
            <span className='text-blackWhite mt-[3px]'>{data.referenceId}</span>
          </motion.div>
          <motion.div
            animate={{ opacity: 1, scaleY: 1 }}
            className='w-[1px] h-[23px] bg-[#E1E1E1] shrink-0 grow-0 basis-[1px]'
            initial={{ opacity: 0, scaleY: 0 }}
            transition={{ duration: 0.3, delay: 0.3 }}
          />
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='flex-center flex-col text-[10px] lg:text-xs'
            initial={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            <span className='text-[#969696] dark:text-[#ccc9c9df]'>
              Amount:
            </span>
            <span className='text-blackWhite mt-[3px] font-bold'>
              {formatIndRs(data?.withdrawAmount || 0)}
            </span>
          </motion.div>
          <motion.div
            animate={{ opacity: 1, scaleY: 1 }}
            className='w-[1px] h-[23px] bg-[#E1E1E1] shrink-0 grow-0 basis-[1px]'
            initial={{ opacity: 0, scaleY: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
          />
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='flex-center flex-col text-[10px] lg:text-xs'
            initial={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.3, delay: 0.6 }}
          >
            <span className='text-[#969696] dark:text-[#ccc9c9df]'>
              Request Type:
            </span>
            <span className='text-blackWhite mt-[3px]'>{data.paymentType}</span>
          </motion.div>
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='bg-container dark:bg-[#31343D] h-[40px] lg:h-[48px] flex items-center justify-between px-[20px]'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.3, delay: 0.7 }}
        >
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className='flex items-center gap-x-[10px] w-full lg:justify-center'
            initial={{ opacity: 0, x: -10 }}
            transition={{ duration: 0.3, delay: 0.8 }}
          >
            <motion.div
              animate={{ opacity: 1 }}
              className='text-[10px] lg:text-xs'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.3, delay: 0.9 }}
            >
              <span className='text-[#969696] dark:text-[#ccc9c9df]'>
                Date:
              </span>
              <span className='text-blackWhite ml-[2px] lg:ml-[5px]'>
                {dayjs(data.paymentDate).format('DD MMMM YYYY')}
              </span>
            </motion.div>
            <motion.div
              animate={{ opacity: 1 }}
              className='text-[10px] lg:text-xs'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.3, delay: 1.0 }}
            >
              <span className='text-[#969696] dark:text-[#ccc9c9df]'>
                Time:
              </span>
              <span className='text-blackWhite ml-[2px] lg:ml-[5px]'>
                {dayjs(data.paymentDate).format('h:mm A')}
              </span>
            </motion.div>
          </motion.div>
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className='lg:hidden flex-center'
            initial={{ opacity: 0, x: 10 }}
            transition={{ duration: 0.3, delay: 1.1 }}
          >
            <span
              className={clsx(
                data.status === 'Requested' && '!text-[#FFC554]',
                'text-[10px] text-[#407BFF] font-semibold'
              )}
            >
              {data.status}
            </span>
          </motion.div>
        </motion.div>
      </motion.div>
      <motion.div
        animate={{ opacity: 1, x: 0 }}
        className='w-[165px] hidden lg:flex-center bg-[#f1f1f1] dark:bg-[#3e424c] border-l-[1px] border-gray-300'
        initial={{ opacity: 0, x: 20 }}
        transition={{ duration: 0.3, delay: 1.2 }}
      >
        <span
          className={clsx(
            data.status === 'Requested' && '!text-[#FFC554]',
            'text-[10px] lg:text-sm text-[#407BFF] font-semibold'
          )}
        >
          {data.status}
        </span>
      </motion.div>
    </motion.div>
  );
};

export default PaymentHistoryCont;

import { MissingCashbackType } from '@/services/api/data-contracts';
import {
  formatIndRs,
  formatDateDisplay,
  formatTimeDisplay,
} from '@/utils/helpers';
import Image from 'next/image';
import React from 'react';

const ReportMissingCbHistory = ({ data }: { data: MissingCashbackType }) => {
  return (
    <div className='rounded-[5px] flex flex-col overflow-hidden shadow-md'>
      <div className='bg-container h-[45px] lg:h-[50px] flex items-center justify-evenly lg:justify-start overflow-hidden border-b-[0.5px] border-[#e2e2e227] gap-x-[10px] px-1 sm:px-2 md:px-4 lg:px-8 lg:pl-[40px]'>
        <div className='w-[46px] h-[40px] lg:w-[105px] lg:h-[50px] relative shrink-0'>
          <Image alt='' className='object-contain' fill src={data.storeLogo} />
        </div>
        <div className='text-[10px] lg:text-xs text-blackWhite min-w-0 flex lg:ml-[35px]'>
          <span className='shrink-0'>Item:</span>
          <p className='font-medium ml-[4px] lg:ml-[10px] truncate'>
            {data.title}
          </p>
        </div>
      </div>
      <div className='bg-white dark:bg-[#3E424C] h-[57px] flex items-center justify-between text-blackWhite border-b-[0.5px] border-[#b3b2b24e] dark:border-[#e2e2e227] px-2 md:px-4 lg:px-8'>
        <div className='flex flex-col'>
          <span className='text-[10px] lg:text-[10px] font-light'>Amount</span>
          <span className='text-[10px] lg:text-xs font-bold mt-[5px]'>
            {formatIndRs(data.amount)}
          </span>
        </div>
        <div className='flex flex-col'>
          <span className='text-[10px] lg:text-[10px] font-light'>
            Ref. No :
          </span>
          <span className='text-[10px] lg:text-xs font-bold mt-[5px]'>
            {data.referenceId}
          </span>
        </div>
        <div className='flex flex-col'>
          <span className='text-[10px] lg:text-[10px] font-light'>
            Transaction Date
          </span>
          <span className='text-[10px] lg:text-xs font-bold mt-[5px]'>
            {formatDateDisplay(data.createdAt)}
          </span>
        </div>
      </div>
      <div className='h-[30px] lg:h-[40px] bg-white dark:bg-[#3E424C] flex items-center justify-between px-2 md:px-4 lg:px-8'>
        <div className='text-[#828282] text-[10px] lg:text-[10px]'>
          <span>{formatDateDisplay(data.createdAt)}</span>
          <span className='ml-[10px]'>
            {formatTimeDisplay(data.clickedTime)}
          </span>
        </div>
        <span className='text-[#407BFF] text-[9px] lg:text-xs font-semibold'>
          {data.status}
        </span>
      </div>
    </div>
  );
};

export default ReportMissingCbHistory;

import React from 'react';
import Image from 'next/image';
import clsx from 'clsx';
import { motion } from 'framer-motion';
import SmartLink from './common/smart-link';
import { LinkType } from '@/utils/link-utils';

const NoData = ({
  imgClass,
  customHeight,
  message,
  showHomeLink = true,
}: {
  imgClass?: string;
  customHeight?: string;
  message?: string;
  showHomeLink?: boolean;
}) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: 'beforeChildren',
        staggerChildren: 0.2,
        duration: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: 'spring', stiffness: 300, damping: 24 },
    },
  };

  return (
    <>
      <motion.section
        animate='visible'
        className={clsx(
          'bg-container w-full flex-center m-auto',
          customHeight ||
            'min-h-[calc(100vh-65px)] lg:min-h-[calc(100vh-104px)]'
        )}
        initial='hidden'
        variants={containerVariants}
      >
        <motion.div className='flex flex-col items-center'>
          <motion.div
            animate={{
              y: [0, -10, 0],
              transition: {
                y: {
                  repeat: Infinity,
                  duration: 3,
                  ease: 'easeInOut',
                },
              },
            }}
            className={clsx(imgClass, 'relative w-56 h-56')}
            variants={itemVariants}
            whileHover={{ scale: 1.05 }}
          >
            <Image
              alt='No data image'
              className='object-contain w-full h-full object-scale-down'
              fill
              priority
              src={`/svg/filter-no-data.svg`}
            />
          </motion.div>
          <motion.div
            className='text-[14px] md:text-[16px] mb-4 font-semibold text-blackWhite text-center px-4'
            variants={itemVariants}
          >
            {message || 'Sorry, No Result Found!'}
          </motion.div>
          {showHomeLink && (
            <motion.div variants={itemVariants}>
              <SmartLink
                className='text-xs px-6 py-2.5 font-semibold bg-primary text-white hover:scale-110 hover:bg-primaryDark active:scale-95 transition-all duration-300 rounded-md shadow-md flex items-center justify-center'
                href={'/'}
                linkType={LinkType.INTERNAL}
              >
                Take me to Home
              </SmartLink>
            </motion.div>
          )}
        </motion.div>
      </motion.section>
    </>
  );
};

export default NoData;

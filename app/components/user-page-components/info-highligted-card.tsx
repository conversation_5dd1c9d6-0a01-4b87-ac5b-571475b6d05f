import clsx from 'clsx';
import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const InfoHighligtedCard = ({
  icon,
  number,
  caption,
  stripColorClass,
  onClick,
  tooltip,
}: {
  icon: React.ReactNode;
  number: number;
  caption: string;
  stripColorClass: string;
  onClick?: () => void;
  tooltip?: string;
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState<'top' | 'bottom'>(
    'bottom'
  );
  const cardRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (showTooltip && cardRef.current && tooltipRef.current) {
      const cardRect = cardRef.current.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;

      // Check if tooltip would overflow at the bottom
      const spaceBelow = viewportHeight - cardRect.bottom;
      const spaceAbove = cardRect.top;

      // If not enough space below and more space above, show tooltip on top
      if (
        spaceBelow < tooltipRect.height + 10 &&
        spaceAbove > tooltipRect.height + 10
      ) {
        setTooltipPosition('top');
      } else {
        setTooltipPosition('bottom');
      }
    }
  }, [showTooltip]);

  const handleClick = () => {
    if (tooltip) {
      // For mobile: toggle tooltip on click
      setShowTooltip(!showTooltip);
    }
    if (onClick) {
      onClick();
    }
  };

  const handleMouseEnter = () => {
    if (tooltip && window.innerWidth >= 1024) {
      // Only on desktop (lg breakpoint)
      setShowTooltip(true);
    }
  };

  const handleMouseLeave = () => {
    if (tooltip && window.innerWidth >= 1024) {
      // Only on desktop (lg breakpoint)
      setShowTooltip(false);
    }
  };

  // Close tooltip when clicking outside (for mobile)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        cardRef.current &&
        !cardRef.current.contains(event.target as Node) &&
        window.innerWidth < 1024 // Only on mobile
      ) {
        setShowTooltip(false);
      }
    };

    if (showTooltip) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [showTooltip]);

  return (
    <div className='relative' ref={cardRef}>
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='w-[71px] z-10 lg:w-[84px] h-[72px] bg-white dark:bg-[#3E424C] shrink-0 rounded-[5px] relative before:w-[63px] before:bottom-[-4px] before:left-[4px] before:h-[72px] before:rounded-b-[5px] before:opacity-50 before:bg-white dark:before:bg-[#3E424C] before:absolute before:z-[-1] lg:before:content-none cursor-pointer'
        initial={{ opacity: 0, y: 20 }}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        transition={{ duration: 0.3 }}
        whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
        whileTap={{ scale: 0.95 }}
      >
        <motion.div
          animate={{ scaleY: 1 }}
          className={clsx(
            stripColorClass,
            'h-[22px] w-[4px] rounded-r-[10px] absolute top-[8px] left-0'
          )}
          initial={{ scaleY: 0 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        />
        <motion.div
          animate={{ opacity: 1 }}
          className='w-full h-full flex-col flex-center relative z-[1]'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.4, delay: 0.3 }}
        >
          <motion.div
            animate={{ scale: 1 }}
            initial={{ scale: 0 }}
            transition={{ duration: 0.3, delay: 0.4 }}
          >
            {icon}
          </motion.div>
          <motion.p
            animate={{ opacity: 1 }}
            className='mt-[8px] text-xs font-bold text-black dark:text-white'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
          >
            {number}
          </motion.p>
          <motion.p
            animate={{ opacity: 1 }}
            className='text-[8px] lg:text-[10px] text-black dark:text-white'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.6 }}
          >
            {caption}
          </motion.p>
        </motion.div>
      </motion.div>

      {/* Tooltip */}
      <AnimatePresence>
        {tooltip && showTooltip && (
          <motion.div
            animate={{ opacity: 1, scale: 1 }}
            className={clsx(
              'absolute left-1/2 transform -translate-x-1/2 px-3 py-2 bg-primary text-white text-xs rounded-lg shadow-lg z-[9999] pointer-events-none',
              'w-[200px] lg:w-[250px] text-center',
              tooltipPosition === 'bottom'
                ? 'top-full mt-2'
                : 'bottom-full mb-2'
            )}
            exit={{ opacity: 0, scale: 0.95 }}
            initial={{ opacity: 0, scale: 0.95 }}
            ref={tooltipRef}
            style={{
              // Ensure tooltip doesn't overflow horizontally
              left: '-100%',
              transform: 'translateX(-50%)',
            }}
            transition={{ duration: 0.15 }}
          >
            {tooltip}
            {/* Arrow */}
            <div
              className={clsx(
                'absolute left-1/2 transform -translate-x-1/2 w-0 h-0',
                tooltipPosition === 'bottom'
                  ? 'bottom-full border-l-[5px] border-r-[5px] border-b-[5px] border-l-transparent border-r-transparent border-b-primary'
                  : 'top-full border-l-[5px] border-r-[5px] border-t-[5px] border-l-transparent border-r-transparent border-t-primary'
              )}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default InfoHighligtedCard;

'use client';

import Image from 'next/image';
import React, { useRef } from 'react';
import Pattern1SVG from '../../svg/patterns/pattern1';
import QuickAccessCard from './quick-access-card';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';
import { Pagination } from 'swiper/modules';
import Edit from '../../svg/edit';
import CommonContainer from '../../common-container';
import Pattern6 from '../../svg/patterns/pattern6';
import { LeftRoundButton, RightRoundButton } from '../../atoms/rounded-buttons';
import { chunkArray, sideScroll } from '@/utils/helpers';
import type { QuickAccessResponseItem } from '@/services/api/data-contracts';
import type { PromiseStatus } from '@/types/global-types';
import { useOverflowing } from '@/utils/custom-hooks';

const colors = [
  { firstColor: 'bg-[#6F62D6]', secondColor: 'bg-[#6557CE]' },
  { firstColor: 'bg-[#FEA133]', secondColor: 'bg-[#E8932F]' },
  { firstColor: 'bg-[#77B2B2]', secondColor: 'bg-[#6CADAD]' },
  { firstColor: 'bg-[#A069CC]', secondColor: 'bg-[#8F5BBB]' },
];
type ModifiedQuickAccessResponseItem = QuickAccessResponseItem & {
  firstColor: string;
  secondColor: string;
};

const QuickAccess = ({
  quickAccessData,
  promiseStatus,
}: {
  quickAccessData: Array<QuickAccessResponseItem>;
  promiseStatus: PromiseStatus;
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null);

  const modifiedQuickAccessData: Array<ModifiedQuickAccessResponseItem> =
    quickAccessData.map((item, index) => ({
      ...item,
      ...colors[index % colors.length],
    }));

  const isOverflowing: boolean = useOverflowing(
    containerRef,
    modifiedQuickAccessData
  );

  if (promiseStatus === 'rejected' || !quickAccessData?.length) {
    return;
  }

  return (
    <CommonContainer className='quickAccessSection lg:flex lg:rounded-none'>
      <div className='flex justify-between pt-[15px] lg:pt-0 shrink-0 relative bg-transparent lg:bg-[#EAEAEA] lg:dark:bg-[#1f2022] lg:w-[162px] lg:justify-center lg:items-center'>
        <Pattern1SVG className='text-[#E2E2E2] dark:text-[#3B3D45] pl-[13px] shrink-0 lg:hidden' />
        <Pattern6 className='text-[#D7D7D7] dark:text-[#3B3D45] shrink-0 hidden lg:block absolute top-[15px] left-[13px]' />

        <h3 className='text-sm md:text-lg lg:text-sm text-heading font-medium font-pat lg:w-min'>
          Quick Access
        </h3>
        <button className='pr-[16px] lg:hidden invisible ' type='button'>
          <Edit className='w-[16px] text-heading' />
        </button>
      </div>

      <div className='px-[12px] lg:px-0 mt-[18px] lg:overflow-hidden lg:grow'>
        <Swiper
          className='quickAccessSwiper !pb-[30px] md:!hidden'
          grabCursor={true}
          modules={[Pagination]}
          pagination={{ clickable: true }}
          slidesPerView={1}
        >
          {chunkArray(modifiedQuickAccessData, 4).map((chunk) => (
            <SwiperSlide key={chunk[0].title}>
              <div className='grid grid-cols-4 place-items-center	'>
                {chunk.map((item) => (
                  <QuickAccessCard
                    firstColor={item.firstColor}
                    key={item.title}
                    redirectUrl={item.redirectUrl}
                    secondColor={item.secondColor}
                    title={item.title}
                  >
                    <Image
                      alt='price tracker icon'
                      height={28}
                      src={item.imageUrl || ''}
                      width={28}
                    />
                  </QuickAccessCard>
                ))}
              </div>
            </SwiperSlide>
          ))}
          {/* <SwiperSlide>
            <div className='grid grid-cols-4 place-items-center md:hidden'>
              {modifiedQuickAccessData.slice(5, 9).map((item, index) => (
                <QuickAccessCard
                  firstColor={item.firstColor}
                  key={index}
                  redirectUrl={item.redirectUrl}
                  secondColor={item.secondColor}
                  title={item.title}
                >
                  <Image
                    alt='price tracker icon'
                    height={28}
                    src={item.imageUrl || ''}
                    width={28}
                  />
                </QuickAccessCard>
              ))}
            </div>
          </SwiperSlide> */}
        </Swiper>

        <div className='hidden justify-end'>
          <div className='mr-[100px] cursor-pointer'>
            <Edit className='w-[16px] text-heading inline' />
            <span className='text-[10px] font-normal text-heading ml-[5px]'>
              Customize Order
            </span>
          </div>
        </div>
        {isOverflowing && (
          <LeftRoundButton
            classCont='mt-[12px] ml-[6px]'
            onClick={() => sideScroll(containerRef.current, 10, 400, -10)}
          />
        )}
        <div
          className='hidden md:flex pb-[16px] lg:pb-[24px] md:gap-x-[14px] lg:gap-x-[28px] lg:pt-[25px] lg:mx-[30px] overflow-auto w-full lg:w-[calc(100%-60px)] customScrollbar relative'
          ref={containerRef}
        >
          {modifiedQuickAccessData.map((item) => (
            <QuickAccessCard
              firstColor={item.firstColor}
              key={item.title}
              redirectUrl={item.redirectUrl}
              secondColor={item.secondColor}
              title={item.title}
            >
              <Image
                alt='price tracker icon'
                height={28}
                src={item.imageUrl || ''}
                width={28}
              />
            </QuickAccessCard>
          ))}
        </div>
        {isOverflowing && (
          <RightRoundButton
            classCont='mt-[12px]'
            onClick={() => sideScroll(containerRef.current, 10, 400, 10)}
          />
        )}
      </div>
    </CommonContainer>
  );
};

export default QuickAccess;

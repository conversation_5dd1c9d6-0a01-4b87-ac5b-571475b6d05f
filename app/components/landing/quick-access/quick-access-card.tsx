import React from 'react';
import Pattern1SVG from '../../svg/patterns/pattern1';
import SmartLink from '../../common/smart-link';
import { LinkType } from '@/utils/link-utils';

const QuickAccessCard = ({
  title,
  children,
  firstColor,
  secondColor,
  redirectUrl,
}: {
  title: string;
  children: React.ReactElement;
  firstColor: string;
  secondColor: string;
  thirdColor?: string;
  redirectUrl: string;
}) => {
  return (
    <SmartLink href={redirectUrl || ''} linkType={LinkType.INTERNAL}>
      <div className='w-[72px] lg:w-[111px] h-[84px] shrink-0 rounded-[10px] overflow-hidden relative shadow-md cursor-pointer'>
        <Pattern1SVG className='text-white opacity-30 pl-[13px] shrink-0 absolute top-[17px] rotate-[90deg] z-[1] left-[-14px]' />
        <div className='absolute' />
        <div
          className={`${firstColor} h-[51px] w-full flex items-center justify-center relative`}
        >
          {children}
        </div>
        <div
          className={`${secondColor} h-[33px] bg-[#6557CE] flex items-center justify-center text-center`}
        >
          <span className='text-[8px] lg:text-[10px] font-semibold text-white uppercase w-min lg:w-auto'>
            {title}
          </span>
        </div>
      </div>
    </SmartLink>
  );
};

export default QuickAccessCard;

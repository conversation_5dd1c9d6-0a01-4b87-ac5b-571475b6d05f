'use client';
import React, { useMemo, useRef } from 'react';
import CommonContainer from '../../common-container';
import Pattern2 from '../../svg/patterns/pattern2';
import Image from 'next/image';
import onGoingSaleOffersImg from '@/public/img/on-going-sale-offers.png';
import MainOfferCard from '../../cards/main-offer-card';
import PlusIcon from '../../svg/plus-icon';
import Pattern5 from '../../svg/patterns/pattern5';
import SectionSeeAllBtn from '../../atoms/section-see-all';
import { LeftRoundButton, RightRoundButton } from '../../atoms/rounded-buttons';
import { formatIndRs, sideScroll } from '@/utils/helpers';
import type { PromiseStatus } from '@/types/global-types';
import { useRouter } from 'next/navigation';
import type { ContextOngoingOfferType } from '@/services/api/data-contracts';
import { useOverflowing } from '@/utils/custom-hooks';
import { ChevronRightCircle } from 'lucide-react';
import ThemeButton from '../../atoms/theme-btn';
import { useWindowSize } from 'usehooks-ts';

const Index = ({
  onGoingOffersData,
  promiseStatus,
}: {
  onGoingOffersData: ContextOngoingOfferType[];
  promiseStatus: PromiseStatus;
}) => {
  const router = useRouter();
  const containerRef = useRef<HTMLDivElement | null>(null);
  const isOverflowing: boolean = useOverflowing(containerRef);
  const { width = 0 } = useWindowSize();
  const filteredOnGoingOffersData = useMemo(() => {
    if (onGoingOffersData.length > 6 && width < 640) {
      return onGoingOffersData.slice(0, 6);
    }
    return onGoingOffersData;
  }, [onGoingOffersData, width]);
  if (promiseStatus === 'rejected') {
    return;
  }
  return (
    <CommonContainer className='OnGoingSalesWrapper lg:flex lg:rounded-none'>
      <div className='relative bg-transparent lg:bg-[#EAEAEA] lg:dark:bg-[#1f2022] lg:w-[162px] flex justify-center shrink-0'>
        <Pattern2 className='text-[#E2E2E2] dark:text-[#3B3D45] shrink-0 absolute top-[15px] left-[13px] lg:hidden' />
        <Pattern5 className='text-[#D7D7D7] dark:text-[#3B3D45] shrink-0 absolute hidden lg:block top-[15px] left-[13px]' />
        <div className='flex mt-[21px] lg:mt-0 items-center justify-center lg:flex-col lg:w-min text-center'>
          <Image
            alt='shop image'
            className='w-[40px] h-[40px] lg:w-[80px] lg:h-[80px]'
            quality={100}
            src={onGoingSaleOffersImg}
          />
          <h3 className='text-sm md:text-lg lg:text-sm lg:font-[400] text-heading font-medium font-pat ml-[11px] lg:ml-0'>
            On Going Sale Offers
          </h3>
          <SectionSeeAllBtn
            isMobile={false}
            onClick={() => router.push('on-going-sale-offers')}
          />
        </div>
      </div>
      {isOverflowing && (
        <LeftRoundButton
          classCont='mt-[0px] ml-[12px]'
          onClick={() => sideScroll(containerRef.current, 10, 400, -10)}
        />
      )}
      <div
        className='mt-[20px] lg:mt-0 grid grid-cols-2 md:grid-cols-3 gap-4 px-[8px] lg:gap-x-6 lg:flex relative lg:py-[40px] pt-[10px] lg:mx-[30px] overflow-auto w-full lg:w-[calc(100%-60px)] customScrollbar'
        ref={containerRef}
      >
        {filteredOnGoingOffersData.map((item) => {
          return (
            <MainOfferCard
              duration={item.endDate}
              hideCbTag={item?.hideCbTag}
              isAutoGenerated={item?.isAutoGenerated}
              isOfferUpto={Boolean(item?.offerCaption?.trim())}
              key={item.uid}
              offerTitle={item.offerTitle}
              productImgUrl={item.productImage}
              saved={item.saved}
              showNewBadge={false}
              storeImgUrl={item.storeLogoUrl}
              storeName={item.storeName}
              uid={item.uid}
            >
              {/* first child */}
              <p dangerouslySetInnerHTML={{ __html: item.offerTitle }} />
              {/* second child */}
              <p className='text-primary dark:text-white font-medium text-[9px]'>
                {item.salePrice > 0 && (
                  <>
                    Offer Applied Price
                    <span className='font-nexa font-black ml-[4px] text-[10px] sm:text-[11px] '>
                      {formatIndRs(item.salePrice)}
                    </span>
                  </>
                )}
              </p>
              {/* third child */}
              <>
                <PlusIcon className='text-black shrink-0' />
                <p className='text-[10px] text-black font-bold leading-none ml-[4px] mt-[2px] truncate'>
                  {item.offerCaption}
                </p>
              </>
              {/* fourth child */}
              <div className=' h-full flex items-center justify-center'>
                <Image
                  alt='img'
                  height={22}
                  src={item.saleLogoUrl}
                  width={22}
                />
                <span className='ml-[5px] text-[9px] font-[300] text-content'>
                  {item.saleCaption}
                </span>
              </div>
            </MainOfferCard>
          );
        })}

        {/* See All Card */}
        <ThemeButton
          className='!w-fit hidden lg:flex text-xs sm:text-sm lg:text-base m-auto uppercase mr-4 whitespace-nowrap px-3'
          icon={<ChevronRightCircle className='size-6 ml-2' />}
          onClick={() => router.push('on-going-sale-offers')}
          text='See All Offers'
        />
      </div>
      {isOverflowing && (
        <RightRoundButton
          classCont='mt-[0px] ml-[6px]'
          onClick={() => sideScroll(containerRef.current, 10, 400, 10)}
        />
      )}

      <SectionSeeAllBtn
        onClick={() => router.push('on-going-sale-offers')}
        text='See All Offers'
      />
    </CommonContainer>
  );
};

export default Index;

'use client';

import React, { useMemo, useRef, useState } from 'react';
import CommonContainer from '../../common-container';
import Pattern2 from '../../svg/patterns/pattern2';
import Image from 'next/image';
import trendingOfferImg from '@/public/img/trending-offers.png';
import SlidingButton from '../../atoms/sliding-button';
import MainOfferCard from '../../cards/main-offer-card';
import PlusIcon from '../../svg/plus-icon';
import CopySVG from '../../svg/copy';
import Pattern5 from '../../svg/patterns/pattern5';
import SectionSeeAllBtn from '../../atoms/section-see-all';
import { LeftRoundButton, RightRoundButton } from '../../atoms/rounded-buttons';
import { formatIndRs, sideScroll } from '@/utils/helpers';
import type { PromiseStatus } from '@/types/global-types';
import { useRouter } from 'next/navigation';
import { useCopyCoupon, useOverflowing } from '@/utils/custom-hooks';
import type { TrendingOfferResponseType } from '@/services/api/data-contracts';
import ThemeButton from '../../atoms/theme-btn';
import { ChevronRightCircle } from 'lucide-react';
import { useWindowSize } from 'usehooks-ts';

const buttonDetails = [
  { title: 'Deals', value: 'Deals' },
  { title: 'Coupons', value: 'Coupons' },
];

// Generate placeholder array for skeleton loading
const generatePlaceholderArray = (count: number) => Array(count).fill(null);

const Index = ({
  trendingOffers,
  promiseStatus,
}: {
  trendingOffers: TrendingOfferResponseType;
  promiseStatus: PromiseStatus;
}) => {
  const [offerType, setOfferType] = useState<'Deals' | 'Coupons'>('Deals');
  const containerRef = useRef<HTMLDivElement | null>(null);
  const router = useRouter();
  const handleCopyCoupon = useCopyCoupon();

  const { width = 0 } = useWindowSize();

  // if mobile then limit stores upto 6 only
  const trendingDeals = useMemo(() => {
    if (
      trendingOffers.deals.length > 0 &&
      trendingOffers.deals.length > 6 &&
      width < 640
    ) {
      return trendingOffers.deals.slice(0, 6);
    }

    return trendingOffers.deals || [];
  }, [trendingOffers.deals, width]);

  const trendingCoupons = useMemo(() => {
    if (
      trendingOffers.coupons.length > 0 &&
      trendingOffers.coupons.length > 6 &&
      width < 640
    ) {
      return trendingOffers.coupons.slice(0, 6);
    }

    return trendingOffers.coupons || [];
  }, [trendingOffers.coupons, width]);

  const isOverflowing: boolean = useOverflowing(containerRef, offerType);
  // Show loading state if the promise is not fulfilled yet (assuming it's pending)

  // If rejected and no data, don't render anything
  if (promiseStatus === 'rejected') {
    return null;
  }

  return (
    <CommonContainer className='trendingOffersSection lg:flex lg:rounded-none'>
      <div className='relative bg-transparent lg:bg-[#EAEAEA] lg:dark:bg-[#1f2022] lg:w-[162px] flex justify-center shrink-0 '>
        <Pattern2 className='text-[#E2E2E2] dark:text-[#3B3D45] shrink-0 absolute top-[15px] left-[13px] lg:hidden' />
        <Pattern5 className='text-[#D7D7D7] dark:text-[#3B3D45] shrink-0 absolute hidden lg:block top-[15px] left-[13px]' />
        <div className='flex mt-[21px] lg:mt-0 items-center justify-center lg:flex-col lg:w-min text-center'>
          <Image
            alt='shop image'
            className='w-[40px] h-[40px] lg:w-[80px] lg:h-[80px]'
            quality={100}
            src={trendingOfferImg}
          />
          <h3 className='text-sm md:text-lg lg:text-sm lg:font-[400] text-heading font-medium font-pat ml-[11px] lg:ml-0'>
            Trending Offers
          </h3>
          <SectionSeeAllBtn
            isMobile={false}
            onClick={() => router.push('deals-and-coupons?offerType=trending')}
          />
        </div>
      </div>

      {isOverflowing && (
        <LeftRoundButton
          classCont='mt-[0px] ml-[12px]'
          onClick={() => sideScroll(containerRef.current, 10, 400, -10)}
        />
      )}

      <div className='overflow-hidden grow customScrollbar'>
        <div className='mt-[26px] lg:bg-white lg:dark:bg-[#191a1c] lg:mt-0 lg:py-[12px] lg:shadow'>
          <SlidingButton
            buttonDetails={buttonDetails}
            defaultSelectedBtn={1}
            onChange={(e: any) => setOfferType(e.target.value)}
            uniqueId='trendingOffers'
          />
        </div>

        <div
          className='mt-[20px] grid grid-cols-2 md:grid-cols-3 gap-4 lg:gap-6 lg:flex relative lg:mt-[25px] px-[8px] pt-[10px] lg:pb-[30px] lg:mx-[30px] overflow-x-auto overflow-y-visible w-full lg:w-[calc(100%-60px)] customScrollbar'
          ref={containerRef}
        >
          {promiseStatus !== 'fulfilled' ? (
            // Render skeleton loaders when loading
            generatePlaceholderArray(6).map((index) => (
              <MainOfferCard
                duration=''
                isLoading={true}
                isOfferUpto={false}
                // Using a stable key with a unique prefix to avoid index-only key warning
                key={`skeleton-${offerType}-${index}`}
                offerTitle=''
                productImgUrl=''
                showNewBadge={false}
                storeImgUrl=''
                storeName=''
                // Using a numeric UID to match the component's expectations
                uid={index + 1000}
              >
                {/* Empty children for skeleton */}
                <span />
                <span />
                <span />
                <span />
              </MainOfferCard>
            ))
          ) : offerType === 'Deals' ? (
            trendingDeals.length <= 0 ? (
              <div className='flex items-center justify-center w-full h-full'>
                No Deals Available
              </div>
            ) : (
              <>
                {trendingDeals.map((item) => (
                  <MainOfferCard
                    duration={item.endDate}
                    hideCbTag={item.hideCbTag}
                    isAutoGenerated={item.isAutoGenerated}
                    isOfferUpto={Boolean(item?.offerCaption?.trim())}
                    key={item.uid}
                    offerTitle={item.offerTitle}
                    productImgUrl={item.productImage || ''}
                    saved={item.saved}
                    showNewBadge={false}
                    storeImgUrl={item.storeLogoUrl}
                    storeName={item.storeName}
                    uid={item.uid}
                  >
                    {/* first child */}
                    <p>{item.offerTitle}</p>
                    {/* second child */}
                    <p className='text-primary dark:text-white font-medium text-[9px]'>
                      {item.salePrice > 0 && ( // Check if salePrice is greater than 0
                        <>
                          Offer Applied Price
                          <span className='font-nexa font-black ml-[4px] text-[10px] sm:text-[11px]'>
                            {formatIndRs(item.salePrice)}
                          </span>
                        </>
                      )}
                    </p>
                    {/* third child */}
                    <>
                      <PlusIcon className='text-black shrink-0' />
                      <p className='text-[10px] text-black font-bold leading-none ml-[4px] mt-[2px] truncate'>
                        {item.offerCaption}
                      </p>
                    </>
                    {/* fourth child */}
                    <div className='bg-primary p-[4px] h-full rounded-b-[6px] relative flex-center'>
                      <span className='text-[10px] font-semibold text-white'>
                        GRAB DEAL
                      </span>
                    </div>
                  </MainOfferCard>
                ))}
                {/* See All Card */}
                <ThemeButton
                  className='!w-fit hidden lg:flex text-xs sm:text-sm lg:text-base m-auto uppercase mr-4 whitespace-nowrap px-3'
                  icon={<ChevronRightCircle className='size-6 ml-2' />}
                  onClick={() => router.push('deals-and-coupons')}
                  text='See All Offers'
                />
              </>
            )
          ) : trendingCoupons.length <= 0 ? (
            <div className='flex items-center justify-center w-full h-full text-xs text-primary font-semibold'>
              We are finding the best coupons for you!!
            </div>
          ) : (
            <>
              {trendingCoupons.map((item) => (
                <MainOfferCard
                  duration={item.endDate}
                  isOfferUpto={Boolean(item?.offerCaption?.trim())}
                  key={item.uid}
                  offerTitle={item.offerTitle}
                  productImgUrl={item.productImage || ''}
                  saved={item.saved}
                  showNewBadge={false}
                  storeImgUrl={item.storeLogoUrl}
                  storeName={item.storeName}
                  uid={item.uid}
                >
                  {/* first child */}
                  <p>{item.offerTitle}</p>
                  {/* second child */}
                  <p className='text-primary dark:text-white font-medium text-[9px]'>
                    {item.salePrice > 0 && (
                      <>
                        Offer Applied Price
                        <span className='font-nexa font-black ml-[4px] text-[10px] sm:text-[11px]'>
                          {formatIndRs(item.salePrice)}
                        </span>
                      </>
                    )}
                  </p>
                  {/* third child */}
                  <>
                    <PlusIcon className='text-black shrink-0' />
                    <p className='text-[10px] text-black font-bold leading-none ml-[4px] mt-[2px] truncate'>
                      {item.offerCaption}
                    </p>
                  </>
                  {/* fourth child */}
                  {item?.couponCode && (
                    <button
                      className='bg-primary p-[4px] h-full rounded-b-[6px] relative hover:bg-[#5448b0] active:bg-primary w-full'
                      onClick={(event) =>
                        handleCopyCoupon({
                          event,
                          uid: item.uid,
                          couponCode: item.couponCode,
                        })
                      }
                      type='button'
                    >
                      <div className='border-dashed border-[0.5px] border-[#E0DCFF] rounded-b-[4px] h-full flex items-center justify-center'>
                        <span className='text-[10px] sm:text-[12px] font-semibold text-white'>
                          Copy Code
                        </span>
                        <CopySVG className='text-white absolute top-[16px] right-[12px]' />
                      </div>
                    </button>
                  )}
                </MainOfferCard>
              ))}
              {/* See All Card */}
              <ThemeButton
                className='!w-fit hidden lg:flex text-base m-auto uppercase ml-4 mr-4 whitespace-nowrap px-3'
                icon={<ChevronRightCircle className='size-6 ml-2' />}
                onClick={() => router.push('deals-and-coupons')}
                text='See All Coupons'
              />
            </>
          )}
        </div>
      </div>
      {isOverflowing && (
        <RightRoundButton
          classCont='mt-[0px] ml-[6px]'
          onClick={() => sideScroll(containerRef.current, 10, 400, 10)}
        />
      )}

      <SectionSeeAllBtn
        onClick={() => router.push('deals-and-coupons?offerType=trending')}
        text='See All Offers'
      />
    </CommonContainer>
  );
};

export default Index;

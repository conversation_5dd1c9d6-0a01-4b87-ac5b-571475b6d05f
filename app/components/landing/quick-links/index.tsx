import Image from 'next/image';
import React from 'react';
import RightArrow from '../../svg/right-arrow';
import SmartLink from '../../common/smart-link';
import { LinkType } from '@/utils/link-utils';

const data: Array<{ url: string; title: string; redirectUrl: string }> = [
  {
    url: '/img/shop.png',
    title: 'Popular Stores',
    redirectUrl: '/online-free-shopping-stores?sortType=popular',
  },
  {
    url: '/img/trending-offers.png',
    title: 'Trending Offers',
    redirectUrl: '/deals-and-coupons',
  },
  {
    url: '/img/on-going-sale-offers.png',
    title: 'On Going Sale Deals',
    redirectUrl: '/on-going-sale-offers',
  },
  {
    url: '/img/find-by-category.png',
    title: 'New Stores',
    redirectUrl: '/online-free-shopping-stores?sortType=newest',
  },
  {
    url: '/img/100.png',
    title: '100% Cashback Stores',
    redirectUrl:
      '/online-free-shopping-stores?sortType=newest&subCategories=94',
  },
  {
    url: '/img/50.png',
    title: '50% Cashback Stores',
    redirectUrl:
      '/online-free-shopping-stores?sortType=newest&subCategories=95',
  },
];

const Index = () => {
  return (
    <div className='w-full dark:bg-background hidden lg:block'>
      <div className='hidden lg:flex items-center lg:gap-x-[30px] xl:gap-x-[50px] overflow-auto mx-auto max-w-fit px-2 py-1'>
        {data.map((item) => (
          <QuickLink
            href={item.redirectUrl}
            imgURL={item.url}
            key={item.title}
            title={item.title}
          />
        ))}
      </div>
    </div>
  );
};

const QuickLink = ({
  imgURL,
  title,
  href,
}: {
  imgURL: string;
  title: string;
  href: string;
}) => {
  return (
    <SmartLink href={href} linkType={LinkType.INTERNAL}>
      <div className='flex flex-col gap-y-[2px] items-center justify-center shrink-0 cursor-pointer transition-all duration-300 hover:scale-105'>
        <div className='relative transition-all duration-300 hover:shadow-sm rounded-full p-1'>
          <Image
            alt='Category img'
            className='w-8 h-8 transition-opacity duration-200 hover:opacity-100 object-contain'
            height={40}
            src={imgURL}
            style={{ opacity: 0.9 }}
            width={40}
          />
        </div>
        <div className='whitespace-nowrap leading-none'>
          <span className='text-xs font-semibold capitalize text-black dark:text-white mr-[8px] whitespace-nowrap transition-colors duration-300'>
            {title}
          </span>
          <RightArrow className='w-[11px] text-black dark:text-white inline transition-transform duration-300' />
        </div>
      </div>
    </SmartLink>
  );
};

export default Index;

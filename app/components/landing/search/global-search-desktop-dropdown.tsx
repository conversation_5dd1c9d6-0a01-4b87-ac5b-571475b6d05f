'use client';
import clsx from 'clsx';
import type React from 'react';
import { useEffect, useRef, useState } from 'react';
import GlobalSearchResult from './global-search-result';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  setGlobalSearchActive,
  setSearchValue,
  setShowSuggestions,
} from '@/redux/slices/global-search-slice';
import SearchSVG from '../../svg/search';
import CrossSVG from '../../svg/cross';
import { useOnClickOutside } from 'usehooks-ts';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';

const GlobalSearchDesktopDropdown = () => {
  const desktopSearchWrapper = useRef<HTMLDivElement>(null);
  const { isGlobalSearchActive, searchValue } = useAppSelector(
    (state) => state.globalSearch
  );
  const [isLoading, setIsLoading] = useState(false);
  const [hasFocus, setHasFocus] = useState(false);
  const dispatch = useAppDispatch();
  const globalSearchRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  useEffect(() => {
    if (globalSearchRef.current && isGlobalSearchActive) {
      globalSearchRef.current.focus();
    }
  }, [isGlobalSearchActive]);

  // Simulate loading state when typing
  useEffect(() => {
    if (searchValue && searchValue.length >= 2) {
      setIsLoading(true);
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 600);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [searchValue]);

  const handleClearSearch = () => {
    if (searchValue) {
      dispatch(setSearchValue(''));
      dispatch(setShowSuggestions(true));
      if (globalSearchRef.current) {
        globalSearchRef.current.focus();
      }
    }
  };

  const handleBlur = () => {
    // Clear search and close dropdown when clicking outside
    dispatch(setSearchValue(''));
    dispatch(setShowSuggestions(true));
    dispatch(setGlobalSearchActive(false));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      if (searchValue) {
        dispatch(setSearchValue(''));
      } else {
        dispatch(setGlobalSearchActive(false));
      }
    }
    if (e.key === 'Enter') {
      e.preventDefault();
      handleClearSearch();
      router.push(`/deals-and-coupons?searchParam=${searchValue}`);
      dispatch(setSearchValue(''));
      dispatch(setShowSuggestions(true));
      dispatch(setGlobalSearchActive(false));
    }
  };

  useOnClickOutside(desktopSearchWrapper, handleBlur);

  return (
    <div
      className={clsx(
        isGlobalSearchActive ? '!w-[555px] md:!w-[400px] lg:!w-[555px]' : 'w-0',
        'globalSearchWrapper relative h-[35px] bg-[#F2F2F2] dark:bg-[#18191D] transition-all duration-500 rounded-[5px]',
        hasFocus && 'ring-2 ring-primary/50 shadow-md'
      )}
      ref={desktopSearchWrapper}
    >
      <input
        aria-label='Search'
        className='outline-none border-none w-full h-full text-[10px] dark:text-white lg:text-xs font-medium pl-[20px] rounded-[5px] bg-transparent pr-[45px] placeholder:text-gray-500 dark:placeholder:text-gray-400'
        onBlur={() => setHasFocus(false)}
        onChange={(e) => dispatch(setSearchValue(e.target.value))}
        onFocus={() => setHasFocus(true)}
        onKeyDown={handleKeyDown}
        placeholder='Search Product, coupon, deals, cashback...etc'
        ref={globalSearchRef}
        value={searchValue}
      />
      <div
        aria-label={searchValue ? 'Clear search' : 'Search'}
        className={clsx(
          'absolute top-0 right-0 w-[45px] h-full flex items-center justify-center cursor-pointer transition-all duration-300',
          'hover:bg-gray-300/70 dark:hover:bg-[#25262b] rounded-r-[5px]',
          searchValue && 'hover:bg-gray-300/90 dark:hover:bg-[#353943]',
          isLoading && 'pointer-events-none'
        )}
        onClick={handleClearSearch}
        onKeyDown={(e) => e.key === 'Enter' && handleClearSearch()}
        role='button'
        tabIndex={0}
      >
        {isLoading ? (
          <Loader2 className='text-blackWhite w-[16px] animate-spin' />
        ) : searchValue ? (
          <CrossSVG className='text-blackWhite w-[12px] hover:scale-110 hover:text-black dark:hover:text-white transition-all duration-200' />
        ) : (
          <SearchSVG className='text-blackWhite w-[16px] hover:scale-110 hover:text-black dark:hover:text-white transition-all duration-200' />
        )}
      </div>
      {isGlobalSearchActive && <GlobalSearchResult />}
    </div>
  );
};

export default GlobalSearchDesktopDropdown;

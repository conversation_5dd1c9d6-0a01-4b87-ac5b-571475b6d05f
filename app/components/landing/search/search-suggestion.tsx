import { setSearchValue, setShowSuggestions, removeSuggestion } from '@/redux/slices/global-search-slice';
import SuggestionArrowSVG from '../../svg/suggestion-arrow';
import { useAppDispatch } from '@/redux/hooks';
import CrossSVG from '../../svg/cross';
import clsx from 'clsx';
import { useState } from 'react';

export const SearchSuggestion = ({
  suggestedStore,
  offering,
  id,
  onRemove
}: {
  suggestedStore: string;
  offering?: string;
  id: number;
  onRemove?: (id: number) => void;
}) => {
  const dispatch = useAppDispatch();
  const [isHovered, setIsHovered] = useState(false);
  const [isRemoved, setIsRemoved] = useState(false);

  const handleClickSuggestion = () => {
    // Prefill the search with the suggestion instead of redirecting
    dispatch(setSearchValue(suggestedStore));
    dispatch(setShowSuggestions(false));
    // Don't close the search dropdown so user can see results
  };

  const handleRemoveSuggestion = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering parent click
    
    // Mark this suggestion as removed
    setIsRemoved(true);
    
    // If onRemove callback provided, call it
    if (onRemove) {
      onRemove(id);
    }
    
    // Dispatch action to remove suggestion if we're using Redux state
    // This assumes we've added a removeSuggestion action to the slice
    dispatch(removeSuggestion(id));
  };

  // Don't render component if it's been removed
  if (isRemoved) {
    return null;
  }

  return (
    <div 
      aria-label={`Search for ${suggestedStore}`}
      className={clsx(
        'flex justify-between items-center px-[8px] py-2 cursor-pointer rounded transition-all duration-200',
        isHovered ? 'bg-gray-100 dark:bg-gray-800' : 'hover:bg-gray-50 dark:hover:bg-gray-800/50'
      )}
      onClick={handleClickSuggestion}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      role="button"
      tabIndex={0}
    >
      <div className='flex items-center'>
        <SuggestionArrowSVG className={clsx(
          'text-black dark:text-white w-[10px] h-[10px] transition-transform duration-200',
          isHovered && 'translate-x-1'
        )} />
        <span className={clsx(
          'ml-[12px] text-[10px] lg:text-[12px] font-normal transition-colors duration-200',
          isHovered ? 'text-primary dark:text-primary' : 'dark:text-content'
        )}>
          {suggestedStore}
        </span>
      </div>

      <div className='flex items-center'>
        {offering && (
          <span className='text-[10px] lg:text-xs text-[#7366D9] mr-2'>
            {offering}
          </span>
        )}
        <button
          aria-label="Remove suggestion"
          className={clsx(
            'p-1.5 rounded-full transition-colors duration-200',
            isHovered ? 'bg-gray-200 dark:bg-gray-700' : 'hover:bg-gray-200 dark:hover:bg-gray-700'
          )}
          onClick={handleRemoveSuggestion}
          type="button"
        >
          <CrossSVG className={clsx(
            'text-black dark:text-white w-[10px] h-[10px]',
            isHovered && 'text-gray-700 dark:text-gray-300'
          )} />
        </button>
      </div>
    </div>
  );
};

'use client';
import type React from 'react';
import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import PillButton from '../../atoms/pills';
import FindByCategoryCard from '../../cards/find-by-category-card';
import RoundArrow from '../../svg/round-arrow';
import { SearchSuggestion } from './search-suggestion';
import SearchResultCard from './search-result-card';
import { toast } from 'react-toastify';
import {
  setGlobalSearchActive,
  setGlobalSearchResult,
  setNoDataFound,
  setSearchValue,
  setShowSuggestions,
  removeSuggestion,
  setShowSearchLeftPanel,
} from '@/redux/slices/global-search-slice';
import fetchWrapper from '@/utils/fetch-wrapper';
import { BASE_URL } from '@/config';
import type { SearchResponseItem } from '@/services/api/data-contracts';
import { useRouter } from 'next/navigation';
import { useDebounceValue } from 'usehooks-ts';
import { formatStoreName, generateProductUrl } from '@/utils/helpers';
import { Loader2 } from 'lucide-react';
import clsx from 'clsx';

export const getGlobalSearchResults = async ({
  searchQuery,
}: {
  searchQuery: string;
}) => {
  const data = await fetchWrapper<SearchResponseItem>(
    `${BASE_URL}/context/search?text=${searchQuery}`
  );
  return data;
};

export const initialDataGlobalSearchResult: SearchResponseItem = {
  deals: { dealList: [], total: 0 },
  coupons: { couponList: [], total: 0 },
  giftCards: { giftCardList: [], total: 0 },
  stores: { storeList: [], total: 0 },
};

const GlobalSearchResult = () => {
  const {
    isShowSuggestions,
    searchValue,
    globalSearchResult,
    noDataFound,
    suggestions,
  } = useAppSelector((state) => state.globalSearch);
  const [selectedPill, setSelectedPill] = useState<number>(1);
  const [isLoading, setIsLoading] = useState(false);
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [debouncedSearchValue] = useDebounceValue(searchValue, 300);

  useEffect(() => {
    (async () => {
      try {
        if (debouncedSearchValue.length >= 3) {
          setIsLoading(true);
          const result = await getGlobalSearchResults({
            searchQuery: debouncedSearchValue,
          });
          if (result) {
            dispatch(setGlobalSearchResult(result));
            const hasResults =
              (result.coupons?.total || 0) +
                (result.deals?.total || 0) +
                (result.stores?.total || 0) >
              0;
            dispatch(setNoDataFound(!hasResults));
          }
          setIsLoading(false);
        } else {
          dispatch(setGlobalSearchResult(initialDataGlobalSearchResult));
          dispatch(setShowSuggestions(true));
          dispatch(setNoDataFound(false));
        }
      } catch (error) {
        console.error('Search failed:', error);
        setIsLoading(false);
        toast.error('Failed to get search results. Please try again.');
      }
    })();
  }, [debouncedSearchValue, dispatch]);

  const { coupons, deals, stores } = globalSearchResult;

  const totalSearchResultCount =
    (coupons?.total || 0) + (deals?.total || 0) + (stores?.total || 0);

  const Pills: Array<{ id: number; name: string; count: number }> = [
    {
      id: 1,
      name: 'All',
      count: totalSearchResultCount,
    },
    {
      id: 2,
      name: 'Coupons',
      count: coupons?.total || 0,
    },
    {
      id: 3,
      name: 'Deals',
      count: deals.total || 0,
    },
    {
      id: 4,
      name: 'Stores',
      count: stores.total || 0,
    },
  ];

  const handlerDeactivateGSearch = () => {
    dispatch(setSearchValue(''));
    dispatch(setGlobalSearchActive(false));
    dispatch(setShowSearchLeftPanel(false));
  };

  const handleRedirectDeal = ({
    storeName,
    offerTitle,
    uid,
  }: {
    storeName: string;
    offerTitle: string;
    uid: number;
  }) => {
    const url = generateProductUrl(storeName, offerTitle);
    router.push(`/store/${formatStoreName(storeName)}/${url}?uid=${uid}`);
  };

  const handleRemoveSuggestion = (id: number) => {
    dispatch(removeSuggestion(id));
  };

  return (
    <section className='px-[8px] py-[24px] lg:pt-[24px] lg:p-[18px] mt-1 overflow-auto lg:bg-[#F2F2F2] lg:dark:bg-[#35383E] lg:absolute top-[51px] lg:top-[35px] rounded-[5px] z-[999] w-full shadow-lg lg:shadow-xl dark:shadow-gray-900/30 lg:max-h-[600px] customScrollbar overscroll-contain animate-fadeIn'>
      {/* Loading state */}
      {isLoading && (
        <div className='flex items-center justify-center py-8 animate-pulse'>
          <Loader2 className='w-8 h-8 text-primary animate-spin' />
          <span className='ml-3 text-sm font-medium'>Searching...</span>
        </div>
      )}

      {/* Suggestions state - shown when no search or results */}
      {!isLoading && isShowSuggestions && !totalSearchResultCount && (
        <div className='flex flex-col gap-y-[24px] lg:pb-[20px] md:px-[20px] lg:px-0 customScrollbar'>
          {globalSearchResult.suggestions &&
            globalSearchResult.suggestions.some((s) => s.confidence > 0.6) && (
              <div className='mb-3 px-2'>
                <p className='text-sm text-gray-600 dark:text-gray-300'>
                  Did you mean:{' '}
                  {globalSearchResult.suggestions
                    .filter((s) => s.confidence > 0.6)
                    .map((s, i, arr) => (
                      <button
                        className='text-primary hover:underline font-medium'
                        key={i}
                        onClick={() => dispatch(setSearchValue(s.suggestion))}
                      >
                        {s.suggestion}
                        {i < arr.length - 1 ? ', ' : ''}
                      </button>
                    ))}
                </p>
              </div>
            )}
          <h3 className='text-sm font-medium mb-2 text-gray-600 dark:text-gray-300'>
            Suggested searches:
          </h3>
          {suggestions.map((item) => (
            <SearchSuggestion
              id={item.id}
              key={item.id}
              offering={item.offering}
              onRemove={handleRemoveSuggestion}
              suggestedStore={item.name}
            />
          ))}
        </div>
      )}

      {/* No results state */}
      {!isLoading &&
        !isShowSuggestions &&
        noDataFound &&
        searchValue.length >= 3 && (
          <div className='flex flex-col items-center justify-center py-10'>
            <div className='text-gray-500 dark:text-gray-400 text-center mb-4'>
              <p className='text-lg mb-1'>
                No results found for "{searchValue}"
              </p>
              <p className='text-sm'>
                Try different keywords or check for typos
              </p>
            </div>
            <button
              className='px-4 py-2 mt-2 text-sm bg-primary/10 hover:bg-primary/20 text-primary rounded-md transition-colors'
              onClick={() => dispatch(setShowSuggestions(true))}
            >
              View suggestions
            </button>
          </div>
        )}

      {/* Search Results */}
      {!isLoading &&
        (!isShowSuggestions || totalSearchResultCount > 0) &&
        !noDataFound && (
          <div className='globalSearchResults'>
            {/* Did you mean suggestions */}
            {globalSearchResult.suggestions &&
              globalSearchResult.suggestions.some(
                (s) => s.confidence > 0.6
              ) && (
                <div className='mb-3 px-2'>
                  <p className='text-sm text-gray-600 dark:text-gray-300'>
                    Did you mean:{' '}
                    {globalSearchResult.suggestions
                      .filter((s) => s.confidence > 0.6)
                      .map((s, i, arr) => (
                        <button
                          className='text-primary hover:underline font-medium'
                          key={i}
                          onClick={() => dispatch(setSearchValue(s.suggestion))}
                        >
                          {s.suggestion}
                          {i < arr.length - 1 ? ', ' : ''}
                        </button>
                      ))}
                  </p>
                </div>
              )}

            {/* Pills for filtering */}
            <div className='lg:px-[12px] flex gap-x-[8px] overflow-auto customScrollbar pb-[5px]'>
              {Pills.map((item) =>
                item.count > 0 ? (
                  <PillButton
                    className={clsx(
                      'border-[1px] border-primary dark:text-white transition-colors duration-300',
                      item.id === selectedPill
                        ? 'shadow-md bg-primary/10 dark:bg-primary/30'
                        : 'hover:bg-primary/5 hover:shadow-sm dark:hover:bg-primary/20'
                    )}
                    isSelected={item.id === selectedPill}
                    key={item.id}
                    onClick={() => setSelectedPill(item.id)}
                    text={`${item.name} `}
                  >
                    <span className='font-bold transition-all duration-200 hover:scale-105'>
                      ({item.count})
                    </span>
                  </PillButton>
                ) : null
              )}
            </div>

            {/* Deals section */}
            {(selectedPill === 1 || selectedPill === 3) && (
              <SearchResultCont
                count={deals?.total || 0}
                onClickSeeAll={() => {
                  handlerDeactivateGSearch();
                  router.push(
                    `/deals-and-coupons?searchParam=${searchValue}&offerType=deals`
                  );
                }}
                title='Deals'
              >
                {deals?.dealList?.length > 0 ? (
                  <div className='flex flex-col gap-y-[24px] mt-[17px] mb-[10px] pr-[8px]'>
                    {deals.dealList.map((item, index) => (
                      <SearchResultCard
                        cashback={item.caption || ''}
                        imgSrc={item.url || ''}
                        key={`deal-${item.uid || index}`}
                        newUsers={item.newUserOffer}
                        oldUsers={item.oldUserOffer}
                        onClickCard={() => {
                          handlerDeactivateGSearch();
                          handleRedirectDeal({
                            storeName: item.name,
                            offerTitle: item.title,
                            uid: item.uid,
                          });
                        }}
                        title={item.title}
                      />
                    ))}
                  </div>
                ) : (
                  <div className='flex justify-center py-4 text-sm text-gray-500 dark:text-gray-400'>
                    No deals found for this search
                  </div>
                )}
              </SearchResultCont>
            )}

            {/* Stores section */}
            {(selectedPill === 1 || selectedPill === 4) && (
              <SearchResultCont
                count={stores.total}
                onClickSeeAll={() => {
                  handlerDeactivateGSearch();
                  router.push(
                    `/online-free-shopping-stores?searchParam=${searchValue}`
                  );
                }}
                title='Stores'
              >
                {stores.storeList.length ? (
                  <div className='flex flex-wrap gap-4 mt-3'>
                    {stores.storeList.map((item) => (
                      <FindByCategoryCard
                        bgColor={(item as any)?.bgColor || '#FFFFFF'}
                        deals={item?.count || 0}
                        key={item.uid}
                        offering={item?.caption || ''}
                        onClickCard={() => {
                          handlerDeactivateGSearch();
                          router.push(
                            `/store/${formatStoreName(item.name)}?brandID=${
                              item.uid
                            }`
                          );
                        }}
                        searchResultStyle
                        title={item.name}
                        url={item.url || ''}
                      />
                    ))}
                  </div>
                ) : (
                  <div className='flex justify-center py-4 text-sm text-gray-500 dark:text-gray-400'>
                    No stores found for this search
                  </div>
                )}
              </SearchResultCont>
            )}

            {/* Coupons section */}
            {(selectedPill === 1 || selectedPill === 2) && (
              <SearchResultCont
                count={coupons?.total || 0}
                onClickSeeAll={() => {
                  handlerDeactivateGSearch();
                  router.push(
                    `/deals-and-coupons?searchParam=${searchValue}&offerType=coupons`
                  );
                }}
                title='Coupons'
              >
                {coupons?.couponList?.length ? (
                  <div className='flex flex-col gap-y-[24px] mt-[17px] mb-[10px] pr-[8px]'>
                    {coupons.couponList.map((item, index) => (
                      <SearchResultCard
                        cashback={item.caption || ''}
                        imgSrc={item.url || ''}
                        key={`coupon-${item.uid || index}`}
                        newUsers={item.newUserOffer}
                        oldUsers={item.oldUserOffer}
                        onClickCard={() => {
                          handlerDeactivateGSearch();
                          handleRedirectDeal({
                            storeName: item.name,
                            offerTitle: item.title,
                            uid: item.uid,
                          });
                        }}
                        title={item.title}
                      />
                    ))}
                  </div>
                ) : (
                  <div className='flex justify-center py-4 text-sm text-gray-500 dark:text-gray-400'>
                    No coupons found for this search
                  </div>
                )}
              </SearchResultCont>
            )}
          </div>
        )}
    </section>
  );
};

const SearchResultCont = ({
  children,
  count,
  onClickSeeAll,
  title,
}: {
  children: React.ReactNode;
  title: string;
  count?: number;
  onClickSeeAll?: () => void;
}) => {
  if (count === 0) {
    return null;
  }
  return (
    <div className='searchResultStores border-[0.5px] border-white dark:border-[#353943] bg-[#F6F6F6] dark:bg-[#2A2D35] p-[12px] mt-[16px] rounded-[5px] lg:text-sm font-normal hover:shadow-md transition-shadow duration-300'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center'>
          <span className='ml-[8px] text-[12px] lg:text-sm font-pat font-normal dark:text-white'>
            {title}{' '}
            <span className='font-nexa font-bold transition-all duration-200 hover:text-primary'>
              ({count})
            </span>
          </span>
        </div>

        {onClickSeeAll && (
          <button
            aria-label={`See all ${title}`}
            className='flex items-center cursor-pointer group'
            onClick={onClickSeeAll}
            type='button'
          >
            <span className='text-[10px] lg:text-[12px] font-medium text-primary group-hover:underline transition-all'>
              See All
            </span>
            <RoundArrow className='text-primary ml-[7px] group-hover:translate-x-0.5 transition-transform' />
          </button>
        )}
      </div>

      {children}
    </div>
  );
};

export default GlobalSearchResult;

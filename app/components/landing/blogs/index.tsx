'use client';
import React, { useRef } from 'react';
import CommonContainer from '../../common-container';
import Pattern2 from '../../svg/patterns/pattern2';
import Image from 'next/image';
import blogsIcon from '@/public/img/blogs-icon.png';
import BlogCard from '../../cards/blog-card';
import Pattern5 from '../../svg/patterns/pattern5';
import { LeftRoundButton, RightRoundButton } from '../../atoms/rounded-buttons';
import { sideScroll } from '@/utils/helpers';

const Index = () => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  return (
    <CommonContainer className='blogsWrapper lg:flex lg:rounded-none'>
      <div className='relative bg-transparent lg:bg-[#EAEAEA] lg:dark:bg-[#1f2022] lg:w-[162px] flex justify-center shrink-0'>
        <Pattern2 className='text-[#E2E2E2] dark:text-[#3B3D45] shrink-0 absolute top-[15px] left-[13px] lg:hidden' />
        <Pattern5 className='text-[#D7D7D7] dark:text-[#3B3D45] shrink-0 absolute hidden lg:block top-[15px] left-[13px]' />
        <div className='flex mt-[21px] lg:mt-0 items-center justify-center lg:flex-col lg:w-min text-center'>
          <Image
            alt='shop image'
            className='w-[40px] h-[40px] lg:w-[80px] lg:h-[80px]'
            quality={100}
            src={blogsIcon}
          />
          <h1 className='text-sm md:text-lg lg:text-sm lg:font-[400] text-heading font-medium font-pat ml-[11px] lg:ml-0'>
            Blogs
          </h1>
        </div>
      </div>

      <LeftRoundButton
        classCont='mt-[-20px] ml-[12px]'
        onClick={() => sideScroll(containerRef.current, 10, 400, -10)}
      />
      <div
        className='py-[10px] flex justify-items-center gap-x-[8px] lg:gap-x-[24px] px-[8px] lg:px-0 lg:mx-[30px] lg:py-[40px] overflow-auto w-full lg:w-[calc(100%-60px)] customScrollbar'
        ref={containerRef}
      >
        <BlogCard
          blogImg='/temp/blog1.png'
          blogTitle='How to get cash back in an 
effective Way?'
          category='Cashback'
          postedOn='Posted on 21 December 2021'
          readTime='1 Min Read'
          userName='Delvin Joseph'
          userPic='/temp/userPic.png'
        />
        <BlogCard
          blogImg='/temp/blog1.png'
          blogTitle='It’s Important to invest in 
Digital Gold!'
          category='Investment'
          postedOn='Posted on 21 December 2021'
          readTime='2 Min Read'
          userName='Ashish'
          userPic='/temp/userPic.png'
        />
        <BlogCard
          blogImg='/temp/blog1.png'
          blogTitle='How to get cash back in an 
effective Way?'
          category='Cashback'
          postedOn='Posted on 21 December 2021'
          readTime='1 Min Read'
          userName='Delvin Joseph'
          userPic='/temp/userPic.png'
        />
        <BlogCard
          blogImg='/temp/blog1.png'
          blogTitle='It’s Important to invest in 
Digital Gold!'
          category='Investment'
          postedOn='Posted on 21 December 2021'
          readTime='2 Min Read'
          userName='Ashish'
          userPic='/temp/userPic.png'
        />
        <BlogCard
          blogImg='/temp/blog1.png'
          blogTitle='How to get cash back in an 
effective Way?'
          category='Cashback'
          postedOn='Posted on 21 December 2021'
          readTime='1 Min Read'
          userName='Delvin Joseph'
          userPic='/temp/userPic.png'
        />
        <BlogCard
          blogImg='/temp/blog1.png'
          blogTitle='It’s Important to invest in 
Digital Gold!'
          category='Investment'
          postedOn='Posted on 21 December 2021'
          readTime='2 Min Read'
          userName='Ashish'
          userPic='/temp/userPic.png'
        />
        <BlogCard
          blogImg='/temp/blog1.png'
          blogTitle='How to get cash back in an 
effective Way?'
          category='Cashback'
          postedOn='Posted on 21 December 2021'
          readTime='1 Min Read'
          userName='Delvin Joseph'
          userPic='/temp/userPic.png'
        />
        <BlogCard
          blogImg='/temp/blog1.png'
          blogTitle='It’s Important to invest in 
Digital Gold!'
          category='Investment'
          postedOn='Posted on 21 December 2021'
          readTime='2 Min Read'
          userName='Ashish'
          userPic='/temp/userPic.png'
        />
      </div>

      <RightRoundButton
        classCont='mt-[-20px] ml-[6px]'
        onClick={() => sideScroll(containerRef.current, 10, 400, 10)}
      />
    </CommonContainer>
  );
};

export default Index;

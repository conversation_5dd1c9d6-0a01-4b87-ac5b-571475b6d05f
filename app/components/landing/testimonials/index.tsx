'use client';
import React, { useRef } from 'react';
import CommonContainer from '../../common-container';
import Pattern2 from '../../svg/patterns/pattern2';
import Image from 'next/image';
import starImg from '@/public/img/star.png';
import CoverflowSwiper from '../hero-slider/coverflow-swiper';
import TestimonialCard from '../../cards/testimonial-card';
import Pattern5 from '../../svg/patterns/pattern5';
import { SwiperSlide } from 'swiper/react';
import { LeftRoundButton, RightRoundButton } from '../../atoms/rounded-buttons';
import { sideScroll } from '@/utils/helpers';
import type { TestimonialResponseType } from '@/services/api/data-contracts';
import type { PromiseStatus } from '@/types/global-types';

// Generate placeholder array for skeleton loading
const generatePlaceholderArray = (count: number) => Array(count).fill(null);

const Index = ({
  testimonials,
  promiseStatus,
}: {
  testimonials: TestimonialResponseType[];
  promiseStatus: PromiseStatus;
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const isLoading = promiseStatus !== 'fulfilled';

  // Only return null if rejected and no data
  if (
    promiseStatus === 'rejected' ||
    !testimonials ||
    testimonials.length === 0
  ) {
    return null;
  }

  // Use empty array for testimonials if they're not available
  const displayTestimonials = testimonials || [];

  return (
    <CommonContainer className='testimonials lg:flex lg:rounded-none min-h-[300px]'>
      <div className='relative bg-transparent lg:bg-[#EAEAEA] lg:dark:bg-[#1f2022] lg:w-[162px] flex justify-center shrink-0'>
        <Pattern2 className='text-[#E2E2E2] dark:text-[#3B3D45] shrink-0 absolute top-[15px] left-[13px] lg:hidden' />
        <Pattern5 className='text-[#D7D7D7] dark:text-[#3B3D45] shrink-0 absolute hidden lg:block top-[15px] left-[13px]' />
        <div className='flex mt-[21px] lg:mt-0 items-center justify-center lg:flex-col lg:gap-y-[20px] lg:w-min min-w-[100px] text-center'>
          <Image
            alt='shop image'
            className='w-[40px] h-[40px] lg:w-[80px] lg:h-[80px]'
            quality={100}
            src={starImg}
          />
          <h3 className='text-sm md:text-lg lg:text-sm lg:font-[400] text-heading font-medium font-pat ml-[11px] lg:ml-0'>
            What users tell about us
          </h3>
        </div>
      </div>

      <div className='mt-[21px] my-[5px] rounded-[10px] lg:hidden'>
        <CoverflowSwiper swiperName='testimonialCoverSwiper !pb-[30px]'>
          {isLoading
            ? generatePlaceholderArray(3).map((index) => (
                <SwiperSlide key={`skeleton-testimonial-mobile-${index}`}>
                  <TestimonialCard
                    isLoading={true}
                    testimonial={{} as TestimonialResponseType}
                  />
                </SwiperSlide>
              ))
            : displayTestimonials.map(
                (testimonial: TestimonialResponseType, index) => (
                  <SwiperSlide
                    key={`testimonial-mobile-${
                      (testimonial as any).id || index
                    }`}
                  >
                    <TestimonialCard testimonial={testimonial} />
                  </SwiperSlide>
                )
              )}
        </CoverflowSwiper>
      </div>

      <LeftRoundButton
        classCont='mt-[0px] ml-[12px]'
        onClick={() => sideScroll(containerRef.current, 10, 450, -10)}
      />
      <div
        className='hidden lg:flex gap-x-[30px] py-[40px] px-[8px] mx-[30px] overflow-auto w-full lg:w-[calc(100%-60px)] customScrollbar'
        ref={containerRef}
      >
        {isLoading
          ? generatePlaceholderArray(3).map((_, index) => (
              <TestimonialCard
                isLoading={true}
                key={`skeleton-testimonial-desktop-${index}`}
                testimonial={{} as TestimonialResponseType}
              />
            ))
          : displayTestimonials.map(
              (testimonial: TestimonialResponseType, index) => (
                <TestimonialCard
                  key={`testimonial-desktop-${
                    (testimonial as any).id || index
                  }`}
                  testimonial={testimonial}
                />
              )
            )}
      </div>
      <RightRoundButton
        classCont='mt-[0px] ml-[6px]'
        onClick={() => sideScroll(containerRef.current, 10, 450, 10)}
      />
    </CommonContainer>
  );
};

export default Index;

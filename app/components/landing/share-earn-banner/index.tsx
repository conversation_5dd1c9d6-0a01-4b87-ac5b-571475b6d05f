'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import CommonContainer from '../../common-container';
import Pattern2 from '../../svg/patterns/pattern2';
import Pattern5 from '../../svg/patterns/pattern5';

const ShareEarnBanner: React.FC = () => {
  const [productLink, setProductLink] = useState('');
  const router = useRouter();

  const handleGenerate = () => {
    // Redirect to share-and-earn page with product link as URL parameter
    if (productLink.trim()) {
      const encodedLink = encodeURIComponent(productLink);
      router.push(`/share-and-earn?link=${encodedLink}`);
    } else {
      router.push('/share-and-earn');
    }
  };

  return (
    <CommonContainer className='shareEarnBannerSection lg:flex lg:rounded-none min-h-[250px] lg:min-h-[300px]'>
      {/* Left sidebar */}
      <div className='relative bg-transparent lg:bg-[#EAEAEA] lg:dark:bg-[#1f2022] lg:w-[162px] flex justify-center shrink-0'>
        <Pattern2 className='text-[#E2E2E2] dark:text-[#3B3D45] shrink-0 absolute top-[15px] left-[13px] lg:hidden' />
        <Pattern5 className='text-[#D7D7D7] dark:text-[#3B3D45] shrink-0 absolute hidden lg:block top-[15px] left-[13px]' />

        <div className='flex mt-[21px] lg:mt-0 items-center justify-center lg:flex-col lg:w-min text-center'>
          {/* Share & Earn Icon */}
          <div className='w-[40px] h-[40px] lg:w-[80px] lg:h-[80px]  flex items-center justify-center relative'>
            {/* Money image */}
            <div className='relative flex items-center'>
              {/* Money image */}
              <Image
                alt='Money'
                className='object-contain lg:w-[48px] lg:h-[48px]'
                height={24}
                src='/img/share-and-earn/money.png'
                width={24}
              />
              {/* Link image overlaying right side */}
              <div className='absolute right-0 transform translate-x-1/2'>
                <Image
                  alt='Link'
                  className='object-contain lg:w-[30px] lg:h-[30px]'
                  height={30}
                  src='/img/share-and-earn/link.png'
                  width={30}
                />
              </div>
            </div>
          </div>
          <h3 className='text-sm md:text-lg lg:text-sm lg:font-[400] text-heading font-medium font-pat ml-[11px] lg:ml-0'>
            Share & Earn
          </h3>
        </div>
      </div>

      {/* Main content area */}
      <div className='overflow-hidden grow customScrollbar px-[8px] lg:px-[30px] relative'>
        {/* Header section with title and link */}
        <div className='flex flex-col items-center text-center mt-[20px] lg:mt-[30px] mb-[20px] relative'>
          {/* Centered content */}
          <div className='flex flex-col items-center'>
            {/* Mobile coin icon above text */}
            <div className='w-[39px] h-[39px] flex items-center justify-center mb-[15px] lg:hidden'>
              <Image
                alt='Coin'
                className='object-contain'
                height={48}
                src='/img/share-and-earn/coin.png'
                width={48}
              />
            </div>

            {/* Desktop layout with coin on left side of text */}
            <div className='hidden lg:flex items-center gap-4 mb-[15px]'>
              {/* Coin icon positioned to the left of center text */}
              <div className='w-[48px] h-[48px]  flex items-center justify-center '>
                <Image
                  alt='Coin'
                  className='object-contain'
                  height={48}
                  src='/img/share-and-earn/coin.png'
                  width={48}
                />
              </div>

              {/* Main heading */}
              <h2 className='font-inter font-bold text-[25px] leading-[52px] tracking-[-0.03em] text-heading'>
                Share & Earn with IndianCashback
              </h2>
            </div>

            {/* Mobile heading */}
            <h2 className='font-inter font-bold text-[20px] leading-[28px] tracking-[-0.03em] text-heading text-center mb-[10px] lg:hidden'>
              Share & Earn with IndianCashback
            </h2>

            {/* See My Share & Earn History link */}
            <Link
              className='font-inter font-semibold text-[14px] lg:text-[16px] leading-[19px] text-center tracking-[-0.03em] text-[#4D40B9] hover:underline'
              href='/share-and-earn'
            >
              See My Share & Earn History
            </Link>
          </div>
        </div>

        {/* Description text */}
        <div className='text-center mb-[25px]'>
          <p className='font-inter font-normal text-[14px] lg:text-[16px] leading-[18px] lg:leading-[19px] tracking-[-0.03em] capitalize text-content'>
            Paste any product link, generate your unique cashback link, and
            start earning when others shop through it.
          </p>
        </div>

        {/* Input section */}
        <div className='space-y-[10px] relative'>
          {/* Decorative money image for mobile */}
          <div className='absolute -top-8 right-4 lg:hidden w-8 h-8'>
            <Image
              alt='Money'
              className='object-contain opacity-60'
              height={32}
              src='/img/share-and-earn/money.png'
              width={32}
            />
          </div>
          {/* Input label */}

          {/* Input container */}
          <div className='flex flex-col sm:flex-row gap-[10px] sm:gap-0'>
            {/* Input field */}
            <input
              className='flex-1 h-[51px] px-4 border-[0.5px] border-[#A2A2A2] rounded-[10px] sm:rounded-l-[10px] sm:rounded-r-none font-inter font-medium text-[14px] leading-[17px] tracking-[-0.03em] text-content placeholder:text-[#999999] focus:outline-none focus:border-[#7366D9] bg-white dark:bg-[#191a1c]'
              onChange={(e) => setProductLink(e.target.value)}
              placeholder='Paste supported link. Eg. https://www.flipkart.com/apple-iphone-13…'
              type='text'
              value={productLink}
            />

            {/* Generate button */}
            <button
              className='w-full sm:w-[155px] h-[51px] bg-[#7366D9] border-[0.5px] border-[#A2A2A2] rounded-[10px] sm:rounded-r-[10px] sm:rounded-l-none font-inter font-semibold text-[16px] lg:text-[18px] leading-[22px] text-white hover:bg-[#6355C8] transition-colors duration-200 flex items-center justify-center gap-2'
              onClick={handleGenerate}
            >
              <Image
                alt='Generate Link'
                className='object-contain'
                height={16}
                src='/img/share-and-earn/link.png'
                width={16}
              />
              Generate
            </button>
          </div>
        </div>
      </div>
    </CommonContainer>
  );
};

export default ShareEarnBanner;

'use client';
import React, { useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/effect-creative';
import { EffectCreative } from 'swiper/modules';
import Story from './story';
import { useDispatch } from 'react-redux';
import { setCurrentSlideIndex } from '@/redux/slices/stories-slice';
import { ResponseStoriesData } from '@/types/global-types';

const StoriesContainer = ({
  currentSlideIndex,
  storiesData,
}: {
  currentSlideIndex: number;
  storiesData: ResponseStoriesData[];
}) => {
  const [swiper, setSwiper] = useState<any>();

  const handleChangeSlide = () => {
    swiper?.slideNext();
  };

  const dispatch = useDispatch();

  return (
    <>
      <Swiper
        className='StoriesSwiper !w-full !h-[100svh] !m-0'
        creativeEffect={{
          prev: {
            shadow: true,
            translate: ['-20%', 0, -1],
          },
          next: {
            translate: ['100%', 0, 0],
          },
        }}
        effect={'creative'}
        initialSlide={currentSlideIndex}
        loop={false}
        modules={[EffectCreative]}
        onSlideChange={(swiper) =>
          dispatch(setCurrentSlideIndex(swiper.realIndex))
        }
        onSwiper={(swiper) => {
          setSwiper(swiper);
        }}
      >
        {storiesData.map((item, index) => (
          <SwiperSlide className='storySlide bg-[#2a2d35]' key={index}>
            {currentSlideIndex === index && (
              <Story
                handleChangeSlide={handleChangeSlide}
                slidesLength={storiesData.length}
                storeLogo={item.storeLogo}
                storeName={item.storeName}
                stories={item.stories}
              />
            )}
          </SwiperSlide>
        ))}
      </Swiper>
    </>
  );
};

export default StoriesContainer;

'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import CommonHeader from '@/app/components/headers/common-header';
import Image from 'next/image';
import React from 'react';
import AboutDottedLineSVG1 from '@/public/svg/about-us/dotted-line1.svg';
import AboutDottedLineSVG2 from '@/public/svg/about-us/dotted-line2.svg';
import AboutHighlightCard from '../components/atoms/about-highlight-card'; // Assuming this component exists
import TelegramSVG from '../components/svg/social/telegram';
import TwitterSVG from '../components/svg/social/twitter';
import InstagramSVG from '../components/svg/social/instagram';
import FacebookSVG from '../components/svg/social/facebook';
import YoutubeSVG from '../components/svg/social/youtube';
import SmartLink from '../components/common/smart-link';
import { LinkType } from '@/utils/link-utils';
import WhatsappSVG from '../components/svg/social/whatsapp';
// Animation: 1. Import the necessary animation libraries
import { motion } from 'framer-motion';

// --- Animation Variants ---

// Animation: 2. Add container animations for the entire page (simple fade-in)
const pageContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.5,
    },
  },
};

// Animation: 3. Add staggered animations for each section
const sectionContainerVariants = {
  hidden: { opacity: 1 }, // Parent doesn't fade, just orchestrates children
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2, // Time delay between each child animating in
    },
  },
};

const sectionItemVariants = {
  hidden: { opacity: 0, y: 30 }, // Start slightly lower and invisible
  visible: {
    opacity: 1,
    y: 0, // Animate to original position and full opacity
    transition: {
      duration: 0.5,
      ease: 'easeOut',
    },
  },
};

// Animation: 5. Add scroll animations for the milestone cards (staggered within container)
const milestoneContainerVariants = {
  hidden: { opacity: 1 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1, // Faster stagger for cards
    },
  },
};

const milestoneItemVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.4,
      ease: 'easeOut',
    },
  },
};

// Animation: 6. Add subtle animations for images (on scroll)
const imageVariants = {
  hidden: { opacity: 0, scale: 0.95 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: 'easeOut',
    },
  },
};

const IndexClientsAboutUs = () => {
  return (
    <>
      <CommonHeader headline='About Us' />
      {/* Animation: 2. Apply container animation */}
      <motion.section
        animate='visible'
        className='header-container'
        initial='hidden'
        variants={pageContainerVariants}
      >
        <BreadcrumbSaveShare
          breadCrumbs={[
            { title: 'Cashback Home', link: '/' },
            { title: 'About Us' },
          ]}
        />
        <div className='mx-[6px] px-[8px] lg:mx-0 lg:px-[40px] py-[26px] text-blackWhite bg-container lg:bg-[#E0E0E0] lg:bg-container rounded-b-[10px]'>
          {/* Animation: 3. Container for staggered sections */}
          <motion.div
            initial='hidden'
            variants={sectionContainerVariants}
            viewport={{ once: true, amount: 0.1 }} // Trigger early, only once
            // Use whileInView for sections to trigger as they appear, or 'animate' for initial load stagger
            whileInView='visible'
          >
            {/* --- Mission & Vision Section --- */}
            <motion.div
              className='mt-[51px] lg:mt-[70px]'
              variants={sectionItemVariants}
            >
              <h4 className='text-[14px] lg:text-[24px] font-pat text-center mb-[20px]'>
                Our Mission & Vision
              </h4>
              <div className='flex flex-col lg:flex-row gap-[20px] lg:gap-[40px] justify-center mb-[40px]'>
                {/* Animation: 4. Hover animations (example - subtle scale) */}
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[15px] lg:p-[20px] rounded-[10px] shadow-md max-w-[500px]'
                  whileHover={{ scale: 1.03, transition: { duration: 0.2 } }}
                >
                  <h5 className='font-pat text-[12px] lg:text-[18px] font-normal text-center mb-[10px] text-primary'>
                    Our Mission
                  </h5>
                  <p className='text-[9px] lg:text-xs font-medium text-center'>
                    To revolutionize the online shopping experience in India by
                    providing substantial savings to consumers through cashback
                    and innovative financial solutions.
                  </p>
                </motion.div>
                {/* Animation: 4. Hover animations (example - subtle scale) */}
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[15px] lg:p-[20px] rounded-[10px] shadow-md max-w-[500px]'
                  whileHover={{ scale: 1.03, transition: { duration: 0.2 } }}
                >
                  <h5 className='font-pat text-[12px] lg:text-[18px] font-normal text-center mb-[10px] text-primary'>
                    Our Vision
                  </h5>
                  <p className='text-[9px] lg:text-xs font-medium text-center'>
                    To become the most trusted cashback platform connecting
                    millions of shoppers with their favorite brands while
                    pioneering new ways to save money and enhance the digital
                    shopping experience.
                  </p>
                </motion.div>
              </div>
            </motion.div>

            {/* --- Our Journey Section --- */}
            <motion.div
              className='mt-[20px] lg:mt-[40px] mx-auto w-fit'
              variants={sectionItemVariants}
            >
              <h4 className='font-pat text-xs lg:text-[18px] font-normal text-center'>
                Our Journey: Building Indian Cashback
              </h4>
              {/* Animation: 6. Subtle animation for image */}
              <motion.div
                initial='hidden'
                variants={imageVariants}
                viewport={{ once: true, amount: 0.3 }}
                whileInView='visible'
              >
                <Image
                  alt='illustration1'
                  className='w-[135px] h-[127px] lg:w-[342px] lg:h-[323px] mt-[19px] lg:mt-[30px] mx-auto'
                  height={323}
                  src='/img/about/illustration1.png'
                  width={342}
                />
              </motion.div>
              <p className='mt-[12px] lg:mt-[30px] text-[9px] lg:text-xs font-medium text-center max-w-[300px] lg:max-w-[466px]'>
                We started IndianCashback.com in 2013 at God's Own Country,
                Kerala - at NIT Calicut by three college friends who envisioned
                revolutionizing the online shopping experience in India. At that
                time, shopping experiences were limited to nearby physical
                stores with restricted product variety, higher prices, and often
                poor customer service.
              </p>
            </motion.div>

            {/* --- Our Beginning Section --- */}
            <motion.div
              className='flex items-center gap-x-[5px] lg:gap-x-[100px] mt-[35px] lg:mt-[48px] relative'
              variants={sectionItemVariants}
            >
              {/* Animation: 6. Subtle animation for image */}
              <motion.div
                initial='hidden'
                variants={imageVariants}
                viewport={{ once: true, amount: 0.3 }}
                whileInView='visible'
              >
                <Image
                  alt='illustration2'
                  className='w-[123px] h-[118px] lg:w-[325px] lg:h-[311px]'
                  height={311}
                  src='/img/about/illustration2.png'
                  width={325}
                />
              </motion.div>
              <div>
                <h4 className='font-pat text-xs lg:text-[18px] font-normal'>
                  Our Beginning
                </h4>
                <p className='mt-[12px] text-[9px] lg:text-xs font-medium max-w-[300px] lg:max-w-[466px]'>
                  When e-commerce was just beginning to blossom in India, we
                  partnered with pioneering companies like Flipkart, Amazon, and
                  Myntra, offering additional cashback incentives to make online
                  shopping more rewarding. This innovative approach quickly
                  gained traction among savvy shoppers looking to maximize their
                  savings.
                </p>
              </div>
              {/* Animation: 6. Subtle animation for icon (SVG can be animated too) */}
              <motion.div
                className='absolute translate-x-[-50%] left-[50%] bottom-[-50%] lg:bottom-[-30%] w-[150px] lg:w-[528px] h-auto'
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 0.2 }} // Slight delay
                viewport={{ once: true, amount: 0.5 }}
                whileInView={{ opacity: 1, y: 0 }}
              >
                <Image alt='' src={AboutDottedLineSVG1} />
              </motion.div>
            </motion.div>

            {/* --- Reinventing Gift Card Section --- */}
            <motion.div
              className='flex justify-end gap-x-[5px] lg:gap-x-[100px] mt-[90px] lg:mt-[48px] relative'
              variants={sectionItemVariants}
            >
              {/* Animation: 6. Subtle animation for icon (SVG) */}
              <motion.div
                className='absolute translate-x-[-50%] left-[50%] bottom-[-40%] lg:bottom-[-45%] w-[135px] lg:w-[528px]  h-auto'
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                viewport={{ once: true, amount: 0.5 }}
                whileInView={{ opacity: 1, y: 0 }}
              >
                <Image alt='' src={AboutDottedLineSVG2} />
              </motion.div>
              <div className='lg:flex flex-col justify-end '>
                <h4 className='font-pat text-xs lg:text-[18px] font-normal text-right'>
                  Reinventing the Gift Card Experience
                </h4>
                <p className='mt-[12px] text-[9px] lg:text-xs font-medium max-w-[300px] lg:max-w-[466px]'>
                  After establishing ourselves in the cashback industry, we
                  identified similar issues in the gifting sector. Traditional
                  gift-giving often results in unwanted items when the giver
                  doesn't know the recipient's preferences. We solved this by
                  promoting branded gift cards from popular retailers,
                  redeemable both online and in-store, with instant cashback of
                  up to 75%.
                </p>
              </div>
              {/* Animation: 6. Subtle animation for image */}
              <motion.div
                initial='hidden'
                variants={imageVariants}
                viewport={{ once: true, amount: 0.3 }}
                whileInView='visible'
              >
                <Image
                  alt='illustration3'
                  className='w-[126px] h-[116px] lg:w-[296px] lg:h-[271px]'
                  height={271}
                  src='/img/about/illustration3.png'
                  width={296}
                />
              </motion.div>
            </motion.div>

            {/* --- Fintech Innovation Section --- */}
            <motion.div
              className='flex justify-start gap-x-[5px] lg:gap-x-[100px] mt-[75px] lg:mt-[48px]'
              variants={sectionItemVariants}
            >
              {/* Animation: 6. Subtle animation for image */}
              <motion.div
                initial='hidden'
                variants={imageVariants}
                viewport={{ once: true, amount: 0.3 }}
                whileInView='visible'
              >
                <Image
                  alt='illustration4'
                  className='w-[128px] h-[126px] lg:w-[315px] lg:h-[308px]'
                  height={308}
                  src='/img/about/illustration4.png'
                  width={315}
                />
              </motion.div>
              <div className='lg:flex flex-col justify-center '>
                <h4 className='font-pat text-xs lg:text-[18px] font-normal text-left'>
                  Fintech Innovation & Growth
                </h4>
                <p className='mt-[12px] text-[9px] lg:text-xs font-medium max-w-[300px] lg:max-w-[466px]'>
                  Today, we're integrating cashback and gift card services with
                  fintech solutions through our partnership with Yes Bank and
                  the launch of the ICB InstantPay RuPay Card. This innovative
                  card offers an additional 5% cashback on top of existing
                  offers and accelerates cashback approvals, creating
                  unprecedented value for our customers.
                </p>
              </div>
            </motion.div>

            {/* --- Highlight Cards Section --- */}
            {/* Animate highlight cards as they scroll into view */}
            <motion.div
              className='flex justify-center gap-x-[15px] lg:gap-x-[30px] mt-[48px]'
              variants={sectionItemVariants} // Reuse section variant for consistency
            >
              {/* Animation: 6. Subtle animation for icons (handled within AboutHighlightCard if needed, or animate the whole card) */}
              {/* Animation: 4. Hover animation could be added to AboutHighlightCard itself */}
              <AboutHighlightCard
                caption='Users'
                highlightedText='1,00,000+'
                icon={
                  <Image
                    alt='icon'
                    className='w-[8px] h-[12px] lg:w-[18px] lg:h-[27px]'
                    height={27}
                    src='/svg/about-us/person.svg'
                    width={18}
                  />
                }
              />
              <AboutHighlightCard
                caption='Partner Brands'
                highlightedText='600+'
                icon={
                  <Image
                    alt='icon'
                    className='w-[15px] h-[12px] lg:w-[34px] lg:h-[26px]'
                    height={26}
                    src='/svg/about-us/people.svg'
                    width={34}
                  />
                }
              />
              <AboutHighlightCard
                caption='Cashback Given'
                highlightedText='₹ 10+ Cr'
                icon={
                  <Image
                    alt='icon'
                    className='w-[16px] h-[16px] lg:w-[35px] lg:h-[35px]'
                    height={35}
                    src='/svg/about-us/cashback-given.svg'
                    width={35}
                  />
                }
              />
            </motion.div>

            {/* --- Milestone Cards Section --- */}
            {/* Animation: 5. Scroll animations for the milestone cards (staggered container) */}
            <motion.div
              className='flex flex-col gap-[15px] my-[40px]'
              initial='hidden'
              variants={milestoneContainerVariants}
              viewport={{ once: true, amount: 0.2 }} // Trigger when 20% is visible
              whileInView='visible'
            >
              {/* Row 1 */}
              <div className='flex flex-wrap justify-center gap-[15px] lg:gap-[20px]'>
                {/* Individual Milestone Card Animation */}
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[12px] lg:p-[15px] rounded-[10px] shadow-md w-[140px] lg:w-[180px]'
                  variants={milestoneItemVariants}
                >
                  <h5 className='font-pat text-[12px] lg:text-[16px] font-bold text-center text-primary'>
                    2013
                  </h5>
                  <h6 className='text-[10px] lg:text-[12px] font-medium text-center mb-[5px]'>
                    Humble Beginnings
                  </h6>
                  <p className='text-[8px] lg:text-[10px] text-center'>
                    Founded at NIT Calicut by 3 college friends
                  </p>
                </motion.div>
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[12px] lg:p-[15px] rounded-[10px] shadow-md w-[140px] lg:w-[180px]'
                  variants={milestoneItemVariants}
                >
                  <h5 className='font-pat text-[12px] lg:text-[16px] font-bold text-center text-primary'>
                    2014
                  </h5>
                  <h6 className='text-[10px] lg:text-[12px] font-medium text-center mb-[5px]'>
                    Award Winner
                  </h6>
                  <p className='text-[8px] lg:text-[10px] text-center'>
                    Most visited stall at YES Kerala startup summit
                  </p>
                </motion.div>
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[12px] lg:p-[15px] rounded-[10px] shadow-md w-[140px] lg:w-[180px]'
                  variants={milestoneItemVariants}
                >
                  <h5 className='font-pat text-[12px] lg:text-[16px] font-bold text-center text-primary'>
                    2015
                  </h5>
                  <h6 className='text-[10px] lg:text-[12px] font-medium text-center mb-[5px]'>
                    Digital Pioneer
                  </h6>
                  <p className='text-[8px] lg:text-[10px] text-center'>
                    Partnered with Paytm for 100% cashback on recharges
                  </p>
                </motion.div>
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[12px] lg:p-[15px] rounded-[10px] shadow-md w-[140px] lg:w-[180px]'
                  variants={milestoneItemVariants}
                >
                  <h5 className='font-pat text-[12px] lg:text-[16px] font-bold text-center text-primary'>
                    2016
                  </h5>
                  <h6 className='text-[10px] lg:text-[12px] font-medium text-center mb-[5px]'>
                    Silicon Valley Move
                  </h6>
                  <p className='text-[8px] lg:text-[10px] text-center'>
                    Relocated to Bangalore with team of 10
                  </p>
                </motion.div>
              </div>
              {/* Row 2 */}
              <div className='flex flex-wrap justify-center gap-[15px] lg:gap-[20px]'>
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[12px] lg:p-[15px] rounded-[10px] shadow-md w-[140px] lg:w-[180px]'
                  variants={milestoneItemVariants}
                >
                  <h5 className='font-pat text-[12px] lg:text-[16px] font-bold text-center text-primary'>
                    2017
                  </h5>
                  <h6 className='text-[10px] lg:text-[12px] font-medium text-center mb-[5px]'>
                    600+ Partners
                  </h6>
                  <p className='text-[8px] lg:text-[10px] text-center'>
                    ₹1 Crore+ distributed in cashback
                  </p>
                </motion.div>
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[12px] lg:p-[15px] rounded-[10px] shadow-md w-[140px] lg:w-[180px]'
                  variants={milestoneItemVariants}
                >
                  <h5 className='font-pat text-[12px] lg:text-[16px] font-bold text-center text-primary'>
                    2018
                  </h5>
                  <h6 className='text-[10px] lg:text-[12px] font-medium text-center mb-[5px]'>
                    Gift Card Revolution
                  </h6>
                  <p className='text-[8px] lg:text-[10px] text-center'>
                    150+ brand gift cards with instant cashback
                  </p>
                </motion.div>
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[12px] lg:p-[15px] rounded-[10px] shadow-md w-[140px] lg:w-[180px]'
                  variants={milestoneItemVariants}
                >
                  <h5 className='font-pat text-[12px] lg:text-[16px] font-bold text-center text-primary'>
                    2019
                  </h5>
                  <h6 className='text-[10px] lg:text-[12px] font-medium text-center mb-[5px]'>
                    Global Expansion
                  </h6>
                  <p className='text-[8px] lg:text-[10px] text-center'>
                    Expanded to Finland serving European markets
                  </p>
                </motion.div>
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[12px] lg:p-[15px] rounded-[10px] shadow-md w-[140px] lg:w-[180px]'
                  variants={milestoneItemVariants}
                >
                  <h5 className='font-pat text-[12px] lg:text-[16px] font-bold text-center text-primary'>
                    2020
                  </h5>
                  <h6 className='text-[10px] lg:text-[12px] font-medium text-center mb-[5px]'>
                    Shopping Games
                  </h6>
                  <p className='text-[8px] lg:text-[10px] text-center'>
                    Interactive Diwali sales experience
                  </p>
                </motion.div>
              </div>
              {/* Row 3 */}
              <div className='flex flex-wrap justify-center gap-[15px] lg:gap-[20px]'>
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[12px] lg:p-[15px] rounded-[10px] shadow-md w-[140px] lg:w-[180px]'
                  variants={milestoneItemVariants}
                >
                  <h5 className='font-pat text-[12px] lg:text-[16px] font-bold text-center text-primary'>
                    2023
                  </h5>
                  <h6 className='text-[10px] lg:text-[12px] font-medium text-center mb-[5px]'>
                    Fintech Innovation
                  </h6>
                  <p className='text-[8px] lg:text-[10px] text-center'>
                    ICB InstantPay RuPay Card with Yes Bank
                  </p>
                </motion.div>
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[12px] lg:p-[15px] rounded-[10px] shadow-md w-[140px] lg:w-[180px]'
                  variants={milestoneItemVariants}
                >
                  <h5 className='font-pat text-[12px] lg:text-[16px] font-bold text-center text-primary'>
                    2024
                  </h5>
                  <h6 className='text-[10px] lg:text-[12px] font-medium text-center mb-[5px]'>
                    Digital Relaunch
                  </h6>
                  <p className='text-[8px] lg:text-[10px] text-center'>
                    New website and app experience
                  </p>
                </motion.div>
                <motion.div
                  className='bg-white dark:bg-[#2A2D35] p-[12px] lg:p-[15px] rounded-[10px] shadow-md w-[140px] lg:w-[180px]'
                  variants={milestoneItemVariants}
                >
                  <h5 className='font-pat text-[12px] lg:text-[16px] font-bold text-center text-primary'>
                    2025
                  </h5>
                  <h6 className='text-[10px] lg:text-[12px] font-medium text-center mb-[5px]'>
                    Women Leadership
                  </h6>
                  <p className='text-[8px] lg:text-[10px] text-center'>
                    New lady CEO, CTO & 50% women workforce
                  </p>
                </motion.div>
              </div>
            </motion.div>

            {/* --- Start Saving Today CTA Section --- */}
            <motion.div
              className='relative rounded-xl overflow-hidden mb-8 shadow-xl'
              variants={sectionItemVariants}
            >
              <div className='bg-gradient-to-r from-primary to-[#1a1065] p-12 md:p-16 text-center'>
                <h3 className='text-2xl md:text-3xl font-bold text-white mb-4'>
                  Start Saving Today!
                </h3>
                <p className='mb-8 text-white text-lg max-w-2xl mx-auto'>
                  Join thousands of smart shoppers who never miss a cashback
                  opportunity. Sign up now and start earning cashback on your
                  online purchases!
                </p>
                {/* Animation: 4. Hover animation for interactive element (Button) */}
                <motion.div
                  className='inline-block' // Needed for transform to apply correctly
                  transition={{ type: 'spring', stiffness: 300, damping: 15 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <SmartLink
                    className='bg-white hover:bg-gray-100 text-primary font-bold py-3 px-8 rounded-full transition-all shadow-md text-lg hover:shadow-lg block' // Use block if motion.div is inline-block
                    href='https://indiancashback.com/' // Ensure this is the correct signup link
                    target='_blank'
                  >
                    Sign Up Now - It's Free!
                  </SmartLink>
                </motion.div>
              </div>
            </motion.div>

            {/* --- Follow Us Section --- */}
            {/* This section already had animations, keeping them */}
            <motion.div variants={sectionItemVariants}>
              <h4 className='text-[11px] font-pat lg:text-[14px] text-center'>
                Follow Us
              </h4>
              <div className='mt-[21px] lg:mt-[12px] '>
                <motion.div
                  // Removed initial/animate here as the parent section handles entry animation
                  className='flex-center pl-[10px] gap-x-[20px] cursor-pointer'
                >
                  {/* Animation: 4. Hover animations for interactive elements (Social Icons - already implemented) */}
                  <motion.div
                    transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                    whileHover={{ scale: 1.2, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <SmartLink
                      href='https://t.me/indiancashbackofficial'
                      linkType={LinkType.EXTERNAL}
                      target='_blank'
                    >
                      <TelegramSVG className='text-primary w-[26px]' />
                    </SmartLink>
                  </motion.div>
                  <motion.div
                    transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                    whileHover={{ scale: 1.2, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <SmartLink
                      href='https://whatsapp.com/channel/0029VaLW0IoCcW4zFzzcqY23'
                      linkType={LinkType.EXTERNAL}
                      target='_blank'
                    >
                      <WhatsappSVG className='text-primary w-[26px]' />
                    </SmartLink>
                  </motion.div>
                  <motion.div
                    transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                    whileHover={{ scale: 1.2, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <SmartLink
                      href='https://twitter.com/Indian_cashback'
                      linkType={LinkType.EXTERNAL}
                      target='_blank'
                    >
                      <TwitterSVG className='text-primary w-[26px]' />
                    </SmartLink>
                  </motion.div>
                  <motion.div
                    transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                    whileHover={{ scale: 1.2, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <SmartLink
                      href='https://www.instagram.com/indian_cashback/'
                      linkType={LinkType.EXTERNAL}
                      target='_blank'
                    >
                      <InstagramSVG className='text-primary w-[26px]' />
                    </SmartLink>
                  </motion.div>
                  <motion.div
                    transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                    whileHover={{ scale: 1.2, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <SmartLink
                      href='https://www.facebook.com/indiancashback'
                      linkType={LinkType.EXTERNAL}
                      target='_blank'
                    >
                      <FacebookSVG className='text-primary w-[26px]' />
                    </SmartLink>
                  </motion.div>
                  <motion.div
                    transition={{ type: 'spring', stiffness: 400, damping: 10 }}
                    whileHover={{ scale: 1.2, rotate: 5 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <SmartLink
                      // Check if this Youtube link is correct
                      href='https://www.youtube.com/indiancashback'
                      linkType={LinkType.EXTERNAL}
                      target='_blank'
                    >
                      <YoutubeSVG className='text-primary w-[28px]' />
                    </SmartLink>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>
            {/* --- End of Staggered Section Container --- */}
          </motion.div>
        </div>
      </motion.section>
    </>
  );
};

export default IndexClientsAboutUs;

'use client';

import { useAppDispatch } from '@/redux/hooks';
import { 
  setIsUserLogin, 
  setLoginModalOpen, 
  setUserDetails 
} from '@/redux/slices/auth-slice';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { LoadingGif } from '@/app/components/misc/loading-components';

/**
 * This page handles the callback from Google OAuth authentication.
 * It receives the redirect URL and state parameters from the Google OAuth flow.
 * The backend will handle the actual authentication and return user details.
 */
export default function GoogleAuthCallback() {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Get the redirect and state parameters from the URL
        const redirect = searchParams.get('redirect') || '/';
        const state = searchParams.get('state') || '';

        // Call the callback endpoint to complete the authentication
        const response = await fetch(`/api/proxy/auth/google/callback?redirect=${redirect}&state=${state}`, {
          method: 'GET',
          credentials: 'include',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Authentication failed');
        }

        const data = await response.json();

        // Update the auth state with user details
        dispatch(setIsUserLogin(true));
        
        // Set user details if available in the response
        if (data.user) {
          dispatch(setUserDetails({
            name: data.user.name || '',
            email: data.user.email || '',
            mobile: data.user.mobile || '',
            avatar: data.user.avatar || '',
            referralCode: data.user.referralCode || '',
            balance: data.user.balance || 0,
            pendingCount: data.user.pendingCount || 0,
            confirmedCount: data.user.confirmedCount || 0,
            personalInterest: data.user.personalInterest || [],
            sendNotification: data.user.sendNotification || true,
          }));
        }

        // Close the login modal
        dispatch(setLoginModalOpen(false));
        
        // Show success message
        toast.success('Successfully logged in with Google!');
        
        // Redirect to the specified redirect URL or home page
        router.push(redirect);
      } catch (error) {
        console.error('Google auth callback error:', error);
        setError(error instanceof Error ? error.message : 'Authentication failed');
        toast.error('Failed to complete Google authentication. Please try again.');
        
        // Redirect to login page after a short delay
        setTimeout(() => {
          router.push('/');
          dispatch(setLoginModalOpen(true));
        }, 3000);
      } finally {
        setIsProcessing(false);
      }
    };

    handleCallback();
  }, [dispatch, router, searchParams]);

  if (isProcessing) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <LoadingGif className="w-16 h-16" />
        <p className="mt-4 text-lg text-gray-600 dark:text-gray-300">
          Completing authentication...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-red-500 text-xl mb-4">Authentication Error</div>
        <p className="text-gray-600 dark:text-gray-300 mb-6">{error}</p>
        <p className="text-gray-500">Redirecting to home page...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <p className="text-lg text-gray-600 dark:text-gray-300">
        Authentication successful! Redirecting...
      </p>
    </div>
  );
}

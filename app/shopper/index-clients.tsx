'use client';

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Sparkles,
  Send,
  Tag,
  DollarSign,
  Zap,
  ArrowRight,
  Shirt,
  MessageCircle,
  Plus,
  History,
  Trash2,
  Edit3,
  Search,
  X,
} from 'lucide-react';
import CommonContainer from '@/app/components/common-container';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.5,
      staggerChildren: 0.1,
    },
  },
};

const messageVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.3 },
  },
};

// Types
interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  component?:
    | 'greeting'
    | 'clarification'
    | 'deal-card'
    | 'product-carousel'
    | 'gemini-result';
  data?: any;
  timestamp: Date;
}

interface ChatSession {
  id: string;
  name: string;
  messages: Message[];
  createdAt: Date;
  lastActivity: Date;
}

// Local storage helpers
const saveChatsToStorage = (chats: ChatSession[]) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('ai-shopping-buddy-chats', JSON.stringify(chats));
  }
};

const loadChatsFromStorage = (): ChatSession[] => {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('ai-shopping-buddy-chats');
    if (stored) {
      const parsed = JSON.parse(stored);
      return parsed.map((chat: any) => ({
        ...chat,
        createdAt: new Date(chat.createdAt),
        lastActivity: new Date(chat.lastActivity),
        messages: chat.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp),
        })),
      }));
    }
  }
  return [];
};

// --- Standalone Component Section ---

// ChatGroup Component (Moved outside for stability)
const ChatGroup = ({
  title,
  chats: groupChats,
  currentChatId,
  onSelectChat,
  onDeleteChat,
  onRenameChat,
}: {
  title: string;
  chats: ChatSession[];
  currentChatId: string;
  onSelectChat: (chatId: string) => void;
  onDeleteChat: (chatId: string) => void;
  onRenameChat: (chatId: string, newName: string) => void;
}) => {
  const [editingChat, setEditingChat] = useState<string | null>(null);
  const [editName, setEditName] = useState('');

  if (groupChats.length === 0) {
    return null;
  }

  const handleRename = (chatId: string, currentName: string) => {
    setEditingChat(chatId);
    setEditName(currentName);
  };

  const saveRename = () => {
    if (editingChat && editName.trim()) {
      onRenameChat(editingChat, editName.trim());
    }
    setEditingChat(null);
    setEditName('');
  };

  return (
    <div className='mb-4'>
      <h3 className='text-xs font-medium text-content/60 mb-2 px-3'>{title}</h3>
      <div className='space-y-1'>
        {groupChats.map((chat) => (
          <motion.div
            className={`group flex items-center gap-2 p-3 mx-2 rounded-lg cursor-pointer transition-colors ${
              currentChatId === chat.id
                ? 'bg-primary/10 border border-primary/20'
                : 'hover:bg-container'
            }`}
            key={chat.id}
            onClick={() => onSelectChat(chat.id)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className='flex-1 min-w-0'>
              {editingChat === chat.id ? (
                <input
                  autoFocus
                  className='w-full bg-transparent text-sm text-blackWhite outline-none'
                  onBlur={saveRename}
                  onChange={(e) => setEditName(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && saveRename()}
                  value={editName}
                />
              ) : (
                <p className='text-sm text-blackWhite truncate'>{chat.name}</p>
              )}
              <p className='text-xs text-content/60 truncate'>
                {chat.messages.length} messages
              </p>
            </div>
            <div className='opacity-0 group-hover:opacity-100 flex gap-1'>
              <motion.button
                className='p-1 hover:bg-primary/20 rounded'
                onClick={(e) => {
                  e.stopPropagation();
                  handleRename(chat.id, chat.name);
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Edit3 className='w-3 h-3 text-content/60' />
              </motion.button>
              <motion.button
                className='p-1 hover:bg-red-500/20 rounded'
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteChat(chat.id);
                }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <Trash2 className='w-3 h-3 text-red-500' />
              </motion.button>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

// Chat History Sidebar Component
const ChatHistorySidebar = ({
  isOpen,
  onClose,
  chats,
  currentChatId,
  onSelectChat,
  onNewChat,
  onDeleteChat,
  onRenameChat,
}: {
  isOpen: boolean;
  onClose: () => void;
  chats: ChatSession[];
  currentChatId: string;
  onSelectChat: (chatId: string) => void;
  onNewChat: () => void;
  onDeleteChat: (chatId: string) => void;
  onRenameChat: (chatId: string, newName: string) => void;
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredChats = chats.filter((chat) =>
    chat.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const groupedChats = useMemo(() => {
    const groups = {
      today: [] as ChatSession[],
      yesterday: [] as ChatSession[],
      lastWeek: [] as ChatSession[],
      older: [] as ChatSession[],
    };

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const lastWeek = new Date(today);
    lastWeek.setDate(lastWeek.getDate() - 7);

    filteredChats.forEach((chat) => {
      const chatDate = new Date(
        chat.lastActivity.getFullYear(),
        chat.lastActivity.getMonth(),
        chat.lastActivity.getDate()
      );

      if (chatDate.getTime() === today.getTime()) {
        groups.today.push(chat);
      } else if (chatDate.getTime() === yesterday.getTime()) {
        groups.yesterday.push(chat);
      } else if (chatDate > lastWeek) {
        groups.lastWeek.push(chat);
      } else {
        groups.older.push(chat);
      }
    });
    return groups;
  }, [filteredChats]);

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Overlay */}
          <motion.div
            animate={{ opacity: 1 }}
            className='fixed inset-0 bg-black/50 z-[998] lg:hidden'
            exit={{ opacity: 0 }}
            initial={{ opacity: 0 }}
            onClick={onClose}
          />

          {/* Sidebar */}
          <motion.div
            animate={{ x: 0 }}
            className='fixed left-0 top-[65px] lg:top-[104px] bottom-0 w-80 bg-mainCard border-r border-white dark:border-[#353943] z-[998] flex flex-col'
            exit={{ x: -320 }}
            initial={{ x: -320 }}
            transition={{ type: 'spring', damping: 20 }}
          >
            {/* Header */}
            <div className='p-4 border-b border-white dark:border-[#353943]'>
              <div className='flex items-center justify-between mb-4'>
                <h2 className='text-lg font-pat font-bold text-blackWhite'>
                  Chat History
                </h2>
                <motion.button
                  className='p-2 hover:bg-container rounded-lg'
                  onClick={onClose}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <X className='w-5 h-5 text-content' />
                </motion.button>
              </div>

              {/* New Chat Button */}
              <motion.button
                className='w-full flex items-center gap-3 p-3 bg-gradient-to-r from-[#7264db] to-[#4335bd] text-white rounded-lg font-medium'
                onClick={onNewChat}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Plus className='w-4 h-4' />
                New Chat
              </motion.button>

              {/* Search */}
              <div className='relative mt-3'>
                <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-content/60' />
                <input
                  className='w-full pl-10 pr-4 py-2 bg-container border border-white dark:border-[#353943] rounded-lg text-sm text-blackWhite placeholder-content/60 outline-none focus:border-primary/50'
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder='Search chats...'
                  type='text'
                  value={searchTerm}
                />
              </div>
            </div>

            {/* Chat List */}
            <div className='flex-1 overflow-y-auto customScrollbar'>
              <div className='py-2'>
                <ChatGroup
                  chats={groupedChats.today}
                  currentChatId={currentChatId}
                  onDeleteChat={onDeleteChat}
                  onRenameChat={onRenameChat}
                  onSelectChat={onSelectChat}
                  title='Today'
                />
                <ChatGroup
                  chats={groupedChats.yesterday}
                  currentChatId={currentChatId}
                  onDeleteChat={onDeleteChat}
                  onRenameChat={onRenameChat}
                  onSelectChat={onSelectChat}
                  title='Yesterday'
                />
                <ChatGroup
                  chats={groupedChats.lastWeek}
                  currentChatId={currentChatId}
                  onDeleteChat={onDeleteChat}
                  onRenameChat={onRenameChat}
                  onSelectChat={onSelectChat}
                  title='Last 7 days'
                />
                <ChatGroup
                  chats={groupedChats.older}
                  currentChatId={currentChatId}
                  onDeleteChat={onDeleteChat}
                  onRenameChat={onRenameChat}
                  onSelectChat={onSelectChat}
                  title='Older'
                />

                {filteredChats.length === 0 && (
                  <div className='flex flex-col items-center justify-center py-8 text-center'>
                    <History className='w-12 h-12 text-content/30 mb-3' />
                    <p className='text-content/60 text-sm'>
                      {searchTerm ? 'No chats found' : 'No chat history yet'}
                    </p>
                    <p className='text-content/40 text-xs mt-1'>
                      {searchTerm
                        ? 'Try a different search term'
                        : 'Start a new conversation!'}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

// Chat Header Component
const ChatHeader = ({
  onNewChat,
  onShowHistory,
  currentChatName,
}: {
  onNewChat: () => void;
  onShowHistory: () => void;
  currentChatName: string;
}) => {
  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className='bg-gradient-to-r from-[#7264db] to-[#4335bd] text-white p-4 rounded-t-[10px] flex items-center justify-between shadow-lg'
      initial={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.5 }}
    >
      <div className='flex items-center gap-3'>
        <motion.div
          animate={{ rotate: [0, 10, -10, 0] }}
          className='bg-white/10 p-2 rounded-lg backdrop-blur-sm'
          transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
        >
          <Sparkles className='w-5 h-5' />
        </motion.div>
        <div>
          <h1 className='text-lg font-pat font-bold'>
            {currentChatName || 'AI Shopping Buddy'}
          </h1>
          <div className='flex items-center gap-2'>
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              className='w-2 h-2 bg-green-400 rounded-full'
              transition={{ duration: 1.5, repeat: Infinity }}
            />
            <p className='text-white/80 text-xs'>
              Online & ready to help you save!
            </p>
          </div>
        </div>
      </div>

      <div className='flex items-center gap-2'>
        <motion.button
          className='p-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors backdrop-blur-sm'
          onClick={onShowHistory}
          title='Chat History'
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <MessageCircle className='w-4 h-4' />
        </motion.button>
        <motion.button
          className='px-3 py-2 bg-white/10 rounded-lg hover:bg-white/20 transition-colors backdrop-blur-sm text-xs font-medium'
          onClick={onNewChat}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          New Chat
        </motion.button>
      </div>
    </motion.div>
  );
};

// Quick Reply Button Component
const QuickReplyButton = ({
  text,
  onClick,
  variant = 'primary',
}: {
  text: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}) => {
  const baseClasses =
    'px-4 py-2 rounded-full text-sm font-medium transition-all duration-300';
  const variants = {
    primary:
      'bg-primary/10 text-primary hover:bg-primary/20 border border-primary/20',
    secondary:
      'bg-container text-blackWhite hover:bg-primary/10 border border-container',
  };

  return (
    <motion.button
      className={`${baseClasses} ${variants[variant]}`}
      onClick={onClick}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      {text}
    </motion.button>
  );
};

// Greeting Component
const GreetingComponent = ({
  onQuickReply,
}: {
  onQuickReply: (message: string) => void;
}) => {
  const quickReplies = [
    'Best Mobile Offers',
    'Deals on running shoes',
    'Flipkart Coupons',
  ];

  return (
    <motion.div
      animate={{ opacity: 1 }}
      className='space-y-4'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className='flex items-center gap-2 mb-4'>
        <MessageCircle className='w-5 h-5 text-primary' />
        <p className='text-blackWhite font-medium'>
          Welcome to AI Shopping Buddy! 🛍️
        </p>
      </div>
      <p className='text-content text-sm leading-relaxed'>
        I'm here to help you find the best deals and cashback offers. How can I
        help you save money today?
      </p>
      <div className='flex flex-wrap gap-2 mt-4'>
        {quickReplies.map((reply, index) => (
          <QuickReplyButton
            key={index}
            onClick={() => onQuickReply(reply)}
            text={reply}
          />
        ))}
      </div>
    </motion.div>
  );
};

// Clarification Component
const ClarificationComponent = ({
  onQuickReply,
}: {
  onQuickReply: (message: string) => void;
}) => {
  const brands = ['Nike', 'Adidas', 'Puma', 'Reebok'];

  return (
    <motion.div
      animate={{ opacity: 1 }}
      className='space-y-4'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <p className='text-blackWhite font-medium'>Which brand interests you?</p>
      <div className='flex flex-wrap gap-2'>
        {brands.map((brand, index) => (
          <QuickReplyButton
            key={index}
            onClick={() => onQuickReply(`Show me ${brand} deals`)}
            text={brand}
            variant='secondary'
          />
        ))}
      </div>
    </motion.div>
  );
};

// Deal Store Card Component
const DealStoreCard = ({ deal, index }: { deal: any; index: number }) => {
  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className='border border-white dark:border-black rounded-lg p-4 bg-mainCard shadow-sm'
      initial={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      whileHover={{ scale: 1.02, boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
    >
      <h4 className='font-pat font-bold text-lg mb-3 text-blackWhite'>
        {deal.store}
      </h4>
      <div className='space-y-2 text-sm'>
        <div className='flex items-center gap-2'>
          <Tag className='w-4 h-4 text-content' />
          <span className='text-content'>Price: {deal.price}</span>
        </div>
        <div className='flex items-center gap-2'>
          <DollarSign className='w-4 h-4 text-green-500' />
          <span className='text-content'>Cashback: {deal.cashback}</span>
        </div>
        <div className='flex items-center gap-2'>
          <Zap className='w-4 h-4 text-orange-500' />
          <span className='text-content'>Bank Offer: {deal.bankOffer}</span>
        </div>
        <div className='font-bold text-primary text-base mt-3'>
          Effective Price: {deal.effectivePrice}
        </div>
      </div>
      <motion.button
        className='w-full mt-4 bg-primary text-white py-2 px-4 rounded-lg font-medium flex items-center justify-center gap-2 transition-colors hover:bg-primaryDark'
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        Shop on {deal.store}
        <ArrowRight className='w-4 h-4' />
      </motion.button>
    </motion.div>
  );
};

// Deal Card Component
const DealCardComponent = ({
  productName = 'iPhone 15 Pro',
}: {
  productName?: string;
}) => {
  const deals = [
    {
      store: 'Amazon',
      price: '₹1,34,900',
      cashback: '₹2,698',
      bankOffer: '₹5,000',
      effectivePrice: '₹1,27,202',
    },
    {
      store: 'Croma',
      price: '₹1,34,900',
      cashback: '₹1,349',
      bankOffer: '₹3,000',
      effectivePrice: '₹1,30,551',
    },
  ];

  return (
    <motion.div
      animate={{ opacity: 1 }}
      className='space-y-4'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <h3 className='font-pat font-bold text-blackWhite text-lg'>
        {productName} - Best Deals
      </h3>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        {deals.map((deal, index) => (
          <DealStoreCard deal={deal} index={index} key={index} />
        ))}
      </div>
      <motion.button
        className='w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-3 px-4 rounded-lg font-medium transition-all duration-300'
        whileHover={{
          scale: 1.02,
          boxShadow: '0px 8px 30px rgba(147, 51, 234, 0.3)',
        }}
        whileTap={{ scale: 0.98 }}
      >
        ✨ Compare All Deals
      </motion.button>
    </motion.div>
  );
};

// Product Card Component
const ProductCard = ({ product, index }: { product: any; index: number }) => {
  return (
    <motion.div
      animate={{ opacity: 1, x: 0 }}
      className='flex-shrink-0 w-48 bg-mainCard border border-white dark:border-black rounded-lg p-3 shadow-sm'
      initial={{ opacity: 0, x: 20 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      whileHover={{ scale: 1.05, boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
    >
      <div className='w-full h-32 bg-container rounded-lg mb-3 flex items-center justify-center'>
        <span className='text-content text-sm font-medium'>Product Image</span>
      </div>
      <h4 className='font-medium text-sm mb-1 text-blackWhite'>
        {product.name}
      </h4>
      <p className='text-primary font-bold'>{product.price}</p>
      <p className='text-green-600 text-xs'>Cashback: {product.cashback}</p>
      <motion.button
        className='w-full mt-2 bg-blackWhite text-white dark:text-blackWhite py-1 px-2 rounded text-sm font-medium transition-colors hover:bg-primaryDark'
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        View Deal
      </motion.button>
    </motion.div>
  );
};

// Product Carousel Component
const ProductCarouselComponent = () => {
  const products = [
    {
      name: 'Nike Air Max',
      price: '₹8,999',
      cashback: '₹450',
    },
    {
      name: 'Adidas Ultraboost',
      price: '₹12,999',
      cashback: '₹650',
    },
    {
      name: 'Puma RS-X',
      price: '₹6,999',
      cashback: '₹350',
    },
    {
      name: 'Reebok Classic',
      price: '₹4,999',
      cashback: '₹250',
    },
  ];

  return (
    <motion.div
      animate={{ opacity: 1 }}
      className='space-y-4'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5 }}
    >
      <h3 className='font-pat font-bold text-blackWhite'>
        Running Shoes You Might Like
      </h3>
      <div className='flex gap-4 overflow-x-auto pb-2 customScrollbar'>
        {products.map((product, index) => (
          <ProductCard index={index} key={index} product={product} />
        ))}
      </div>
      <motion.button
        className='bg-gradient-to-r from-pink-500 to-purple-500 text-white py-2 px-4 rounded-lg font-medium flex items-center gap-2 transition-all duration-300'
        whileHover={{
          scale: 1.02,
          boxShadow: '0px 8px 30px rgba(236, 72, 153, 0.3)',
        }}
        whileTap={{ scale: 0.98 }}
      >
        <Shirt className='w-4 h-4' />✨ Suggest Outfit
      </motion.button>
    </motion.div>
  );
};

// Gemini Result Component
const GeminiResultComponent = ({ query }: { query: string }) => {
  return (
    <motion.div
      animate={{ opacity: 1, scale: 1 }}
      className='bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 p-4 rounded-lg border border-purple-200 dark:border-purple-700'
      initial={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.5 }}
    >
      <h3 className='font-pat font-bold text-purple-800 dark:text-purple-300 mb-2 flex items-center gap-2'>
        <Sparkles className='w-4 h-4' />
        AI Recommendation
      </h3>
      <p className='text-purple-700 dark:text-purple-200 text-sm leading-relaxed'>
        Based on your search for "{query}", I found some great deals! Here are
        personalized recommendations considering your preferences, current
        market trends, and the best cashback offers available. These suggestions
        are tailored to give you maximum savings while ensuring quality
        products.
      </p>
    </motion.div>
  );
};

// Typing Indicator Component
const TypingIndicator = () => {
  return (
    <motion.div
      animate={{ opacity: 1, scale: 1 }}
      className='flex items-center space-x-2 p-4 bg-container rounded-lg max-w-fit'
      initial={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.3 }}
    >
      <div className='flex space-x-1'>
        {[0, 1, 2].map((i) => (
          <motion.div
            animate={{ y: [0, -8, 0] }}
            className='w-2 h-2 bg-primary rounded-full'
            key={i}
            transition={{
              duration: 0.6,
              repeat: Infinity,
              delay: i * 0.1,
            }}
          />
        ))}
      </div>
      <span className='text-content text-sm font-medium'>AI is typing...</span>
    </motion.div>
  );
};

// Message Bubble Component
const MessageBubble = ({
  message,
  renderMessageComponent,
}: {
  message: Message;
  renderMessageComponent: (message: Message) => React.ReactNode;
}) => {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  };

  return (
    <motion.div
      animate='visible'
      className={`flex ${
        message.type === 'user' ? 'justify-end' : 'justify-start'
      } mb-1`}
      initial='hidden'
      variants={messageVariants}
    >
      <div
        className={`flex flex-col ${
          message.type === 'user' ? 'items-end' : 'items-start'
        } max-w-[85%]`}
      >
        {/* Avatar and message container */}
        <div
          className={`flex items-end gap-2 ${
            message.type === 'user' ? 'flex-row-reverse' : ''
          }`}
        >
          {/* Avatar */}
          {message.type === 'ai' && (
            <motion.div
              animate={{ scale: 1 }}
              className='w-8 h-8 rounded-full bg-gradient-to-r from-[#7264db] to-[#4335bd] flex items-center justify-center flex-shrink-0 shadow-md'
              initial={{ scale: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Sparkles className='w-4 h-4 text-white' />
            </motion.div>
          )}

          {/* Message bubble */}
          <motion.div
            className={`p-4 rounded-2xl shadow-lg ${
              message.type === 'user'
                ? 'bg-gradient-to-r from-[#7264db] to-[#4335bd] text-white rounded-br-md'
                : 'bg-mainCard border border-white dark:border-[#353943] rounded-bl-md'
            }`}
            transition={{ duration: 0.2 }}
            whileHover={{ scale: 1.02 }}
          >
            {message.content && (
              <p
                className={`${
                  message.type === 'user' ? 'text-white' : 'text-blackWhite'
                } leading-relaxed text-sm`}
              >
                {message.content}
              </p>
            )}
            {message.component && renderMessageComponent(message)}
          </motion.div>
        </div>

        {/* Timestamp */}
        <span
          className={`text-xs text-content/60 mt-1 px-2 ${
            message.type === 'user' ? 'text-right' : 'text-left'
          }`}
        >
          {formatTime(message.timestamp)}
        </span>
      </div>
    </motion.div>
  );
};

// Chat Input Component
const ChatInput = ({
  inputText,
  setInputText,
  isTyping,
  onSendMessage,
}: {
  inputText: string;
  setInputText: (text: string) => void;
  isTyping: boolean;
  onSendMessage: () => void;
}) => {
  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className='p-4 bg-gradient-to-r from-container to-body border-t border-white dark:border-[#353943] rounded-b-[10px]'
      initial={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      {/* Quick suggestions */}
      <div className='mb-3 flex flex-wrap gap-2'>
        {!inputText && (
          <>
            <motion.button
              animate={{ opacity: 1, y: 0 }}
              className='px-3 py-1 bg-mainCard border border-white dark:border-[#353943] rounded-full text-xs text-content hover:bg-primary/10 transition-colors'
              initial={{ opacity: 0, y: 10 }}
              onClick={() => setInputText('Best mobile deals')}
              transition={{ delay: 0.3 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              📱 Mobile Deals
            </motion.button>
            <motion.button
              animate={{ opacity: 1, y: 0 }}
              className='px-3 py-1 bg-mainCard border border-white dark:border-[#353943] rounded-full text-xs text-content hover:bg-primary/10 transition-colors'
              initial={{ opacity: 0, y: 10 }}
              onClick={() => setInputText('Flipkart offers')}
              transition={{ delay: 0.4 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              🛒 Flipkart Offers
            </motion.button>
            <motion.button
              animate={{ opacity: 1, y: 0 }}
              className='px-3 py-1 bg-mainCard border border-white dark:border-[#353943] rounded-full text-xs text-content hover:bg-primary/10 transition-colors'
              initial={{ opacity: 0, y: 10 }}
              onClick={() => setInputText('Running shoes under 5000')}
              transition={{ delay: 0.5 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              👟 Sports Shoes
            </motion.button>
          </>
        )}
      </div>

      {/* Input area */}
      <div className='flex gap-3 bg-mainCard rounded-2xl p-3 border border-white dark:border-[#353943] shadow-lg'>
        <input
          className='flex-1 px-3 py-2 outline-none text-blackWhite bg-transparent placeholder-content/50 text-sm'
          disabled={isTyping}
          onChange={(e) => setInputText(e.target.value)}
          onKeyPress={(e) =>
            e.key === 'Enter' && !e.shiftKey && onSendMessage()
          }
          placeholder='Ask about products, deals, or cashback offers...'
          type='text'
          value={inputText}
        />
        <motion.button
          className='bg-gradient-to-r from-[#7264db] to-[#4335bd] text-white p-3 rounded-xl disabled:from-content/30 disabled:to-content/30 disabled:cursor-not-allowed transition-all duration-300 shadow-md'
          disabled={!inputText.trim() || isTyping}
          onClick={onSendMessage}
          whileHover={{
            scale: 1.05,
            boxShadow: '0 8px 25px rgba(114, 100, 219, 0.3)',
          }}
          whileTap={{ scale: 0.95 }}
        >
          {isTyping ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            >
              <Sparkles className='w-5 h-5' />
            </motion.div>
          ) : (
            <Send className='w-5 h-5' />
          )}
        </motion.button>
      </div>

      {/* Status indicator */}
      {isTyping && (
        <motion.p
          animate={{ opacity: 1 }}
          className='text-xs text-content/60 mt-2 flex items-center gap-2'
          initial={{ opacity: 0 }}
        >
          <motion.div
            animate={{ scale: [1, 1.2, 1] }}
            className='w-2 h-2 bg-primary rounded-full'
            transition={{ duration: 1, repeat: Infinity }}
          />
          AI is thinking...
        </motion.p>
      )}
    </motion.div>
  );
};

// Main AI Shopping Buddy Component
const AIShoppingBuddy = () => {
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [currentChatId, setCurrentChatId] = useState<string>('');
  const [showHistory, setShowHistory] = useState(false);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize chat sessions from storage
  useEffect(() => {
    const stored = loadChatsFromStorage();
    if (stored.length > 0) {
      setChatSessions(stored);
      setCurrentChatId(stored[0].id);
    } else {
      // Create initial chat
      const initialChat: ChatSession = {
        id: Date.now().toString(),
        name: 'New Chat',
        messages: [
          {
            id: '1',
            type: 'ai',
            content: '',
            component: 'greeting',
            timestamp: new Date(),
          },
        ],
        createdAt: new Date(),
        lastActivity: new Date(),
      };
      setChatSessions([initialChat]);
      setCurrentChatId(initialChat.id);
    }
  }, []);

  // Save to storage whenever sessions change
  useEffect(() => {
    if (chatSessions.length > 0) {
      saveChatsToStorage(chatSessions);
    }
  }, [chatSessions]);

  const currentChat = chatSessions.find((chat) => chat.id === currentChatId);

  // Memoize messages to prevent unnecessary re-renders
  const messages = useMemo(() => currentChat?.messages || [], [currentChat]);

  // Auto-scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  // Handle sending messages
  const handleSendMessage = async (messageText?: string) => {
    const textToSend = messageText || inputText.trim();
    if (!textToSend) {
      return;
    }

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: textToSend,
      timestamp: new Date(),
    };

    // Update current chat with new message
    setChatSessions((prev) =>
      prev.map((chat) =>
        chat.id === currentChatId
          ? {
              ...chat,
              messages: [...chat.messages, userMessage],
              lastActivity: new Date(),
              // Auto-name chat based on first user message
              name:
                chat.messages.length === 1 && chat.name === 'New Chat'
                  ? textToSend.slice(0, 30) +
                    (textToSend.length > 30 ? '...' : '')
                  : chat.name,
            }
          : chat
      )
    );
    setInputText('');
    setIsTyping(true);

    // Simulate AI response delay
    setTimeout(() => {
      const aiResponse = generateAIResponse(textToSend);
      setChatSessions((prev) =>
        prev.map((chat) =>
          chat.id === currentChatId
            ? {
                ...chat,
                messages: [...chat.messages, aiResponse],
                lastActivity: new Date(),
              }
            : chat
        )
      );
      setIsTyping(false);
    }, 1500);
  };

  // Generate AI response based on keywords
  const generateAIResponse = (userInput: string): Message => {
    const input = userInput.toLowerCase();

    if (
      input.includes('iphone') ||
      input.includes('mobile') ||
      input.includes('phone')
    ) {
      return {
        id: Date.now().toString(),
        type: 'ai',
        content: '',
        component: 'deal-card',
        data: { productName: 'iPhone 15 Pro' },
        timestamp: new Date(),
      };
    }

    if (
      input.includes('shoes') ||
      input.includes('nike') ||
      input.includes('adidas') ||
      input.includes('running')
    ) {
      if (
        input.includes('nike') ||
        input.includes('adidas') ||
        input.includes('puma') ||
        input.includes('reebok')
      ) {
        return {
          id: Date.now().toString(),
          type: 'ai',
          content: '',
          component: 'product-carousel',
          timestamp: new Date(),
        };
      } else {
        return {
          id: Date.now().toString(),
          type: 'ai',
          content: '',
          component: 'clarification',
          timestamp: new Date(),
        };
      }
    }

    if (
      input.includes('flipkart') ||
      input.includes('coupon') ||
      input.includes('deal')
    ) {
      return {
        id: Date.now().toString(),
        type: 'ai',
        content: '',
        component: 'gemini-result',
        data: { query: userInput },
        timestamp: new Date(),
      };
    }

    // Default response
    return {
      id: Date.now().toString(),
      type: 'ai',
      content:
        'I can help you find great deals on electronics, fashion, and more! Try asking about specific products like iPhones, running shoes, or popular stores like Flipkart.',
      component: 'gemini-result',
      data: { query: userInput },
      timestamp: new Date(),
    };
  };

  // Handle quick reply clicks
  const handleQuickReply = (message: string) => {
    handleSendMessage(message);
  };

  // Chat management functions
  const handleNewChat = () => {
    const newChat: ChatSession = {
      id: Date.now().toString(),
      name: 'New Chat',
      messages: [
        {
          id: '1',
          type: 'ai',
          content: '',
          component: 'greeting',
          timestamp: new Date(),
        },
      ],
      createdAt: new Date(),
      lastActivity: new Date(),
    };
    setChatSessions((prev) => [newChat, ...prev]);
    setCurrentChatId(newChat.id);
    setShowHistory(false);
  };

  const handleSelectChat = (chatId: string) => {
    setCurrentChatId(chatId);
    setShowHistory(false);
  };

  const handleDeleteChat = (chatId: string) => {
    setChatSessions((prev) => {
      const filtered = prev.filter((chat) => chat.id !== chatId);
      if (filtered.length === 0) {
        // Create a new chat if all are deleted
        const newChat: ChatSession = {
          id: Date.now().toString(),
          name: 'New Chat',
          messages: [
            {
              id: '1',
              type: 'ai',
              content: '',
              component: 'greeting',
              timestamp: new Date(),
            },
          ],
          createdAt: new Date(),
          lastActivity: new Date(),
        };
        setCurrentChatId(newChat.id);
        return [newChat];
      }
      if (currentChatId === chatId) {
        setCurrentChatId(filtered[0].id);
      }
      return filtered;
    });
  };

  const handleRenameChat = (chatId: string, newName: string) => {
    setChatSessions((prev) =>
      prev.map((chat) =>
        chat.id === chatId ? { ...chat, name: newName } : chat
      )
    );
  };

  // Render message component
  const renderMessageComponent = (message: Message) => {
    switch (message.component) {
      case 'greeting':
        return <GreetingComponent onQuickReply={handleQuickReply} />;
      case 'clarification':
        return <ClarificationComponent onQuickReply={handleQuickReply} />;
      case 'deal-card':
        return <DealCardComponent productName={message.data?.productName} />;
      case 'product-carousel':
        return <ProductCarouselComponent />;
      case 'gemini-result':
        return (
          <GeminiResultComponent
            query={message.data?.query || message.content}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <ChatHistorySidebar
        chats={chatSessions}
        currentChatId={currentChatId}
        isOpen={showHistory}
        onClose={() => setShowHistory(false)}
        onDeleteChat={handleDeleteChat}
        onNewChat={handleNewChat}
        onRenameChat={handleRenameChat}
        onSelectChat={handleSelectChat}
      />
      {/* Mobile spacing for header */}
      <div className='h-[65px] lg:h-[104px]' />

      <motion.div
        animate='visible'
        className='header-container px-2 pb-4'
        initial='hidden'
        variants={containerVariants}
      >
        <CommonContainer className='!mt-0 !mx-0 min-h-[calc(100vh-140px)] lg:min-h-[calc(100vh-180px)] flex flex-col overflow-hidden'>
          <ChatHeader
            currentChatName={currentChat?.name || 'AI Shopping Buddy'}
            onNewChat={handleNewChat}
            onShowHistory={() => setShowHistory(true)}
          />

          {/* Messages Area */}
          <div className='flex-1 overflow-y-auto p-4 space-y-4 bg-body customScrollbar'>
            <motion.div
              animate='visible'
              className='space-y-4'
              initial='hidden'
              variants={containerVariants}
            >
              {messages.map((message) => (
                <MessageBubble
                  key={message.id}
                  message={message}
                  renderMessageComponent={renderMessageComponent}
                />
              ))}

              {/* Typing Indicator */}
              {isTyping && (
                <motion.div
                  animate={{ opacity: 1 }}
                  className='flex justify-start'
                  initial={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <TypingIndicator />
                </motion.div>
              )}

              <div ref={messagesEndRef} />
            </motion.div>
          </div>

          {/* Input Area */}
          <ChatInput
            inputText={inputText}
            isTyping={isTyping}
            onSendMessage={() => handleSendMessage()}
            setInputText={setInputText}
          />
        </CommonContainer>
      </motion.div>
    </>
  );
};

export default AIShoppingBuddy;

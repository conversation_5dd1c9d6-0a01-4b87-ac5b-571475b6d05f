'use client';
import React, { useState } from 'react';
import CommonHeader from '../components/headers/common-header';
import CommonToolbar from '../components/common-toolbar';
import CommonFilterMobile from '../components/misc/common-filter-mobile';
import PillButton from '../components/atoms/pills';

const IndexClientsNotifications = () => {
  const [isShowFilterModal, setShowFilterModal] = useState(false);

  return (
    <>
      <CommonHeader
        headline='Notifications'
        subHeading={
          <>
            <span>All Transaction</span>
            <span className='ml-[6px] text-[7px] sm:text-[8px] font-nexa'>
              25/1254
            </span>
          </>
        }
      />
      <div
        className='bg-[#E1E2E4] dark:bg-container'
        style={{ boxShadow: '0px 4px 6px 0px rgba(0, 0, 0, 0.13)' }}
      >
        <CommonToolbar
          onClickFilterBtn={() => setShowFilterModal(!isShowFilterModal)}
          rootClassName='shadow-none !bg-transparent'
        />
        <div
          className='flex gap-x-[8px] ml-[16px] pt-[5px] pb-[15px]'
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <PillButton
            className='border-[1px] border-primary'
            isSelected
            text='All'
          />
          <PillButton className='border-[1px] border-primary' text='Read' />
          <PillButton className='border-[1px] border-primary' text='Unread' />
        </div>
      </div>

      <section className='mx-[6px] px-[5x] bg-container'>
        <div />
      </section>

      <CommonFilterMobile
        filterProps={[{ filter: 'percentage' }]}
        isShowFilterModal={isShowFilterModal}
        setShowFilterModal={() => setShowFilterModal(!isShowFilterModal)}
      />
    </>
  );
};

export default IndexClientsNotifications;

'use client';
import React, { useState } from 'react';
import CommonHeader from '../components/headers/common-header';
import StoreByCBCard from '../components/landing/stores-cb-percentage/store-by-cb-card';
import AsideStoreByCB from './aside-store-by-cashback';
import BreadcrumbSaveShare from '../components/atoms/breadcrumb-container';
import CommonToolbar, {
  storeTypeSortItems,
} from '../components/common-toolbar';
import { ConfigProvider, Pagination, theme } from 'antd';
import { useTheme } from 'next-themes';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import {
  useCreateMultiQueryString,
  useResponsiveGrid,
} from '@/utils/custom-hooks';
import CommonFilterMobile from '../components/misc/common-filter-mobile';
import type { GetAllStoresResponse } from '@/services/api/data-contracts';
import EnhancedNoData from '../components/enhanced-no-data';

export const slidingBtnStoresPage = [
  { title: '100% CB', value: '100%' },
  { title: '50% CB', value: '50%' },
  { title: '25% CB ', value: '25%' },
];

const IndexAllStoresClients = ({ data }: { data: GetAllStoresResponse }) => {
  const [isShowFilterModal, setShowFilterModal] = useState(false);
  const { resolvedTheme } = useTheme();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);

  // Use custom responsive grid hook
  const { getGridProps } = useResponsiveGrid();

  return (
    <>
      <CommonHeader headline='Store By CB Percentage' />
      <div className='flex h-full lg:min-h-[calc(100svh-112px)] lg:m-[8px] !mb-0 gap-x-[8px]'>
        <AsideStoreByCB />
        <div className='bg-container w-full'>
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'Store By Percentage' },
            ]}
          />
          <div className='sticky top-[64px] lg:top-[103px] bg-container z-[9]'>
            {/* <div className='pt-[26px] lg:bg-white lg:dark:bg-[#191a1c] lg:mt-0 lg:py-[12px] lg:shadow lg:hidden'>
              <SlidingButton
                buttonDetails={slidingBtnStoresPage}
                defaultSelectedBtn={1}
                onChange={() => console.log('')}
                uniqueId='storeByCB'
              />
            </div> */}
            <CommonToolbar
              customSortItems={storeTypeSortItems}
              initialSort={storeTypeSortItems.find(
                (item) => item.label === 'Popular'
              )}
              onClickFilterBtn={() => setShowFilterModal(!isShowFilterModal)}
              rootClassName='bg-transparent lg:bg-[#E1E2E4] lg:dark:bg-[#2D313A] mt-[15px] lg:mt-0'
            />
          </div>

          {/* ---------all stores cards--------- */}
          <section className='px-[8px] lg:px-[22px] pb-[30px]'>
            <h6 className='text-xs font-pat font-normal text-blackWhite mt-[14px] mb-[16px] hidden lg:block'>
              Results{' '}
              <span className='font-nexa font-[800]'>
                ({data?.pagination?.total ?? 0})
              </span>
            </h6>
            {data?.stores.length ? (
              <div {...getGridProps()}>
                {data.stores.map((item) => (
                  <StoreByCBCard
                    bgColor={item.bgColor}
                    caption={item.caption}
                    key={item.uid}
                    saved={item.saved}
                    src={item.imageUrl}
                    storeName={item.storeName}
                    uid={item.uid}
                  />
                ))}
              </div>
            ) : (
              <EnhancedNoData
                customHeight='min-h-[400px]'
                message='No stores available at the moment. Please check back later!'
                showHomeLink={false}
              />
            )}
            {data.pagination.pageSize > 0 && (
              <div className='flex-center w-full my-[50px]'>
                <ConfigProvider
                  theme={{
                    algorithm:
                      resolvedTheme === 'dark'
                        ? theme.darkAlgorithm
                        : theme.defaultAlgorithm,
                  }}
                >
                  <Pagination
                    defaultCurrent={1}
                    defaultPageSize={18}
                    onChange={(pageNumber, pageSize) =>
                      replace(
                        pathname +
                          '?' +
                          createMultiQueryString([
                            { name: 'page', value: pageNumber.toString() },
                            { name: 'pageSize', value: pageSize.toString() },
                          ])
                      )
                    }
                    total={data.pagination.total}
                  />
                </ConfigProvider>
              </div>
            )}
          </section>
        </div>
      </div>
      <CommonFilterMobile
        filterProps={[{ filter: 'percentage' }]}
        isShowFilterModal={isShowFilterModal}
        setShowFilterModal={() => setShowFilterModal(!isShowFilterModal)}
      />
    </>
  );
};

export default IndexAllStoresClients;

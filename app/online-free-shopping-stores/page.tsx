import {
  GetAllStoresResponse,
  StoreControllerGetAllStoresParams,
} from '@/services/api/data-contracts';
import IndexAllStoresClients from './index-clients';
import { BASE_URL } from '@/config';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import { Metadata } from 'next';

export async function generateMetadata(): Promise<Metadata> {
  // Define static metadata
  const titleValue =
    'Best Free Shopping Stores & Cashback Offers – IndianCashback';
  const metaDesc =
    'Find the top online free shopping stores offering the best cashback deals & discounts. Shop smarter and save more with IndianCashback!';
  const metaKeyword =
    'online free shopping sites, cashback for online shopping, online shopping with cashback';

  return {
    title: titleValue,
    description: metaDesc,
    keywords: metaKeyword,
    alternates: {
      canonical: 'https://www.indiancashback.com/online-free-shopping-stores',
    },
    openGraph: {
      url: 'https://www.indiancashback.com/online-free-shopping-stores',
      title: titleValue,
      description: metaDesc,
    },
  };
}

async function getStoresData(searchParams: StoreControllerGetAllStoresParams) {
  const {
    searchParam = '',
    sortType = 'popular',
    subCategories = '',
    page = '1',
    pageSize = '18',
    minPercent = 0,
    maxPercent = 100,
  } = {
    ...searchParams,
  };

  const accessToken = getCookie('accessToken', { cookies }) as string;
  //   const query = `searchParam=${searchParam}&sortType=${sortType}&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}&minPercent=${minPercent}&maxPercent=${maxPercent}`;
  const query = `searchParam=${searchParam}&sortType=${sortType}&page=${page}&pageSize=${pageSize}&minPercent=${minPercent}&maxPercent=${maxPercent}${
    subCategories.length ? `&subCategories=${subCategories}` : ''
  }`;
  return await fetchWrapper<GetAllStoresResponse>(
    `${BASE_URL}/stores?${query}`,
    {
      token: accessToken,
      cache: 'no-store',
    }
  );
}

//using hooks and client side functions listeners in IndexClient (i.e in a client component),
//so that page remains server side completely and fetch data on server side which improve overall performance.
const Page = async ({
  searchParams,
}: {
  searchParams: StoreControllerGetAllStoresParams;
}) => {
  const resData: GetAllStoresResponse = await getStoresData(searchParams);
  return <IndexAllStoresClients data={resData} />;
};

export default Page;

// Dynamic Configuration - using searchParams and cookies makes this page dynamic
export const dynamic = 'force-dynamic';

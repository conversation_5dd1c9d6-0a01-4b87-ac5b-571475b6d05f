import { <PERSON><PERSON><PERSON>, <PERSON>ua_One } from 'next/font/google';
import clsx from 'clsx';
import localFont from 'next/font/local';
import { Providers } from '@/redux/provider';
import LoginSignUpModal from './components/auth/login-signup-modal';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import '@/app/styles/globals.scss';
import MainHeader from './components/headers/main-header';
import { BASE_URL } from '@/config';
import { cookies } from 'next/headers';
// import ClickRegisteredModal from './components/modals/click-registered-modal';
import type {
  CategoryResponse,
  SubCategoriesByCategoryResponse,
} from '@/services/api/data-contracts';
import fetchWrapper from '@/utils/fetch-wrapper';
import MainFooter from './components/footers/main-footer';
import BottomNavbar from './components/footers/BottomNavbar';
import type { Metadata, Viewport } from 'next';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import PWAInstallPrompt from './components/PWAInstallPrompt';
import { GoogleAnalytics } from '@next/third-parties/google';
import ClickRegisteredModal from './components/modals/click-registered-modal';
import { generateBaseSchema } from '@/utils/schema';
import AuthProvider from './components/auth/auth-provider';
// import ForesightProvider from './components/providers/foresight-provider';

const GA_TRACKING_ID = 'G-VNRELXRWKL';

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['400', '500', '600', '700'],
  variable: '--font-poppins',
});

const patuaOne = Patua_One({
  subsets: ['latin'],
  weight: ['400'],
  variable: '--font-patuaOne',
});

const nexa = localFont({
  src: [
    {
      path: '../public/fonts/NexaLight.otf',
      weight: '300',
      style: 'normal',
    },
    {
      path: '../public/fonts/NexaBold.otf',
      weight: '400',
      style: 'normal',
    },

    //ANCHOR - nexa black to open sans
    {
      path: '../public/fonts/OpenSans-ExtraBold.ttf',
      weight: '900',
      style: 'normal',
    },
  ],
  variable: '--font-nexa',
});

export const viewport: Viewport = {
  themeColor: '#574abe',
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1, // Disable zoom to prevent zoom on input focus
  userScalable: false, // Completely disable user scaling for smooth mobile experience
};

export const metadata: Metadata = {
  metadataBase: new URL('https://www.indiancashback.com'),
  title: {
    template: '%s | IndianCashback',
    default: 'Best Cashback & Coupons for Online Shopping - IndianCashback',
  },
  description:
    'Find the best cashback offers, free coupons & deals for online shopping in India. IndianCashback.com helps you save money with exclusive cashback rewards on 600+ popular stores and brands.',
  keywords:
    'save on online shopping, free online shopping india, free offers online, coupons and cashbacks',
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-snippet': -1,
      'max-video-preview': -1,
    },
  },
  alternates: {
    canonical: '/',
    languages: {
      'en-US': 'https://www.indiancashback.com',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_IN',
    url: 'https://www.indiancashback.com',
    siteName: 'IndianCashback',
    title:
      'Best cashback | coupon codes | online shopping - IndianCashback.com',
    description:
      'Are you searching for the best offers, free coupons & cashback deals online? Visit IndianCashback.com for one of the best cashback websites in India',
    images: [
      {
        url: 'https://www.indiancashback.com/img/og-image.png',
        width: 1200,
        height: 630,
        alt: 'IndianCashback - Save On Online Shopping',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@indiancashback',
    creator: '@indiancashback',
    title:
      'Best cashback | coupon codes | online shopping - IndianCashback.com',
    description:
      'Are you searching for the best offers, free coupons & cashback deals online? Visit IndianCashback.com for one of the best cashback websites in India',
    images: ['https://www.indiancashback.com/img/og-image.png'],
  },
  icons: {
    icon: [
      { url: '/favicon.png', sizes: '32x32', type: 'image/png' },
      { url: '/icon-48x48.png', sizes: '48x48', type: 'image/png' },
      { url: '/icon-72x72.png', sizes: '72x72', type: 'image/png' },
      { url: '/icon-96x96.png', sizes: '96x96', type: 'image/png' },
      { url: '/icon-144x144.png', sizes: '144x144', type: 'image/png' },
      { url: '/icon-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/icon-512x512.png', sizes: '512x512', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      {
        rel: 'manifest',
        url: '/manifest.webmanifest',
      },
      {
        rel: 'mask-icon',
        url: '/icon-192x192.png',
        color: '#574abe',
      },
    ],
  },
  manifest: '/manifest.webmanifest',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'IndianCashback',
    startupImage: [
      {
        url: '/icon-192x192.png',
        media:
          '(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)',
      },
    ],
  },
  other: {
    'content-type': 'text/html; charset=UTF-8',
    'content-language': 'en-us',
  },
};

async function getCategoriesData() {
  const categories = await fetchWrapper<CategoryResponse[]>(
    `${BASE_URL}/context/category?trending=false`
  );
  // Get the first category
  const firstCategory = categories[0];

  // Fetch the subcategories for the first category
  const subCategories = await fetchWrapper<SubCategoriesByCategoryResponse>(
    `${BASE_URL}/context/category/sub-category${firstCategory.id}`
  );
  return { categories, subCategories };
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  let data: any;
  try {
    data = await Promise.allSettled([getCategoriesData()]);
  } catch (err: any) {
    console.log({ err });
  }

  const token = cookies().get('accessToken');
  const [categoriesData] = data;

  const schema = generateBaseSchema();

  return (
    <html lang='en'>
      <head>
        {schema.map((schemaItem, index) => (
          <script
            dangerouslySetInnerHTML={{ __html: schemaItem }}
            key={`schema-${index}`}
            type='application/ld+json'
          />
        ))}
      </head>
      <body
        className={clsx(
          poppins.className,
          poppins.variable,
          nexa.variable,
          patuaOne.variable,
          'bg-body'
        )}
      >
        <AntdRegistry>
          <PWAInstallPrompt />
          <Providers>
            {/* <StyleRegistry>{children}</StyleRegistry> */}
            {/* <ForesightProvider
              config={{
                trajectoryPredictionTime: 80,
                defaultHitSlop: { top: 20, left: 20, right: 20, bottom: 30 },
                debug: process.env.NODE_ENV === 'development',
              }}
              enableInDevelopment={true}
              enableInProduction={true}
            > */}
            <AuthProvider>
              <MainHeader
                accessToken={token?.value}
                categoriesData={categoriesData.value}
                promiseStatus={categoriesData.status}
              />
              {children}
              <BottomNavbar />
              <MainFooter />
              <LoginSignUpModal isRootLayout />
              <ClickRegisteredModal />
              <ToastContainer
                className='text-[10px] z-[99999999]'
                position='bottom-center'
                theme='colored'
              />
              {/* <GlobalLoader /> */}
            </AuthProvider>
            {/* </ForesightProvider> */}
          </Providers>
        </AntdRegistry>
      </body>
      <GoogleAnalytics gaId={GA_TRACKING_ID} />
    </html>
  );
}

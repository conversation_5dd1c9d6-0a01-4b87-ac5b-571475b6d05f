'use client';
import React, { useState, useEffect, useCallback } from 'react';

// Section components (to be implemented in app/components/referral/)
import HowItWorksMain from '../components/referral-and-link-generator/HowItWorks';
import ReferFriendFAQ from '../components/referral-and-link-generator/ReferFriendFAQ';
import LinkGeneratorHero from '../components/referral-and-link-generator/LinkGeneratorHero';
import LinkGeneratorPerformance from '../components/referral-and-link-generator/LinkGeneratorPerformance';
import SupportedStores from '../components/referral-and-link-generator/SupportedStores';
import CalculateMonthlyProfit from '../components/referral-and-link-generator/CalculateMonthlyProfit';
import ShareAndEarnDeals from '../components/referral-and-link-generator/ShareAndEarnDeals';
import PerformanceAnalyticsCards from '../components/referral-and-link-generator/PerformanceAnalyticsCards';
import CommonHeader from '../components/headers/common-header';
import { useAppSelector } from '@/redux/hooks';
import fetchWrapper from '@/utils/fetch-wrapper';

// Define interfaces for analytics
interface MetricWithChange {
  value: number;
  percentageChange: number;
}

interface UserAnalyticsResponse {
  totalCashbackEarned: MetricWithChange;
  conversionRate: MetricWithChange;
  totalClicks: MetricWithChange;
}

const IndexClientsLinkGenerator = () => {
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const [isHydrated, setIsHydrated] = useState(false);
  const [analytics, setAnalytics] = useState([
    {
      title: 'Total Commission earned',
      value: '₹0',
      type: 'increment' as 'increment' | 'decrement',
      ratePercentage: 0,
      subTitle: 'from last period',
      image: '/temp/link-generator/rupee.png',
    },
    {
      title: 'Conversion Rate',
      value: '0%',
      type: 'increment' as 'increment' | 'decrement',
      ratePercentage: 0,
      subTitle: 'from last period',
      image: '/temp/link-generator/conversion.png',
    },
    {
      title: 'Total Clicks',
      value: '0',
      type: 'increment' as 'increment' | 'decrement',
      ratePercentage: 0,
      subTitle: 'from last period',
      image: '/temp/link-generator/click.png',
    },
  ]);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  const fetchUserAnalytics = useCallback(async () => {
    if (!isUserLogin) {
      return;
    }

    try {
      const queryParams = new URLSearchParams();
      queryParams.append('periodType', 'month');

      const response = await fetchWrapper<UserAnalyticsResponse>(
        `/api/proxy/links/analytics?${queryParams.toString()}`
      );

      if (response) {
        setAnalytics([
          {
            title: 'Total Commission earned',
            value: `₹${response.totalCashbackEarned?.value || 0}`,
            type:
              (response.totalCashbackEarned?.percentageChange || 0) >= 0
                ? 'increment'
                : 'decrement',
            ratePercentage: Math.abs(
              response.totalCashbackEarned?.percentageChange || 0
            ),
            subTitle: 'from last period',
            image: '/temp/link-generator/rupee.png',
          },
          {
            title: 'Conversion Rate',
            value: `${response.conversionRate?.value || 0}%`,
            type:
              (response.conversionRate?.percentageChange || 0) >= 0
                ? 'increment'
                : 'decrement',
            ratePercentage: Math.abs(
              response.conversionRate?.percentageChange || 0
            ),
            subTitle: 'from last period',
            image: '/temp/link-generator/conversion.png',
          },
          {
            title: 'Total Clicks',
            value: `${response.totalClicks?.value || 0}`,
            type:
              (response.totalClicks?.percentageChange || 0) >= 0
                ? 'increment'
                : 'decrement',
            ratePercentage: Math.abs(
              response.totalClicks?.percentageChange || 0
            ),
            subTitle: 'from last period',
            image: '/temp/link-generator/click.png',
          },
        ]);
      }
    } catch (error) {
      console.error('Error fetching analytics:', error);
    }
  }, [isUserLogin, setAnalytics]);

  useEffect(() => {
    if (isHydrated && isUserLogin) {
      fetchUserAnalytics();
    }
  }, [isHydrated, isUserLogin, fetchUserAnalytics]);

  return (
    <>
      <CommonHeader headline='Share & Earn' />
      <div className='w-full min-h-screen bg-background p-3 flex flex-col justify-start gap-y-4'>
        <PerformanceAnalyticsCards analytics={analytics} />
        <LinkGeneratorHero />
        <SupportedStores />
        <ShareAndEarnDeals />
        <CalculateMonthlyProfit />

        <HowItWorksMain type='link-generator' />
        <LinkGeneratorPerformance />
        <ReferFriendFAQ type='link-generator' />
      </div>
    </>
  );
};

export default IndexClientsLinkGenerator;

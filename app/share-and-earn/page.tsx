import React from 'react';

// Section components (to be implemented in app/components/referral/)
import IndexClientsLinkGenerator from './index-clients';

const LinkGeneratorPage = () => {
  return <IndexClientsLinkGenerator />;
};

export default LinkGeneratorPage;

// ISR Configuration
// Revalidate every 24 hours (86400 seconds)
// Link generator page content is static and changes infrequently
export const revalidate = 86400;

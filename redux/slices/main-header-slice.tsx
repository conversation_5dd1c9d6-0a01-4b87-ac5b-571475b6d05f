import { PayloadAction, createSlice } from '@reduxjs/toolkit';

const intialState: {
  isShowLeftPanel: boolean;
} = {
  isShowLeftPanel: false,
};

export const mainHeaderSlice = createSlice({
  name: 'user',
  initialState: intialState,
  reducers: {
    setShowLeftPanel: (state, action: PayloadAction<boolean>) => {
      state.isShowLeftPanel = action.payload;
    },
  },
});

export const { setShowLeftPanel } = mainHeaderSlice.actions;
export default mainHeaderSlice.reducer;

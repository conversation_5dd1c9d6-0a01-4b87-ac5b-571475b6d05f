import { PayloadAction, createSlice } from '@reduxjs/toolkit';

const initialState: {
  reportMissingStep: number;
  clickId: string;
  selectedStoreLogo: string;
  clickDate: string;
  clickTime: string;
  reportingUserType: string;
  isCouponUsed: boolean;
  reportingOrderId: string;
  reportingPaidAmount: string;
  reportingPlatform: string;
  complaintId: string;
} = {
  reportMissingStep: 1,
  clickId: '',
  selectedStoreLogo: '',
  clickDate: '',
  clickTime: '',
  reportingUserType: '',
  isCouponUsed: false,
  reportingOrderId: '',
  reportingPaidAmount: '',
  reportingPlatform: '',
  complaintId: '',
};

export const reportMissingCbStep = createSlice({
  name: 'reportMissingCb',
  initialState: initialState,
  reducers: {
    setReportMissingStep: (state, action: PayloadAction<number>) => {
      state.reportMissingStep = action.payload;
    },
    setClickId: (state, action: PayloadAction<string>) => {
      state.clickId = action.payload;
    },
    setComplaintId: (state, action: PayloadAction<string>) => {
      state.complaintId = action.payload;
    },
    setSelectedStoreLogo: (state, action: PayloadAction<string>) => {
      state.selectedStoreLogo = action.payload;
    },
    setClickedDate: (state, action: PayloadAction<string>) => {
      state.clickDate = action.payload;
    },
    setClickedTime: (state, action: PayloadAction<string>) => {
      state.clickTime = action.payload;
    },
    setReportingUserType: (state, action: PayloadAction<string>) => {
      state.reportingUserType = action.payload;
    },
    setIsCouponUsed: (state, action: PayloadAction<boolean>) => {
      state.isCouponUsed = action.payload;
    },
    setReportingOrderId: (state, action: PayloadAction<string>) => {
      state.reportingOrderId = action.payload;
    },
    setReportingPaidAmount: (state, action: PayloadAction<string>) => {
      state.reportingPaidAmount = action.payload;
    },
    setReportingPlatform: (state, action: PayloadAction<string>) => {
      state.reportingPlatform = action.payload;
    },
  },
});

export const {
  setReportMissingStep,
  setClickId,
  setSelectedStoreLogo,
  setClickedDate,
  setClickedTime,
  setReportingUserType,
  setIsCouponUsed,
  setReportingOrderId,
  setReportingPaidAmount,
  setReportingPlatform,
  setComplaintId,
} = reportMissingCbStep.actions;
export default reportMissingCbStep.reducer;

import { SubCategoriesByCategoryResponse } from '@/services/api/data-contracts';
import { SubCategoriesList } from '@/types/global-types';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';

const initialState: {
  isActiveCategoryLoading: boolean;
  activeCategory: string;
  subCategoriesList: SubCategoriesList;
} = {
  isActiveCategoryLoading: false,
  activeCategory: '',
  subCategoriesList: {},
};

export const categoriesListSlice = createSlice({
  name: 'categoriesList',
  initialState,
  reducers: {
    setActiveCategory: (state, action: PayloadAction<string>) => {
      state.activeCategory = action.payload;
    },
    setSubCategoriesList: (
      state,
      action: PayloadAction<{
        categoryId: string;
        subCategoriesList: SubCategoriesByCategoryResponse;
      }>
    ) => {
      const categoryId = action.payload.categoryId;
      state.subCategoriesList[categoryId] = action.payload.subCategoriesList;
    },
    setActiveCategoryLoading: (state, action: PayloadAction<boolean>) => {
      state.isActiveCategoryLoading = action.payload;
    },
  },
});

export const {
  setActiveCategory,
  setSubCategoriesList,
  setActiveCategoryLoading,
} = categoriesListSlice.actions;
export default categoriesListSlice.reducer;

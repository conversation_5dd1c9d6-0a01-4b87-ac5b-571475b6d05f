import { PayloadAction, createSlice } from '@reduxjs/toolkit';

const initialState: {
    isStoryModalOpen: boolean;
    currentSlideIndex: number;
    currentStoryIndex: number;
    storiesSeen: StoriesSeen;
} = {
    isStoryModalOpen: false,
    currentSlideIndex: 0,
    currentStoryIndex: 0,
    storiesSeen: {},
};

export const storiesSlice = createSlice({
    name: 'stories',
    initialState,
    reducers: {
        setCurrentSlideIndex: (state, action: PayloadAction<number>) => {
            state.currentSlideIndex = action.payload;
        },
        setCurrentStoryIndex: (state, action: PayloadAction<number>) => {
            state.currentStoryIndex = action.payload;
        },
        setIsStoryModalOpen: (state, action: PayloadAction<boolean>) => {
            state.isStoryModalOpen = action.payload;
        },
        setStoriesSeen: (state, action: PayloadAction<StoriesSeen>) => {
            state.storiesSeen = { ...state.storiesSeen, ...action.payload };
        },
    },
});

export const {
    setCurrentSlideIndex,
    setCurrentStoryIndex,
    setIsStoryModalOpen,
    setStoriesSeen,
} = storiesSlice.actions;
export default storiesSlice.reducer;

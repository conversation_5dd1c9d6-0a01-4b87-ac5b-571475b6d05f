import { CategorizedOffers } from "@/services/api/data-contracts";
import { PayloadAction, createSlice } from "@reduxjs/toolkit";

// interface OfferSavedState {
//     uid: string;
//     saved: boolean;
// }

interface OfferState {
	offersSavedStates: Record<number, boolean>; // Object mapping UID to saved state
}

const initialState: OfferState = {
	offersSavedStates: {},
};

const savedOffersSlice = createSlice({
	name: "offer",
	initialState,
	reducers: {
		initializeSavedStates: (
			state,
			action: PayloadAction<CategorizedOffers>,
		) => {
			const categorizedOffers = action.payload;

			// Initialize saved state for deals
			for (const offer of categorizedOffers.trendingOffers.deals) {
				state.offersSavedStates[offer.uid] = offer.saved;
			}

			// Initialize saved state for coupons
			for (const offer of categorizedOffers.trendingOffers.coupons) {
				state.offersSavedStates[offer.uid] = offer.saved;
			}
		},
		toggleSavedState: (
			state,
			action: PayloadAction<{ uid: number; saved: boolean }>,
		) => {
			const { uid, saved } = action.payload;
			state.offersSavedStates[uid] = saved; // Update the saved state
		},
	},
});

export const { initializeSavedStates, toggleSavedState } =
	savedOffersSlice.actions;
export default savedOffersSlice.reducer;

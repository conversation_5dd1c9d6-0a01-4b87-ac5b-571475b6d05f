import { initialDataGlobalSearchResult } from '@/app/components/landing/search/global-search-result';
import { SearchResponseItem } from '@/services/api/data-contracts';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';

// Define suggestion type
export interface Suggestion {
  id: number;
  name: string;
  offering: string;
}

// Initial suggestions data
export const initialSuggestions: Suggestion[] = [
  { id: 1, name: 'Flipkart', offering: 'Up to 7% Reward Points' },
  { id: 2, name: 'Amazon', offering: 'Up to 10% Cashback' },
  { id: 3, name: 'Shopsy', offering: 'Up to 8% Cashback' },
  { id: 4, name: 'Ajio', offering: 'Up to 6.5% Cashback' },
  { id: 5, name: 'TaTaCliQ', offering: 'Flat 4% Cashback' },
];

const initialState: {
  isGlobalSearchActive: boolean;
  isShowSuggestions: boolean;
  searchValue: string;
  isShowSearchLeftPanel: boolean;
  globalSearchResult: SearchResponseItem;
  noDataFound: boolean;
  suggestions: Suggestion[];
} = {
  searchValue: '',
  isShowSuggestions: true,
  isGlobalSearchActive: false,
  isShowSearchLeftPanel: false,
  globalSearchResult: initialDataGlobalSearchResult,
  noDataFound: false,
  suggestions: initialSuggestions,
};

export const globalSearchSlice = createSlice({
  name: 'globalSearch',
  initialState,
  reducers: {
    setGlobalSearchActive: (state, action: PayloadAction<boolean>) => {
      state.isGlobalSearchActive = action.payload;
    },
    setSearchValue: (state, action: PayloadAction<string>) => {
      state.searchValue = action.payload;
    },
    setShowSuggestions: (state, action: PayloadAction<boolean>) => {
      state.isShowSuggestions = action.payload;
    },
    setShowSearchLeftPanel: (state, action: PayloadAction<boolean>) => {
      state.isShowSearchLeftPanel = action.payload;
    },
    setGlobalSearchResult: (
      state,
      action: PayloadAction<SearchResponseItem>
    ) => {
      state.globalSearchResult = action.payload;
    },
    setNoDataFound: (state, action: PayloadAction<boolean>) => {
      state.noDataFound = action.payload;
    },
    removeSuggestion: (state, action: PayloadAction<number>) => {
      state.suggestions = state.suggestions.filter(
        (suggestion) => suggestion.id !== action.payload
      );
    },
    resetSuggestions: (state) => {
      state.suggestions = initialSuggestions;
    },
  },
});

export const {
  setGlobalSearchActive,
  setSearchValue,
  setShowSuggestions,
  setShowSearchLeftPanel,
  setGlobalSearchResult,
  setNoDataFound,
  removeSuggestion,
  resetSuggestions,
} = globalSearchSlice.actions;
export default globalSearchSlice.reducer;

import {
  AllCategoriesResponse,
  GetAllOnGoingOffersResponse,
  OfferTypes,
  ReviewTypes,
  SortTypes,
  UserTypes,
} from '@/services/api/data-contracts';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';

const initialState: {
  selectedSort: SortTypes;
  searchValue: string;
  searchValueCat: string;
  selectedUserType: UserTypes;
  selectedOfferType: OfferTypes;
  percentageFilter: {
    minPercent: number;
    maxPercent: number;
  };
  categories: AllCategoriesResponse[];
  onGoingSalesList: GetAllOnGoingOffersResponse[];
  filteredCategories: AllCategoriesResponse[];
  selectedSubCategories: Array<{ uid: number; name: string }>;
  selectedOnGoingSales: Array<{ uid: number; name: string }>;
  dateRangeFilter: {
    startDate: string;
    endDate: string;
  };
  dateFilter: string;
  storesFilter: string[];
  clickStatus: string[];
  reviewSortFilter: ReviewTypes;
} = {
  selectedSort: SortTypes.Newest,
  searchValue: '',
  searchValueCat: '',
  selectedUserType: UserTypes.Both,
  selectedOfferType: OfferTypes.Deals,
  percentageFilter: {
    minPercent: 25,
    maxPercent: 65,
  },
  categories: [],
  onGoingSalesList: [],
  selectedSubCategories: [],
  selectedOnGoingSales: [],
  filteredCategories: [],
  dateRangeFilter: {
    startDate: '',
    endDate: '',
  },
  dateFilter: '',
  storesFilter: [],
  clickStatus: [],
  reviewSortFilter: ReviewTypes.Newest,
};

const commonFiltersSlice = createSlice({
  name: 'commonFiltersSlice',
  initialState,
  reducers: {
    setSelectedSort: (state, action: PayloadAction<SortTypes>) => {
      state.selectedSort = action.payload;
    },
    setSelectedDate: (state, action: PayloadAction<string>) => {
      state.dateFilter = action.payload;
    },
    setSearchValue: (state, action: PayloadAction<string>) => {
      state.searchValue = action.payload;
    },
    setReviewSortFilter: (state, action: PayloadAction<ReviewTypes>) => {
      state.reviewSortFilter = action.payload;
    },
    setPercentageFilter: (
      state,
      action: PayloadAction<{ minPercent: number; maxPercent: number }>
    ) => {
      state.percentageFilter = action.payload;
    },
    setDateRangeFilter: (
      state,
      action: PayloadAction<{ startDate: string; endDate: string }>
    ) => {
      state.dateRangeFilter = action.payload;
    },

    setSearchValueCat: (state, action: PayloadAction<string>) => {
      state.searchValueCat = action.payload;
    },
    setSelectedUserType: (state, action: PayloadAction<UserTypes>) => {
      state.selectedUserType = action.payload;
    },
    setSelectedOfferType: (state, action: PayloadAction<OfferTypes>) => {
      state.selectedOfferType = action.payload;
    },

    setCategories: (state, action: PayloadAction<AllCategoriesResponse[]>) => {
      state.categories = action.payload;
    },
    setSelectedStoresFilter: (state, action: PayloadAction<string[]>) => {
      state.storesFilter = action.payload;
    },
    setSelectedStoreStatusFilter: (state, action: PayloadAction<string[]>) => {
      state.clickStatus = action.payload;
    },

    setOnGoingSalesList: (
      state,
      action: PayloadAction<GetAllOnGoingOffersResponse[]>
    ) => {
      state.onGoingSalesList = action.payload;
    },
    setFilteredCategories: (
      state,
      action: PayloadAction<AllCategoriesResponse[]>
    ) => {
      state.filteredCategories = action.payload;
    },
    setSelectedSubCategories: (
      state,
      action: PayloadAction<Array<{ uid: number; name: string }>>
    ) => {
      state.selectedSubCategories = action.payload;
    },
    setSelectedOnGoingSales: (
      state,
      action: PayloadAction<Array<{ uid: number; name: string }>>
    ) => {
      state.selectedOnGoingSales = action.payload;
    },
  },
});

export const {
  setSelectedSort,
  setSearchValue,
  setPercentageFilter,
  setDateRangeFilter,
  setSearchValueCat,
  setSelectedUserType,
  setSelectedOfferType,
  setCategories,
  setOnGoingSalesList,
  setFilteredCategories,
  setSelectedSubCategories,
  setSelectedOnGoingSales,
  setSelectedStoresFilter,
  setSelectedStoreStatusFilter,
  setSelectedDate,
  setReviewSortFilter,
} = commonFiltersSlice.actions;

export default commonFiltersSlice.reducer;

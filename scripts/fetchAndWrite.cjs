const https = require('node:https');
const fs = require('node:fs');
const { promisify } = require('node:util');
const { safeDestr } = require("destr");
const writeFile = promisify(fs.writeFile);
const url = 'https://api-main.indiancashback.com/api-legacy-docs-json';
// const url =
//   'https://34.icb-backend-dev.indiancashback.com/api-legacy-docs-json';
// const url = 'http://localhost:3005/api-legacy-docs-json';
// const url =
//   'https://centered-heritage-immigrants-ronald.trycloudflare.com/api-legacy-docs-json';

https
  .get(url, (res) => {
    let body = '';

    res.on('data', (chunk) => {
      body += chunk;
    });

    res.on('end', async () => {
      try {
        const fetchedJson = safeDestr(body);

        await writeFile('swagger.json', JSON.stringify(fetchedJson, null, 2));
        console.log('Successfully wrote Swagger JSON to swagger.json');
      } catch (error) {
        console.error('Error in parsing or writing JSON', error);
      }
    });
  })
  .on('error', (error) => {
    console.error('Got an error:', error);
  });

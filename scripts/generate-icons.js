#!/usr/bin/env node

const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Configuration for icon generation
const config = {
  baseIcon: 'public/icon-192x192.png',
  baseIconLarge: 'public/icon-512x512.png',
  outputDir: 'public',
  icons: [
    // Standard icons
    { name: 'icon-48x48.png', size: 48 },
    { name: 'icon-72x72.png', size: 72 },
    { name: 'icon-96x96.png', size: 96 },
    { name: 'icon-144x144.png', size: 144 },
    { name: 'apple-touch-icon.png', size: 180 },
  ],
  maskableIcons: [
    { name: 'icon-maskable-192x192.png', size: 192 },
    { name: 'icon-maskable-512x512.png', size: 512 },
  ]
};

async function generateIcon(inputPath, outputPath, size) {
  try {
    await sharp(inputPath)
      .resize(size, size, {
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 }
      })
      .png()
      .toFile(outputPath);
    
    console.log(`✅ Generated: ${outputPath} (${size}x${size})`);
  } catch (error) {
    console.error(`❌ Failed to generate ${outputPath}:`, error.message);
  }
}

async function generateMaskableIcon(inputPath, outputPath, size) {
  try {
    // For maskable icons, we need to add padding (safe zone)
    // The icon should fit within 80% of the canvas (20% padding total, 10% on each side)
    const iconSize = Math.round(size * 0.8);
    const padding = Math.round((size - iconSize) / 2);
    
    await sharp(inputPath)
      .resize(iconSize, iconSize, {
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 }
      })
      .extend({
        top: padding,
        bottom: padding,
        left: padding,
        right: padding,
        background: { r: 87, g: 74, b: 190, alpha: 1 } // Theme color background
      })
      .png()
      .toFile(outputPath);
    
    console.log(`✅ Generated maskable: ${outputPath} (${size}x${size})`);
  } catch (error) {
    console.error(`❌ Failed to generate maskable ${outputPath}:`, error.message);
  }
}

async function generateFavicon() {
  try {
    // Generate 16x16 and 32x32 PNG files first
    const favicon16Path = path.join(config.outputDir, 'favicon-16.png');
    const favicon32Path = path.join(config.outputDir, 'favicon-32.png');
    
    await generateIcon(config.baseIcon, favicon16Path, 16);
    await generateIcon(config.baseIcon, favicon32Path, 32);
    
    // For ICO generation, we'll create a simple PNG favicon
    // Most modern browsers support PNG favicons
    await generateIcon(config.baseIcon, path.join(config.outputDir, 'favicon.png'), 32);
    
    // Clean up temporary files
    if (fs.existsSync(favicon16Path)) fs.unlinkSync(favicon16Path);
    if (fs.existsSync(favicon32Path)) fs.unlinkSync(favicon32Path);
    
    console.log('✅ Generated: favicon.png (32x32)');
    console.log('ℹ️  Note: Created PNG favicon. For ICO format, use online tools or ImageMagick');
  } catch (error) {
    console.error('❌ Failed to generate favicon:', error.message);
  }
}

async function main() {
  console.log('🚀 Starting PWA icon generation...\n');
  
  // Check if base icons exist
  if (!fs.existsSync(config.baseIcon)) {
    console.error(`❌ Base icon not found: ${config.baseIcon}`);
    process.exit(1);
  }
  
  if (!fs.existsSync(config.baseIconLarge)) {
    console.error(`❌ Large base icon not found: ${config.baseIconLarge}`);
    process.exit(1);
  }
  
  // Generate standard icons
  console.log('📱 Generating standard icons...');
  for (const icon of config.icons) {
    const outputPath = path.join(config.outputDir, icon.name);
    const inputPath = icon.size <= 192 ? config.baseIcon : config.baseIconLarge;
    await generateIcon(inputPath, outputPath, icon.size);
  }
  
  console.log('\n🎭 Generating maskable icons...');
  for (const icon of config.maskableIcons) {
    const outputPath = path.join(config.outputDir, icon.name);
    const inputPath = icon.size <= 192 ? config.baseIcon : config.baseIconLarge;
    await generateMaskableIcon(inputPath, outputPath, icon.size);
  }
  
  console.log('\n🌟 Generating favicon...');
  await generateFavicon();
  
  console.log('\n✨ Icon generation complete!');
  console.log('\n📋 Generated files:');
  
  // List all generated files
  const allIcons = [
    ...config.icons.map(i => i.name),
    ...config.maskableIcons.map(i => i.name),
    'favicon.png'
  ];
  
  allIcons.forEach(icon => {
    const filePath = path.join(config.outputDir, icon);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      console.log(`   ✅ ${icon} (${Math.round(stats.size / 1024)}KB)`);
    } else {
      console.log(`   ❌ ${icon} (missing)`);
    }
  });
  
  console.log('\n🎯 Next steps:');
  console.log('   1. Update manifest.ts to reference favicon.png instead of favicon.ico');
  console.log('   2. Test PWA installation and icon display');
  console.log('   3. Run Lighthouse PWA audit to validate');
}

// Run the script
main().catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  ChatControllerChatData,
  ChatControllerEmbedFileData,
  ChatRequestDto,
} from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Chat<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags Chat
   * @name ChatControllerChat
   * @summary Send a chat message and receive complete response
   * @request POST:/chat
   * @response `200` `ChatControllerChatData` Chat response
   * @response `201` `ChatResponseDto`
   * @response `400` `void` Invalid request
   * @response `500` `void` Internal server error
   */
  chatControllerChat = (data: ChatRequestDto, params: RequestParams = {}) =>
    this.request<ChatControllerChatData, void>({
      path: `/chat`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
  /**
   * No description
   *
   * @tags Chat
   * @name ChatControllerEmbedFile
   * @summary Upload and embed a TXT file for context
   * @request POST:/chat/embed
   * @response `200` `ChatControllerEmbedFileData`
   * @response `201` `EmbedResponseDto`
   * @response `400` `void` Invalid file or processing error
   */
  chatControllerEmbedFile = (params: RequestParams = {}) =>
    this.request<ChatControllerEmbedFileData, void>({
      path: `/chat/embed`,
      method: "POST",
      format: "json",
      ...params,
    });
}

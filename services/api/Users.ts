/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */



/**
* @title ICB API
* @version 1.0
* @contact 
*  
* ICB API for frontend
*/
export class Api<SecurityDataType extends unknown> extends HttpClient<SecurityDataType>  {



  
  /**
 * No description
 *
 * @tags Health
 * @name AppController<PERSON>etHello
 * @request GET:/
 * @response `200` `AppControllerGetHelloData`
 */
appControllerGetHello = (reqParams: RequestParams = {}) =>
    this.request<AppControllerGetHelloData, any>({
      path: `/`,
      method: "GET",      format: "json",      ...reqParams,
    });

  
    context = {
  
  /**
 * No description
 *
 * @tags Context
 * @name BannerControllerGetBanners
 * @request GET:/context/banner
 * @response `200` `BannerControllerGetBannersData`
 * @response `default` `BannerResponse`
 */
bannerControllerGetBanners = (reqParams: RequestParams = {}) =>
    this.request<BannerControllerGetBannersData, BannerResponse>({
      path: `/context/banner`,
      method: "GET",      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name QuickAccessControllerGetQuickAccesses
 * @request GET:/context/quick-access
 * @response `200` `QuickAccessControllerGetQuickAccessesData`
 * @response `default` `(QuickAccessResponseItem)[]`
 */
quickAccessControllerGetQuickAccesses = (reqParams: RequestParams = {}) =>
    this.request<QuickAccessControllerGetQuickAccessesData, (QuickAccessResponseItem)[]>({
      path: `/context/quick-access`,
      method: "GET",      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name QuickAccessControllerGetHeroQuickAccesses
 * @request GET:/context/quick-access/hero
 * @response `200` `QuickAccessControllerGetHeroQuickAccessesData`
 * @response `default` `(HeroResponseItem)[]`
 */
quickAccessControllerGetHeroQuickAccesses = (reqParams: RequestParams = {}) =>
    this.request<QuickAccessControllerGetHeroQuickAccessesData, (HeroResponseItem)[]>({
      path: `/context/quick-access/hero`,
      method: "GET",      format: "json",      ...reqParams,
    });

  
  /**
 * @description Returns stories grouped by store. For authenticated users, generates cached short URLs. For public users, uses original redirect URLs as short URLs.
 *
 * @tags Context
 * @name StoriesControllerGetStories
 * @summary Get stories for mobile app
 * @request GET:/context/stories
 * @secure
 * @response `200` `StoriesControllerGetStoriesData`
 * @response `401` `void` Unauthorized
 * @response `default` `(ResponseMobileStories)[]`
 */
storiesControllerGetStories = (reqParams: RequestParams = {}) =>
    this.request<StoriesControllerGetStoriesData, void | (ResponseMobileStories)[]>({
      path: `/context/stories`,
      method: "GET",      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name StoriesControllerClickStory
 * @request GET:/context/stories/click/{storyId}
 * @response `200` `StoriesControllerClickStoryData`
 */
storiesControllerClickStory = (storyId: string, reqParams: RequestParams = {}) =>
    this.request<StoriesControllerClickStoryData, any>({
      path: `/context/stories/click/${storyId}`,
      method: "GET",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name StoriesControllerGetStoryAnalytics
 * @request GET:/context/stories/analytics/{storyId}
 * @response `200` `StoriesControllerGetStoryAnalyticsData`
 */
storiesControllerGetStoryAnalytics = (storyId: string, reqParams: RequestParams = {}) =>
    this.request<StoriesControllerGetStoryAnalyticsData, any>({
      path: `/context/stories/analytics/${storyId}`,
      method: "GET",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name StoriesControllerGetAllStoriesAnalytics
 * @request GET:/context/stories/analytics
 * @response `200` `StoriesControllerGetAllStoriesAnalyticsData`
 */
storiesControllerGetAllStoriesAnalytics = (reqParams: RequestParams = {}) =>
    this.request<StoriesControllerGetAllStoriesAnalyticsData, any>({
      path: `/context/stories/analytics`,
      method: "GET",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name OffersControllerGetLandingOffers
 * @request GET:/context/offers
 * @secure
 * @response `200` `OffersControllerGetLandingOffersData`
 * @response `401` `void` Unauthorized
 * @response `default` `CategorizedOffers`
 */
offersControllerGetLandingOffers = (reqParams: RequestParams = {}) =>
    this.request<OffersControllerGetLandingOffersData, void | CategorizedOffers>({
      path: `/context/offers`,
      method: "GET",      secure: true,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name OffersControllerGetOnGoingSaleOffers
 * @request GET:/context/offers/all-on-going-offers
 * @response `200` `OffersControllerGetOnGoingSaleOffersData`
 * @response `default` `(GetAllOnGoingOffersResponse)[]`
 */
offersControllerGetOnGoingSaleOffers = (reqParams: RequestParams = {}) =>
    this.request<OffersControllerGetOnGoingSaleOffersData, (GetAllOnGoingOffersResponse)[]>({
      path: `/context/offers/all-on-going-offers`,
      method: "GET",      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name CategoryControllerGetAllCategories
 * @request GET:/context/category
 * @response `200` `CategoryControllerGetAllCategoriesData`
 * @response `default` `(CategoryResponse)[]`
 */
categoryControllerGetAllCategories = (query: CategoryControllerGetAllCategoriesParams, reqParams: RequestParams = {}) =>
    this.request<CategoryControllerGetAllCategoriesData, (CategoryResponse)[]>({
      path: `/context/category`,
      method: "GET",      query: query,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name CategoryControllerGetSubCategoryByCategoryId
 * @request GET:/context/category/sub-category{id}
 * @secure
 * @response `200` `CategoryControllerGetSubCategoryByCategoryIdData`
 * @response `401` `void` Unauthorized
 * @response `default` `SubCategoriesByCategoryResponse`
 */
categoryControllerGetSubCategoryByCategoryId = (id: string, reqParams: RequestParams = {}) =>
    this.request<CategoryControllerGetSubCategoryByCategoryIdData, void | SubCategoriesByCategoryResponse>({
      path: `/context/category/sub-category${id}`,
      method: "GET",      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name CategoryControllerGetSubCategorySearch
 * @request GET:/context/category/search{value}
 * @secure
 * @response `200` `CategoryControllerGetSubCategorySearchData`
 * @response `401` `void` Unauthorized
 * @response `default` `(SubCategoriesByCategory)[]`
 */
categoryControllerGetSubCategorySearch = (value: string, reqParams: RequestParams = {}) =>
    this.request<CategoryControllerGetSubCategorySearchData, void | (SubCategoriesByCategory)[]>({
      path: `/context/category/search${value}`,
      method: "GET",      secure: true,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name CategoryControllerGetAllCategoriesDetails
 * @request GET:/context/category/all-categories-details
 * @response `200` `CategoryControllerGetAllCategoriesDetailsData`
 * @response `default` `(AllCategoriesResponse)[]`
 */
categoryControllerGetAllCategoriesDetails = (reqParams: RequestParams = {}) =>
    this.request<CategoryControllerGetAllCategoriesDetailsData, (AllCategoriesResponse)[]>({
      path: `/context/category/all-categories-details`,
      method: "GET",      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name StoresControllerGetContextStoresByCb
 * @request GET:/context/stores-by-cb-percent
 * @secure
 * @response `200` `StoresControllerGetContextStoresByCbData`
 * @response `401` `void` Unauthorized
 * @response `default` `StoresByCbContextResponse`
 */
storesControllerGetContextStoresByCb = (reqParams: RequestParams = {}) =>
    this.request<StoresControllerGetContextStoresByCbData, void | StoresByCbContextResponse>({
      path: `/context/stores-by-cb-percent`,
      method: "GET",      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name StoresControllerGetAllCategories
 * @request GET:/context/stores-by-cb-percent/find-by-category
 * @response `200` `StoresControllerGetAllCategoriesData`
 * @response `default` `(CategoryStoresResponse)[]`
 */
storesControllerGetAllCategories = (reqParams: RequestParams = {}) =>
    this.request<StoresControllerGetAllCategoriesData, (CategoryStoresResponse)[]>({
      path: `/context/stores-by-cb-percent/find-by-category`,
      method: "GET",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name SearchControllerGetSearchResults
 * @request GET:/context/search
 * @response `200` `SearchControllerGetSearchResultsData`
 * @response `default` `SearchResponseItem`
 */
searchControllerGetSearchResults = (query: SearchControllerGetSearchResultsParams, reqParams: RequestParams = {}) =>
    this.request<SearchControllerGetSearchResultsData, SearchResponseItem>({
      path: `/context/search`,
      method: "GET",      query: query,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name TermsAndPrivacyControllerGetAllTermsAndConditions
 * @request GET:/context/terms-and-privacy
 * @response `200` `TermsAndPrivacyControllerGetAllTermsAndConditionsData`
 * @response `default` `TermsAndPrivacyResponseItem`
 */
termsAndPrivacyControllerGetAllTermsAndConditions = (query: TermsAndPrivacyControllerGetAllTermsAndConditionsParams, reqParams: RequestParams = {}) =>
    this.request<TermsAndPrivacyControllerGetAllTermsAndConditionsData, TermsAndPrivacyResponseItem>({
      path: `/context/terms-and-privacy`,
      method: "GET",      query: query,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Context
 * @name TestimonialControllerGetAllTestimonials
 * @request GET:/context/testimonials
 * @response `200` `TestimonialControllerGetAllTestimonialsData`
 * @response `default` `(TestimonialResponseType)[]`
 */
testimonialControllerGetAllTestimonials = (reqParams: RequestParams = {}) =>
    this.request<TestimonialControllerGetAllTestimonialsData, (TestimonialResponseType)[]>({
      path: `/context/testimonials`,
      method: "GET",      format: "json",      ...reqParams,
    });

    }
    auth = {
  
  /**
 * No description
 *
 * @tags Auth
 * @name AuthControllerCreate
 * @request POST:/auth/register
 * @response `201` `AuthControllerCreateData`
 * @response `default` `UserResponse`
 */
authControllerCreate = (data: CreateUserDto, reqParams: RequestParams = {}) =>
    this.request<AuthControllerCreateData, UserResponse>({
      path: `/auth/register`,
      method: "POST",      body: data,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Auth
 * @name AuthControllerLogin
 * @request POST:/auth/login
 * @response `201` `AuthControllerLoginData`
 * @response `default` `LoginResponse`
 */
authControllerLogin = (data: LoginDto, reqParams: RequestParams = {}) =>
    this.request<AuthControllerLoginData, LoginResponse>({
      path: `/auth/login`,
      method: "POST",      body: data,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Auth
 * @name AuthControllerVerifyOtp
 * @request POST:/auth/verify-user
 * @response `201` `AuthControllerVerifyOtpData`
 * @response `default` `boolean`
 */
authControllerVerifyOtp = (data: VerifyUserDto, reqParams: RequestParams = {}) =>
    this.request<AuthControllerVerifyOtpData, boolean>({
      path: `/auth/verify-user`,
      method: "POST",      body: data,      type: ContentType.Json,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Auth
 * @name AuthControllerResendOtp
 * @request POST:/auth/resend-otp
 * @response `201` `AuthControllerResendOtpData`
 * @response `default` `LoginResponse`
 */
authControllerResendOtp = (data: LoginDto, reqParams: RequestParams = {}) =>
    this.request<AuthControllerResendOtpData, LoginResponse>({
      path: `/auth/resend-otp`,
      method: "POST",      body: data,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Auth
 * @name AuthControllerLogout
 * @request DELETE:/auth/logout
 * @secure
 * @response `200` `AuthControllerLogoutData`
 * @response `401` `void` Unauthorized
 */
authControllerLogout = (reqParams: RequestParams = {}) =>
    this.request<AuthControllerLogoutData, void>({
      path: `/auth/logout`,
      method: "DELETE",      secure: true,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Auth
 * @name AuthControllerCheck
 * @request GET:/auth/check
 * @secure
 * @response `200` `AuthControllerCheckData`
 * @response `401` `void` Unauthorized
 */
authControllerCheck = (reqParams: RequestParams = {}) =>
    this.request<AuthControllerCheckData, void>({
      path: `/auth/check`,
      method: "GET",      secure: true,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Auth
 * @name AuthControllerGetCsrfToken
 * @request GET:/auth/token
 * @response `200` `AuthControllerGetCsrfTokenData`
 */
authControllerGetCsrfToken = (reqParams: RequestParams = {}) =>
    this.request<AuthControllerGetCsrfTokenData, any>({
      path: `/auth/token`,
      method: "GET",      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Auth
 * @name AuthControllerGoogleAuth
 * @request GET:/auth/google
 * @response `200` `AuthControllerGoogleAuthData`
 * @response `default` `void` Initiates Google OAuth authentication flow
 */
authControllerGoogleAuth = (query: AuthControllerGoogleAuthParams, reqParams: RequestParams = {}) =>
    this.request<AuthControllerGoogleAuthData, void>({
      path: `/auth/google`,
      method: "GET",      query: query,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Auth
 * @name AuthControllerGoogleAuthCallback
 * @request GET:/auth/google/callback
 * @response `200` `AuthControllerGoogleAuthCallbackData`
 * @response `default` `void` Handles the callback from Google OAuth authentication
 */
authControllerGoogleAuthCallback = (query: AuthControllerGoogleAuthCallbackParams, reqParams: RequestParams = {}) =>
    this.request<AuthControllerGoogleAuthCallbackData, void>({
      path: `/auth/google/callback`,
      method: "GET",      query: query,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Auth
 * @name AuthControllerVerifyToken
 * @request GET:/auth/verify-token
 * @response `200` `AuthControllerVerifyTokenData`
 */
authControllerVerifyToken = (query: AuthControllerVerifyTokenParams, reqParams: RequestParams = {}) =>
    this.request<AuthControllerVerifyTokenData, any>({
      path: `/auth/verify-token`,
      method: "GET",      query: query,      ...reqParams,
    });

    }
    users = {
  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerGetBankDetails
 * @request GET:/users/get-bank-details
 * @secure
 * @response `200` `UserControllerGetBankDetailsData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetBankAccountDataResponse`
 */
userControllerGetBankDetails = (reqParams: RequestParams = {}) =>
    this.request<UserControllerGetBankDetailsData, void | GetBankAccountDataResponse>({
      path: `/users/get-bank-details`,
      method: "GET",      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerUpdateBankDetails
 * @request POST:/users/update-bank-details
 * @secure
 * @response `201` `UserControllerUpdateBankDetailsData`
 * @response `401` `void` Unauthorized
 * @response `default` `boolean`
 */
userControllerUpdateBankDetails = (data: UpdateBankAccountDto, reqParams: RequestParams = {}) =>
    this.request<UserControllerUpdateBankDetailsData, void | boolean>({
      path: `/users/update-bank-details`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerUpdateUpiId
 * @request PATCH:/users/update-upi
 * @secure
 * @response `200` `UserControllerUpdateUpiIdData`
 * @response `401` `void` Unauthorized
 * @response `default` `boolean`
 */
userControllerUpdateUpiId = (data: UpdateUpiDto, reqParams: RequestParams = {}) =>
    this.request<UserControllerUpdateUpiIdData, void | boolean>({
      path: `/users/update-upi`,
      method: "PATCH",      body: data,      secure: true,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerGetProfileDetails
 * @request GET:/users/me
 * @secure
 * @response `200` `UserControllerGetProfileDetailsData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetUserProfileResponseItem`
 */
userControllerGetProfileDetails = (reqParams: RequestParams = {}) =>
    this.request<UserControllerGetProfileDetailsData, void | GetUserProfileResponseItem>({
      path: `/users/me`,
      method: "GET",      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerGetUsersOverViewDetails
 * @request GET:/users/overview
 * @secure
 * @response `200` `UserControllerGetUsersOverViewDetailsData`
 * @response `401` `void` Unauthorized
 * @response `default` `UserOverviewResponse`
 */
userControllerGetUsersOverViewDetails = (reqParams: RequestParams = {}) =>
    this.request<UserControllerGetUsersOverViewDetailsData, void | UserOverviewResponse>({
      path: `/users/overview`,
      method: "GET",      secure: true,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerGetUsersByReferralCode
 * @request GET:/users/referral-history
 * @secure
 * @response `200` `UserControllerGetUsersByReferralCodeData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetUsersByReferralCodeResponse`
 */
userControllerGetUsersByReferralCode = (query: UserControllerGetUsersByReferralCodeParams, reqParams: RequestParams = {}) =>
    this.request<UserControllerGetUsersByReferralCodeData, void | GetUsersByReferralCodeResponse>({
      path: `/users/referral-history`,
      method: "GET",      query: query,      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerGetCashbackHistory
 * @request GET:/users/cashback-history
 * @secure
 * @response `200` `UserControllerGetCashbackHistoryData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetCbHistoryResponse`
 */
userControllerGetCashbackHistory = (query: UserControllerGetCashbackHistoryParams, reqParams: RequestParams = {}) =>
    this.request<UserControllerGetCashbackHistoryData, void | GetCbHistoryResponse>({
      path: `/users/cashback-history`,
      method: "GET",      query: query,      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerUpdateProfile
 * @request POST:/users/update-profile
 * @secure
 * @response `201` `UserControllerUpdateProfileData`
 * @response `401` `void` Unauthorized
 * @response `default` `boolean`
 */
userControllerUpdateProfile = (data: UpdateProfileWithImageDto, reqParams: RequestParams = {}) =>
    this.request<UserControllerUpdateProfileData, void | boolean>({
      path: `/users/update-profile`,
      method: "POST",      body: data,      secure: true,      type: ContentType.FormData,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerSendOtpToUpdateUserCredentials
 * @request POST:/users/update-credentials
 * @secure
 * @response `201` `UserControllerSendOtpToUpdateUserCredentialsData`
 * @response `401` `void` Unauthorized
 */
userControllerSendOtpToUpdateUserCredentials = (data: UpdateCredentialsDto, reqParams: RequestParams = {}) =>
    this.request<UserControllerSendOtpToUpdateUserCredentialsData, void>({
      path: `/users/update-credentials`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerVerifyOtpToUpdateCredentials
 * @request POST:/users/verify-credentials-otp
 * @secure
 * @response `201` `UserControllerVerifyOtpToUpdateCredentialsData`
 * @response `401` `void` Unauthorized
 */
userControllerVerifyOtpToUpdateCredentials = (data: OtpDto, reqParams: RequestParams = {}) =>
    this.request<UserControllerVerifyOtpToUpdateCredentialsData, void>({
      path: `/users/verify-credentials-otp`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerGetAllPersonalInterestData
 * @request GET:/users/get-personal-interest
 * @response `200` `UserControllerGetAllPersonalInterestDataData`
 */
userControllerGetAllPersonalInterestData = (reqParams: RequestParams = {}) =>
    this.request<UserControllerGetAllPersonalInterestDataData, any>({
      path: `/users/get-personal-interest`,
      method: "GET",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerUpdateAllUsersReferralCode
 * @request POST:/users/update-referral-code
 * @response `201` `UserControllerUpdateAllUsersReferralCodeData`
 */
userControllerUpdateAllUsersReferralCode = (reqParams: RequestParams = {}) =>
    this.request<UserControllerUpdateAllUsersReferralCodeData, any>({
      path: `/users/update-referral-code`,
      method: "POST",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Users
 * @name UserControllerListUsers
 * @request GET:/users/list
 * @response `default` `UserListResponseDto`
 */
userControllerListUsers = (reqParams: RequestParams = {}) =>
    this.request<any, UserListResponseDto>({
      path: `/users/list`,
      method: "GET",      ...reqParams,
    });

    }
    campaign = {
  
  /**
 * No description
 *
 * @tags ReferralCampaign
 * @name ReferralCampaignControllerGetReferralEarningsLeaderboard
 * @request GET:/campaign/referral-earnings-leaderboard
 * @response `200` `ReferralCampaignControllerGetReferralEarningsLeaderboardData`
 * @response `default` `(GetReferralLeaderboardResponse)[]`
 */
referralCampaignControllerGetReferralEarningsLeaderboard = (query: ReferralCampaignControllerGetReferralEarningsLeaderboardParams, reqParams: RequestParams = {}) =>
    this.request<ReferralCampaignControllerGetReferralEarningsLeaderboardData, (GetReferralLeaderboardResponse)[]>({
      path: `/campaign/referral-earnings-leaderboard`,
      method: "GET",      query: query,      format: "json",      ...reqParams,
    });

    }
    stores = {
  
  /**
 * No description
 *
 * @tags Stores
 * @name StoreControllerGetAllStores
 * @request GET:/stores
 * @secure
 * @response `200` `StoreControllerGetAllStoresData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetAllStoresResponse`
 */
storeControllerGetAllStores = (query: StoreControllerGetAllStoresParams, reqParams: RequestParams = {}) =>
    this.request<StoreControllerGetAllStoresData, void | GetAllStoresResponse>({
      path: `/stores`,
      method: "GET",      query: query,      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Stores
 * @name StoreControllerGetStoreDetailsByName
 * @request GET:/stores/store-details{name}
 * @secure
 * @response `200` `StoreControllerGetStoreDetailsByNameData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetStoreDetailsResponse`
 */
storeControllerGetStoreDetailsByName = (name: string, reqParams: RequestParams = {}) =>
    this.request<StoreControllerGetStoreDetailsByNameData, void | GetStoreDetailsResponse>({
      path: `/stores/store-details${name}`,
      method: "GET",      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Stores
 * @name StoreControllerGetCashbackRatesByStoreId
 * @request GET:/stores/cashback-rates-by-store{id}
 * @response `200` `StoreControllerGetCashbackRatesByStoreIdData`
 * @response `default` `GetCashbackRatesByStoreResponse`
 */
storeControllerGetCashbackRatesByStoreId = (id: string, reqParams: RequestParams = {}) =>
    this.request<StoreControllerGetCashbackRatesByStoreIdData, GetCashbackRatesByStoreResponse>({
      path: `/stores/cashback-rates-by-store${id}`,
      method: "GET",      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Stores
 * @name StoreControllerSaveItem
 * @request POST:/stores/save
 * @secure
 * @response `201` `StoreControllerSaveItemData`
 * @response `401` `void` Unauthorized
 * @response `default` `SaveItemResponse`
 */
storeControllerSaveItem = (data: SaveOfferDto, reqParams: RequestParams = {}) =>
    this.request<StoreControllerSaveItemData, void | SaveItemResponse>({
      path: `/stores/save`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Stores
 * @name StoreControllerRemoveSavedItem
 * @request POST:/stores/remove
 * @secure
 * @response `201` `StoreControllerRemoveSavedItemData`
 * @response `401` `void` Unauthorized
 * @response `default` `SaveItemResponse`
 */
storeControllerRemoveSavedItem = (data: RemoveOfferDto, reqParams: RequestParams = {}) =>
    this.request<StoreControllerRemoveSavedItemData, void | SaveItemResponse>({
      path: `/stores/remove`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Review
 * @name ReviewControllerCreate
 * @request POST:/stores/review/add-review
 * @secure
 * @response `201` `ReviewControllerCreateData`
 * @response `401` `void` Unauthorized
 * @response `default` `CreateReviewResponse`
 */
reviewControllerCreate = (data: CreateReviewDto, reqParams: RequestParams = {}) =>
    this.request<ReviewControllerCreateData, void | CreateReviewResponse>({
      path: `/stores/review/add-review`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Review
 * @name ReviewControllerGetAllGiftCards
 * @request GET:/stores/review
 * @response `200` `ReviewControllerGetAllGiftCardsData`
 * @response `default` `GetAllReviewsResponse`
 */
reviewControllerGetAllGiftCards = (query: ReviewControllerGetAllGiftCardsParams, reqParams: RequestParams = {}) =>
    this.request<ReviewControllerGetAllGiftCardsData, GetAllReviewsResponse>({
      path: `/stores/review`,
      method: "GET",      query: query,      ...reqParams,
    });

    }
    giftCards = {
  
  /**
 * No description
 *
 * @tags GiftCards
 * @name GiftCardControllerGetAllGiftCards
 * @request GET:/gift-cards
 * @secure
 * @response `200` `GiftCardControllerGetAllGiftCardsData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetGiftCardListResponse`
 */
giftCardControllerGetAllGiftCards = (query: GiftCardControllerGetAllGiftCardsParams, reqParams: RequestParams = {}) =>
    this.request<GiftCardControllerGetAllGiftCardsData, void | GetGiftCardListResponse>({
      path: `/gift-cards`,
      method: "GET",      query: query,      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags GiftCards
 * @name GiftCardControllerGetAllGiftCardBanners
 * @request GET:/gift-cards/banners
 * @response `200` `GiftCardControllerGetAllGiftCardBannersData`
 * @response `default` `GiftCardBannersResponse`
 */
giftCardControllerGetAllGiftCardBanners = (reqParams: RequestParams = {}) =>
    this.request<GiftCardControllerGetAllGiftCardBannersData, GiftCardBannersResponse>({
      path: `/gift-cards/banners`,
      method: "GET",      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags GiftCards
 * @name GiftCardControllerGetGiftCardDetails
 * @request GET:/gift-cards/gift-card{id}
 * @secure
 * @response `200` `GiftCardControllerGetGiftCardDetailsData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetGiftCardResponse`
 */
giftCardControllerGetGiftCardDetails = (id: string, reqParams: RequestParams = {}) =>
    this.request<GiftCardControllerGetGiftCardDetailsData, void | GetGiftCardResponse>({
      path: `/gift-cards/gift-card${id}`,
      method: "GET",      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags GiftCards
 * @name GiftCardControllerSaveItem
 * @request POST:/gift-cards/save
 * @secure
 * @response `201` `GiftCardControllerSaveItemData`
 * @response `401` `void` Unauthorized
 * @response `default` `SaveItemResponse`
 */
giftCardControllerSaveItem = (data: SaveOfferDto, reqParams: RequestParams = {}) =>
    this.request<GiftCardControllerSaveItemData, void | SaveItemResponse>({
      path: `/gift-cards/save`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags GiftCards
 * @name GiftCardControllerRemoveSavedItem
 * @request POST:/gift-cards/remove
 * @secure
 * @response `201` `GiftCardControllerRemoveSavedItemData`
 * @response `401` `void` Unauthorized
 * @response `default` `SaveItemResponse`
 */
giftCardControllerRemoveSavedItem = (data: RemoveOfferDto, reqParams: RequestParams = {}) =>
    this.request<GiftCardControllerRemoveSavedItemData, void | SaveItemResponse>({
      path: `/gift-cards/remove`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags GiftCards
 * @name GiftCardControllerOrderGiftCard
 * @request POST:/gift-cards/create-order
 * @secure
 * @response `201` `GiftCardControllerOrderGiftCardData`
 * @response `401` `void` Unauthorized
 * @response `default` `OrderResponse`
 */
giftCardControllerOrderGiftCard = (data: OrderGiftCardDto, reqParams: RequestParams = {}) =>
    this.request<GiftCardControllerOrderGiftCardData, void | OrderResponse>({
      path: `/gift-cards/create-order`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags GiftCards
 * @name GiftCardControllerVerifyPayment
 * @request PUT:/gift-cards/verify-payment
 * @secure
 * @response `200` `GiftCardControllerVerifyPaymentData`
 * @response `401` `void` Unauthorized
 */
giftCardControllerVerifyPayment = (data: PaymentVerifyDto, reqParams: RequestParams = {}) =>
    this.request<GiftCardControllerVerifyPaymentData, void>({
      path: `/gift-cards/verify-payment`,
      method: "PUT",      body: data,      secure: true,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags GiftCards
 * @name GiftCardControllerRedeemIcbGiftCard
 * @request POST:/gift-cards/redeem
 * @secure
 * @response `201` `GiftCardControllerRedeemIcbGiftCardData`
 * @response `401` `void` Unauthorized
 * @response `default` `SaveItemResponse`
 */
giftCardControllerRedeemIcbGiftCard = (data: RedeemIcbGiftCardDto, reqParams: RequestParams = {}) =>
    this.request<GiftCardControllerRedeemIcbGiftCardData, void | SaveItemResponse>({
      path: `/gift-cards/redeem`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags GiftCards
 * @name GiftCardControllerRedeemGiftCardHistory
 * @request GET:/gift-cards/redeem
 * @secure
 * @response `200` `GiftCardControllerRedeemGiftCardHistoryData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetRedeemGiftCardListResponse`
 */
giftCardControllerRedeemGiftCardHistory = (query: GiftCardControllerRedeemGiftCardHistoryParams, reqParams: RequestParams = {}) =>
    this.request<GiftCardControllerRedeemGiftCardHistoryData, void | GetRedeemGiftCardListResponse>({
      path: `/gift-cards/redeem`,
      method: "GET",      query: query,      secure: true,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags GiftCards
 * @name GiftCardControllerGetIcbCard
 * @request GET:/gift-cards/icb-card
 * @response `200` `GiftCardControllerGetIcbCardData`
 * @response `default` `IcbCardTypeResponse`
 */
giftCardControllerGetIcbCard = (reqParams: RequestParams = {}) =>
    this.request<GiftCardControllerGetIcbCardData, IcbCardTypeResponse>({
      path: `/gift-cards/icb-card`,
      method: "GET",      ...reqParams,
    });

    }
    offers = {
  
  /**
 * No description
 *
 * @tags Offers
 * @name OfferControllerGetAllCategories
 * @request GET:/offers/deals-and-coupons
 * @secure
 * @response `200` `OfferControllerGetAllCategoriesData`
 * @response `401` `void` Unauthorized
 * @response `default` `DealAndCouponsResponse`
 */
offerControllerGetAllCategories = (query: OfferControllerGetAllCategoriesParams, reqParams: RequestParams = {}) =>
    this.request<OfferControllerGetAllCategoriesData, void | DealAndCouponsResponse>({
      path: `/offers/deals-and-coupons`,
      method: "GET",      query: query,      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Offers
 * @name OfferControllerGetOngoingOffers
 * @request GET:/offers/ongoing-offers
 * @secure
 * @response `200` `OfferControllerGetOngoingOffersData`
 * @response `401` `void` Unauthorized
 * @response `default` `OngoingOffersResponse`
 */
offerControllerGetOngoingOffers = (query: OfferControllerGetOngoingOffersParams, reqParams: RequestParams = {}) =>
    this.request<OfferControllerGetOngoingOffersData, void | OngoingOffersResponse>({
      path: `/offers/ongoing-offers`,
      method: "GET",      query: query,      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Offers
 * @name OfferControllerGetOfferById
 * @request GET:/offers/offer{uid}
 * @secure
 * @response `200` `OfferControllerGetOfferByIdData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetOfferByIdResponse`
 */
offerControllerGetOfferById = (uid: number, reqParams: RequestParams = {}) =>
    this.request<OfferControllerGetOfferByIdData, void | GetOfferByIdResponse>({
      path: `/offers/offer${uid}`,
      method: "GET",      secure: true,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Offers
 * @name OfferControllerGetOfferByTitle
 * @request GET:/offers/offer/title/{title}
 * @secure
 * @response `200` `OfferControllerGetOfferByTitleData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetSimilarOffers`
 */
offerControllerGetOfferByTitle = (title: string, reqParams: RequestParams = {}) =>
    this.request<OfferControllerGetOfferByTitleData, void | GetSimilarOffers>({
      path: `/offers/offer/title/${title}`,
      method: "GET",      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Offers
 * @name OfferControllerSaveItem
 * @request POST:/offers/save
 * @secure
 * @response `201` `OfferControllerSaveItemData`
 * @response `401` `void` Unauthorized
 * @response `default` `SaveItemResponse`
 */
offerControllerSaveItem = (data: SaveOfferDto, reqParams: RequestParams = {}) =>
    this.request<OfferControllerSaveItemData, void | SaveItemResponse>({
      path: `/offers/save`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Offers
 * @name OfferControllerRemoveSavedItem
 * @request POST:/offers/remove
 * @secure
 * @response `201` `OfferControllerRemoveSavedItemData`
 * @response `401` `void` Unauthorized
 * @response `default` `SaveItemResponse`
 */
offerControllerRemoveSavedItem = (data: RemoveOfferDto, reqParams: RequestParams = {}) =>
    this.request<OfferControllerRemoveSavedItemData, void | SaveItemResponse>({
      path: `/offers/remove`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      ...reqParams,
    });

    }
    click = {
  
  /**
 * No description
 *
 * @tags Click
 * @name ClickControllerCreate
 * @request POST:/click
 * @secure
 * @response `201` `ClickControllerCreateData`
 * @response `401` `void` Unauthorized
 * @response `default` `ClickCreateResponse`
 */
clickControllerCreate = (data: CreateClickDto, reqParams: RequestParams = {}) =>
    this.request<ClickControllerCreateData, void | ClickCreateResponse>({
      path: `/click`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Click
 * @name ClickControllerGetClicks
 * @request GET:/click
 * @secure
 * @response `200` `ClickControllerGetClicksData`
 * @response `401` `void` Unauthorized
 * @response `default` `ClicksResponse`
 */
clickControllerGetClicks = (query: ClickControllerGetClicksParams, reqParams: RequestParams = {}) =>
    this.request<ClickControllerGetClicksData, void | ClicksResponse>({
      path: `/click`,
      method: "GET",      query: query,      secure: true,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Click
 * @name ClickControllerCreateNoAuth
 * @request POST:/click/no-auth
 * @response `201` `ClickControllerCreateNoAuthData`
 * @response `default` `ClickCreateResponse`
 */
clickControllerCreateNoAuth = (data: CreateClickDto, reqParams: RequestParams = {}) =>
    this.request<ClickControllerCreateNoAuthData, ClickCreateResponse>({
      path: `/click/no-auth`,
      method: "POST",      body: data,      type: ContentType.Json,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Click
 * @name ClickControllerGetClicksByStores
 * @request GET:/click/stores
 * @secure
 * @response `200` `ClickControllerGetClicksByStoresData`
 * @response `401` `void` Unauthorized
 * @response `default` `ClicksByStoreResponse`
 */
clickControllerGetClicksByStores = (query: ClickControllerGetClicksByStoresParams, reqParams: RequestParams = {}) =>
    this.request<ClickControllerGetClicksByStoresData, void | ClicksByStoreResponse>({
      path: `/click/stores`,
      method: "GET",      query: query,      secure: true,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Click
 * @name ClickControllerGetClickedStores
 * @request GET:/click/clicked_stores
 * @secure
 * @response `200` `ClickControllerGetClickedStoresData`
 * @response `401` `void` Unauthorized
 */
clickControllerGetClickedStores = (reqParams: RequestParams = {}) =>
    this.request<ClickControllerGetClickedStoresData, void>({
      path: `/click/clicked_stores`,
      method: "GET",      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Click
 * @name MissingCashbackControllerReportMissingCashback
 * @request POST:/click/missing-cashback/report
 * @secure
 * @response `201` `MissingCashbackControllerReportMissingCashbackData`
 * @response `401` `void` Unauthorized
 * @response `default` `string`
 */
missingCashbackControllerReportMissingCashback = (data: ReportMissingCashbackDto, reqParams: RequestParams = {}) =>
    this.request<MissingCashbackControllerReportMissingCashbackData, void | string>({
      path: `/click/missing-cashback/report`,
      method: "POST",      body: data,      secure: true,      type: ContentType.FormData,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Click
 * @name MissingCashbackControllerListMissingCashback
 * @request GET:/click/missing-cashback/list
 * @secure
 * @response `200` `MissingCashbackControllerListMissingCashbackData`
 * @response `401` `void` Unauthorized
 * @response `default` `MissingCashbackResponse`
 */
missingCashbackControllerListMissingCashback = (query: MissingCashbackControllerListMissingCashbackParams, reqParams: RequestParams = {}) =>
    this.request<MissingCashbackControllerListMissingCashbackData, void | MissingCashbackResponse>({
      path: `/click/missing-cashback/list`,
      method: "GET",      query: query,      secure: true,      ...reqParams,
    });

    }
    payment = {
  
  /**
 * No description
 *
 * @tags PaymentRequest
 * @name PaymentControllerRequestPayments
 * @request POST:/payment/withdraw
 * @secure
 * @response `201` `PaymentControllerRequestPaymentsData`
 * @response `401` `void` Unauthorized
 * @response `default` `boolean`
 */
paymentControllerRequestPayments = (data: PaymentRequestDto, reqParams: RequestParams = {}) =>
    this.request<PaymentControllerRequestPaymentsData, void | boolean>({
      path: `/payment/withdraw`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags PaymentRequest
 * @name PaymentControllerGetAllPaymentRequestedUser
 * @request GET:/payment
 * @secure
 * @response `200` `PaymentControllerGetAllPaymentRequestedUserData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetPaymentListResponse`
 */
paymentControllerGetAllPaymentRequestedUser = (query: PaymentControllerGetAllPaymentRequestedUserParams, reqParams: RequestParams = {}) =>
    this.request<PaymentControllerGetAllPaymentRequestedUserData, void | GetPaymentListResponse>({
      path: `/payment`,
      method: "GET",      query: query,      secure: true,      format: "json",      ...reqParams,
    });

    }
    savedItems = {
  
  /**
 * No description
 *
 * @tags SavedItems
 * @name SavedItemControllerGetAllSavedDeals
 * @request GET:/saved-items/deals
 * @secure
 * @response `200` `SavedItemControllerGetAllSavedDealsData`
 * @response `401` `void` Unauthorized
 * @response `default` `SavedDealsResponse`
 */
savedItemControllerGetAllSavedDeals = (query: SavedItemControllerGetAllSavedDealsParams, reqParams: RequestParams = {}) =>
    this.request<SavedItemControllerGetAllSavedDealsData, void | SavedDealsResponse>({
      path: `/saved-items/deals`,
      method: "GET",      query: query,      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags SavedItems
 * @name SavedItemControllerGetAllSavedOfferUids
 * @request GET:/saved-items/saved-offer-ids
 * @secure
 * @response `200` `SavedItemControllerGetAllSavedOfferUidsData`
 * @response `401` `void` Unauthorized
 * @response `default` `(number)[]`
 */
savedItemControllerGetAllSavedOfferUids = (reqParams: RequestParams = {}) =>
    this.request<SavedItemControllerGetAllSavedOfferUidsData, void | (number)[]>({
      path: `/saved-items/saved-offer-ids`,
      method: "GET",      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags SavedItems
 * @name SavedItemControllerGetAllSavedCoupons
 * @request GET:/saved-items/coupons
 * @secure
 * @response `200` `SavedItemControllerGetAllSavedCouponsData`
 * @response `401` `void` Unauthorized
 * @response `default` `SavedCouponsResponse`
 */
savedItemControllerGetAllSavedCoupons = (query: SavedItemControllerGetAllSavedCouponsParams, reqParams: RequestParams = {}) =>
    this.request<SavedItemControllerGetAllSavedCouponsData, void | SavedCouponsResponse>({
      path: `/saved-items/coupons`,
      method: "GET",      query: query,      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags SavedItems
 * @name SavedItemControllerGetAllSavedStores
 * @request GET:/saved-items/stores
 * @secure
 * @response `200` `SavedItemControllerGetAllSavedStoresData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetAllStoresResponse`
 */
savedItemControllerGetAllSavedStores = (query: SavedItemControllerGetAllSavedStoresParams, reqParams: RequestParams = {}) =>
    this.request<SavedItemControllerGetAllSavedStoresData, void | GetAllStoresResponse>({
      path: `/saved-items/stores`,
      method: "GET",      query: query,      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags SavedItems
 * @name SavedItemControllerGetAllSavedGiftCards
 * @request GET:/saved-items/gift-cards
 * @secure
 * @response `200` `SavedItemControllerGetAllSavedGiftCardsData`
 * @response `401` `void` Unauthorized
 * @response `default` `GetGiftCardListResponse`
 */
savedItemControllerGetAllSavedGiftCards = (query: SavedItemControllerGetAllSavedGiftCardsParams, reqParams: RequestParams = {}) =>
    this.request<SavedItemControllerGetAllSavedGiftCardsData, void | GetGiftCardListResponse>({
      path: `/saved-items/gift-cards`,
      method: "GET",      query: query,      secure: true,      format: "json",      ...reqParams,
    });

    }
    links = {
  
  /**
 * No description
 *
 * @tags Links
 * @name LinkControllerGenerateLink
 * @request POST:/links/generate
 * @secure
 * @response `201` `LinkControllerGenerateLinkData`
 * @response `401` `void` Unauthorized
 * @response `default` `LinkResponseDto`
 */
linkControllerGenerateLink = (data: GenerateLinkDto, reqParams: RequestParams = {}) =>
    this.request<LinkControllerGenerateLinkData, void | LinkResponseDto>({
      path: `/links/generate`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Links
 * @name LinkControllerProcessMultipleLinks
 * @request POST:/links/generate-multi
 * @secure
 * @response `201` `LinkControllerProcessMultipleLinksData`
 * @response `401` `void` Unauthorized
 * @response `default` `MultiLinkResponseDto`
 */
linkControllerProcessMultipleLinks = (data: GenerateMultiLinkDto, reqParams: RequestParams = {}) =>
    this.request<LinkControllerProcessMultipleLinksData, void | MultiLinkResponseDto>({
      path: `/links/generate-multi`,
      method: "POST",      body: data,      secure: true,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Links
 * @name LinkControllerGetUserLinks
 * @request GET:/links
 * @secure
 * @response `200` `LinkControllerGetUserLinksData`
 * @response `401` `void` Unauthorized
 * @response `default` `PaginationResponseDto`
 */
linkControllerGetUserLinks = (query: LinkControllerGetUserLinksParams, reqParams: RequestParams = {}) =>
    this.request<LinkControllerGetUserLinksData, void | PaginationResponseDto>({
      path: `/links`,
      method: "GET",      query: query,      secure: true,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Links
 * @name LinkControllerGetUserAnalytics
 * @request GET:/links/analytics
 * @secure
 * @response `200` `LinkControllerGetUserAnalyticsData`
 * @response `401` `void` Unauthorized
 * @response `default` `UserAnalyticsResponseDto`
 */
linkControllerGetUserAnalytics = (query: LinkControllerGetUserAnalyticsParams, reqParams: RequestParams = {}) =>
    this.request<LinkControllerGetUserAnalyticsData, void | UserAnalyticsResponseDto>({
      path: `/links/analytics`,
      method: "GET",      query: query,      secure: true,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Links
 * @name LinkControllerGetLinkDetails
 * @request GET:/links/{linkId}
 * @response `200` `LinkControllerGetLinkDetailsData`
 * @response `default` `void` Redirect to the original URL
 */
linkControllerGetLinkDetails = (linkId: string, reqParams: RequestParams = {}) =>
    this.request<LinkControllerGetLinkDetailsData, void>({
      path: `/links/${linkId}`,
      method: "GET",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Links
 * @name LinkControllerGetShareAndEarnOffers
 * @request GET:/links/share-and-earn/offers
 * @secure
 * @response `200` `LinkControllerGetShareAndEarnOffersData`
 * @response `401` `void` Unauthorized
 * @response `default` `(ShareAndEarnOfferDto)[]` Get share and earn offers with short URLs
 */
linkControllerGetShareAndEarnOffers = (reqParams: RequestParams = {}) =>
    this.request<LinkControllerGetShareAndEarnOffersData, void | (ShareAndEarnOfferDto)[]>({
      path: `/links/share-and-earn/offers`,
      method: "GET",      secure: true,      format: "json",      ...reqParams,
    });

    }
    sitemap = {
  
  /**
 * No description
 *
 * @tags Sitemap
 * @name SitemapControllerGenerateSitemap
 * @request GET:/sitemap
 * @response `200` `SitemapControllerGenerateSitemapData`
 */
sitemapControllerGenerateSitemap = (reqParams: RequestParams = {}) =>
    this.request<SitemapControllerGenerateSitemapData, any>({
      path: `/sitemap`,
      method: "GET",      format: "json",      ...reqParams,
    });

    }
    bot = {
  
  /**
 * No description
 *
 * @tags Bot
 * @name BotControllerSendMessage
 * @request POST:/bot/send-message
 * @response `200` `BotControllerSendMessageData` Message sent successfully
 * @response `201` `SendMessageResponseDto`
 * @response `400` `void` Invalid request parameters
 */
botControllerSendMessage = (data: SendMessageDto, reqParams: RequestParams = {}) =>
    this.request<BotControllerSendMessageData, void>({
      path: `/bot/send-message`,
      method: "POST",      body: data,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

    }
    chat = {
  
  /**
 * No description
 *
 * @tags Chat
 * @name ChatControllerChat
 * @summary Send a chat message and receive complete response
 * @request POST:/chat
 * @response `200` `ChatControllerChatData` Chat response
 * @response `201` `ChatResponseDto`
 * @response `400` `void` Invalid request
 * @response `500` `void` Internal server error
 */
chatControllerChat = (data: ChatRequestDto, reqParams: RequestParams = {}) =>
    this.request<ChatControllerChatData, void>({
      path: `/chat`,
      method: "POST",      body: data,      type: ContentType.Json,      format: "json",      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Chat
 * @name ChatControllerEmbedFile
 * @summary Upload and embed a TXT file for context
 * @request POST:/chat/embed
 * @response `200` `ChatControllerEmbedFileData`
 * @response `201` `EmbedResponseDto`
 * @response `400` `void` Invalid file or processing error
 */
chatControllerEmbedFile = (reqParams: RequestParams = {}) =>
    this.request<ChatControllerEmbedFileData, void>({
      path: `/chat/embed`,
      method: "POST",      format: "json",      ...reqParams,
    });

    }
    card = {
  
  /**
 * No description
 *
 * @tags WishList
 * @name WishListControllerJoinWishlist
 * @request POST:/card/wishlist/join
 * @response `201` `WishListControllerJoinWishlistData`
 * @response `default` `WishlistResponseDto`
 */
wishListControllerJoinWishlist = (data: WishlistEmailDto, reqParams: RequestParams = {}) =>
    this.request<WishListControllerJoinWishlistData, WishlistResponseDto>({
      path: `/card/wishlist/join`,
      method: "POST",      body: data,      type: ContentType.Json,      ...reqParams,
    });

  
  /**
 * No description
 *
 * @tags Card Auth
 * @name CardAuthControllerCardLogin
 * @request POST:/card/auth/login
 * @response `201` `CardAuthControllerCardLoginData`
 * @response `default` `CardLoginResponseDto`
 */
cardAuthControllerCardLogin = (data: CardLoginDto, reqParams: RequestParams = {}) =>
    this.request<CardAuthControllerCardLoginData, CardLoginResponseDto>({
      path: `/card/auth/login`,
      method: "POST",      body: data,      type: ContentType.Json,      ...reqParams,
    });

    }
  }

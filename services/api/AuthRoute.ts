/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import {
  AuthControllerCheckData,
  AuthControllerCreateData,
  AuthControllerGetCsrfTokenData,
  AuthControllerGoogleAuthCallbackData,
  AuthControllerGoogleAuthData,
  AuthControllerLoginData,
  AuthControllerLogoutData,
  AuthControllerResendOtpData,
  AuthControllerVerifyOtpData,
  AuthControllerVerifyTokenData,
  BannerControllerGetBannersData,
  BotControllerSendMessageData,
  CardAuthControllerCardLoginData,
  CardLoginDto,
  CashbackSortTypes,
  CategoryControllerGetAllCategoriesData,
  CategoryControllerGetAllCategoriesDetailsData,
  CategoryControllerGetSubCategoryByCategoryIdData,
  CategoryControllerGetSubCategorySearchData,
  ChatControllerChatData,
  ChatControllerEmbedFileData,
  ChatRequestDto,
  ClickControllerCreateData,
  ClickControllerCreateNoAuthData,
  ClickControllerGetClickedStoresData,
  ClickControllerGetClicksByStoresData,
  ClickControllerGetClicksData,
  CreateClickDto,
  CreateReviewDto,
  CreateUserDto,
  GenerateLinkDto,
  GenerateMultiLinkDto,
  GiftCardControllerGetAllGiftCardBannersData,
  GiftCardControllerGetAllGiftCardsData,
  GiftCardControllerGetGiftCardDetailsData,
  GiftCardControllerGetIcbCardData,
  GiftCardControllerOrderGiftCardData,
  GiftCardControllerRedeemGiftCardHistoryData,
  GiftCardControllerRedeemIcbGiftCardData,
  GiftCardControllerRemoveSavedItemData,
  GiftCardControllerSaveItemData,
  GiftCardControllerVerifyPaymentData,
  LinkControllerGenerateLinkData,
  LinkControllerGetLinkDetailsData,
  LinkControllerGetShareAndEarnOffersData,
  LinkControllerGetUserAnalyticsData,
  LinkControllerGetUserAnalyticsParams1PeriodTypeEnum,
  LinkControllerGetUserLinksData,
  LinkControllerProcessMultipleLinksData,
  LoginDto,
  MissingCashbackControllerListMissingCashbackData,
  MissingCashbackControllerReportMissingCashbackData,
  OfferControllerGetAllCategoriesData,
  OfferControllerGetOfferByIdData,
  OfferControllerGetOfferByTitleData,
  OfferControllerGetOngoingOffersData,
  OfferControllerRemoveSavedItemData,
  OfferControllerSaveItemData,
  OfferTypes,
  OffersControllerGetLandingOffersData,
  OffersControllerGetOnGoingSaleOffersData,
  OrderGiftCardDto,
  OtpDto,
  PaymentControllerGetAllPaymentRequestedUserData,
  PaymentControllerRequestPaymentsData,
  PaymentRequestDto,
  PaymentSortTypes,
  PaymentTypes,
  PaymentVerifyDto,
  QuickAccessControllerGetHeroQuickAccessesData,
  QuickAccessControllerGetQuickAccessesData,
  RedeemHistoryTypes,
  RedeemIcbGiftCardDto,
  ReferralCampaignControllerGetReferralEarningsLeaderboardData,
  ReferralCampaignControllerGetReferralEarningsLeaderboardParams1TimeFrameEnum,
  ReferralTypes,
  RemoveOfferDto,
  ReportMissingCashbackDto,
  ReviewControllerCreateData,
  ReviewControllerGetAllGiftCardsData,
  ReviewTypes,
  SaveOfferDto,
  SavedItemControllerGetAllSavedCouponsData,
  SavedItemControllerGetAllSavedDealsData,
  SavedItemControllerGetAllSavedGiftCardsData,
  SavedItemControllerGetAllSavedOfferUidsData,
  SavedItemControllerGetAllSavedStoresData,
  SavedItemControllerGetAllSavedStoresParams1CbPercentEnum,
  SearchControllerGetSearchResultsData,
  SendMessageDto,
  SitemapControllerGenerateSitemapData,
  SortTypes,
  StoreControllerGetAllStoresData,
  StoreControllerGetAllStoresParams1CbPercentEnum,
  StoreControllerGetCashbackRatesByStoreIdData,
  StoreControllerGetStoreDetailsByNameData,
  StoreControllerRemoveSavedItemData,
  StoreControllerSaveItemData,
  StoresControllerGetAllCategoriesData,
  StoresControllerGetContextStoresByCbData,
  StoriesControllerClickStoryData,
  StoriesControllerGetAllStoriesAnalyticsData,
  StoriesControllerGetStoriesData,
  StoriesControllerGetStoryAnalyticsData,
  TermsAndPrivacyControllerGetAllTermsAndConditionsData,
  TestimonialControllerGetAllTestimonialsData,
  UpdateBankAccountDto,
  UpdateCredentialsDto,
  UpdateProfileWithImageDto,
  UpdateUpiDto,
  UserControllerGetAllPersonalInterestDataData,
  UserControllerGetBankDetailsData,
  UserControllerGetCashbackHistoryData,
  UserControllerGetCashbackHistoryParams1StatusEnum,
  UserControllerGetProfileDetailsData,
  UserControllerGetUsersByReferralCodeData,
  UserControllerGetUsersByReferralCodeParams1StatusEnum,
  UserControllerGetUsersOverViewDetailsData,
  UserControllerSendOtpToUpdateUserCredentialsData,
  UserControllerUpdateAllUsersReferralCodeData,
  UserControllerUpdateBankDetailsData,
  UserControllerUpdateProfileData,
  UserControllerUpdateUpiIdData,
  UserControllerVerifyOtpToUpdateCredentialsData,
  UserTypes,
  VerifyUserDto,
  WishListControllerJoinWishlistData,
  WishlistEmailDto,
} from "./data-contracts";

export namespace Context {
  /**
   * No description
   * @tags Context
   * @name BannerControllerGetBanners
   * @request GET:/context/banner
   * @response `200` `BannerControllerGetBannersData`
   * @response `default` `BannerResponse`
   */
  export namespace BannerControllerGetBanners {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = BannerControllerGetBannersData;
  }

  /**
   * No description
   * @tags Context
   * @name QuickAccessControllerGetQuickAccesses
   * @request GET:/context/quick-access
   * @response `200` `QuickAccessControllerGetQuickAccessesData`
   * @response `default` `(QuickAccessResponseItem)[]`
   */
  export namespace QuickAccessControllerGetQuickAccesses {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = QuickAccessControllerGetQuickAccessesData;
  }

  /**
   * No description
   * @tags Context
   * @name QuickAccessControllerGetHeroQuickAccesses
   * @request GET:/context/quick-access/hero
   * @response `200` `QuickAccessControllerGetHeroQuickAccessesData`
   * @response `default` `(HeroResponseItem)[]`
   */
  export namespace QuickAccessControllerGetHeroQuickAccesses {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = QuickAccessControllerGetHeroQuickAccessesData;
  }

  /**
   * @description Returns stories grouped by store. For authenticated users, generates cached short URLs. For public users, uses original redirect URLs as short URLs.
   * @tags Context
   * @name StoriesControllerGetStories
   * @summary Get stories for mobile app
   * @request GET:/context/stories
   * @secure
   * @response `200` `StoriesControllerGetStoriesData`
   * @response `401` `void` Unauthorized
   * @response `default` `(ResponseMobileStories)[]`
   */
  export namespace StoriesControllerGetStories {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = StoriesControllerGetStoriesData;
  }

  /**
   * No description
   * @tags Context
   * @name StoriesControllerClickStory
   * @request GET:/context/stories/click/{storyId}
   * @response `200` `StoriesControllerClickStoryData`
   */
  export namespace StoriesControllerClickStory {
    export type RequestParams = {
      storyId: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = StoriesControllerClickStoryData;
  }

  /**
   * No description
   * @tags Context
   * @name StoriesControllerGetStoryAnalytics
   * @request GET:/context/stories/analytics/{storyId}
   * @response `200` `StoriesControllerGetStoryAnalyticsData`
   */
  export namespace StoriesControllerGetStoryAnalytics {
    export type RequestParams = {
      storyId: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = StoriesControllerGetStoryAnalyticsData;
  }

  /**
   * No description
   * @tags Context
   * @name StoriesControllerGetAllStoriesAnalytics
   * @request GET:/context/stories/analytics
   * @response `200` `StoriesControllerGetAllStoriesAnalyticsData`
   */
  export namespace StoriesControllerGetAllStoriesAnalytics {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = StoriesControllerGetAllStoriesAnalyticsData;
  }

  /**
   * No description
   * @tags Context
   * @name OffersControllerGetLandingOffers
   * @request GET:/context/offers
   * @secure
   * @response `200` `OffersControllerGetLandingOffersData`
   * @response `401` `void` Unauthorized
   * @response `default` `CategorizedOffers`
   */
  export namespace OffersControllerGetLandingOffers {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = OffersControllerGetLandingOffersData;
  }

  /**
   * No description
   * @tags Context
   * @name OffersControllerGetOnGoingSaleOffers
   * @request GET:/context/offers/all-on-going-offers
   * @response `200` `OffersControllerGetOnGoingSaleOffersData`
   * @response `default` `(GetAllOnGoingOffersResponse)[]`
   */
  export namespace OffersControllerGetOnGoingSaleOffers {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = OffersControllerGetOnGoingSaleOffersData;
  }

  /**
   * No description
   * @tags Context
   * @name CategoryControllerGetAllCategories
   * @request GET:/context/category
   * @response `200` `CategoryControllerGetAllCategoriesData`
   * @response `default` `(CategoryResponse)[]`
   */
  export namespace CategoryControllerGetAllCategories {
    export type RequestParams = {};
    export type RequestQuery = {
      trending: boolean;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = CategoryControllerGetAllCategoriesData;
  }

  /**
   * No description
   * @tags Context
   * @name CategoryControllerGetSubCategoryByCategoryId
   * @request GET:/context/category/sub-category{id}
   * @secure
   * @response `200` `CategoryControllerGetSubCategoryByCategoryIdData`
   * @response `401` `void` Unauthorized
   * @response `default` `SubCategoriesByCategoryResponse`
   */
  export namespace CategoryControllerGetSubCategoryByCategoryId {
    export type RequestParams = {
      id: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = CategoryControllerGetSubCategoryByCategoryIdData;
  }

  /**
   * No description
   * @tags Context
   * @name CategoryControllerGetSubCategorySearch
   * @request GET:/context/category/search{value}
   * @secure
   * @response `200` `CategoryControllerGetSubCategorySearchData`
   * @response `401` `void` Unauthorized
   * @response `default` `(SubCategoriesByCategory)[]`
   */
  export namespace CategoryControllerGetSubCategorySearch {
    export type RequestParams = {
      value: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = CategoryControllerGetSubCategorySearchData;
  }

  /**
   * No description
   * @tags Context
   * @name CategoryControllerGetAllCategoriesDetails
   * @request GET:/context/category/all-categories-details
   * @response `200` `CategoryControllerGetAllCategoriesDetailsData`
   * @response `default` `(AllCategoriesResponse)[]`
   */
  export namespace CategoryControllerGetAllCategoriesDetails {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = CategoryControllerGetAllCategoriesDetailsData;
  }

  /**
   * No description
   * @tags Context
   * @name StoresControllerGetContextStoresByCb
   * @request GET:/context/stores-by-cb-percent
   * @secure
   * @response `200` `StoresControllerGetContextStoresByCbData`
   * @response `401` `void` Unauthorized
   * @response `default` `StoresByCbContextResponse`
   */
  export namespace StoresControllerGetContextStoresByCb {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = StoresControllerGetContextStoresByCbData;
  }

  /**
   * No description
   * @tags Context
   * @name StoresControllerGetAllCategories
   * @request GET:/context/stores-by-cb-percent/find-by-category
   * @response `200` `StoresControllerGetAllCategoriesData`
   * @response `default` `(CategoryStoresResponse)[]`
   */
  export namespace StoresControllerGetAllCategories {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = StoresControllerGetAllCategoriesData;
  }

  /**
   * No description
   * @tags Context
   * @name SearchControllerGetSearchResults
   * @request GET:/context/search
   * @response `200` `SearchControllerGetSearchResultsData`
   * @response `default` `SearchResponseItem`
   */
  export namespace SearchControllerGetSearchResults {
    export type RequestParams = {};
    export type RequestQuery = {
      text: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = SearchControllerGetSearchResultsData;
  }

  /**
   * No description
   * @tags Context
   * @name TermsAndPrivacyControllerGetAllTermsAndConditions
   * @request GET:/context/terms-and-privacy
   * @response `200` `TermsAndPrivacyControllerGetAllTermsAndConditionsData`
   * @response `default` `TermsAndPrivacyResponseItem`
   */
  export namespace TermsAndPrivacyControllerGetAllTermsAndConditions {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @example "terms" */
      type: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody =
      TermsAndPrivacyControllerGetAllTermsAndConditionsData;
  }

  /**
   * No description
   * @tags Context
   * @name TestimonialControllerGetAllTestimonials
   * @request GET:/context/testimonials
   * @response `200` `TestimonialControllerGetAllTestimonialsData`
   * @response `default` `(TestimonialResponseType)[]`
   */
  export namespace TestimonialControllerGetAllTestimonials {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = TestimonialControllerGetAllTestimonialsData;
  }
}

export namespace Auth {
  /**
   * No description
   * @tags Auth
   * @name AuthControllerCreate
   * @request POST:/auth/register
   * @response `201` `AuthControllerCreateData`
   * @response `default` `UserResponse`
   */
  export namespace AuthControllerCreate {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = CreateUserDto;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerCreateData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerLogin
   * @request POST:/auth/login
   * @response `201` `AuthControllerLoginData`
   * @response `default` `LoginResponse`
   */
  export namespace AuthControllerLogin {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = LoginDto;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerLoginData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerVerifyOtp
   * @request POST:/auth/verify-user
   * @response `201` `AuthControllerVerifyOtpData`
   * @response `default` `boolean`
   */
  export namespace AuthControllerVerifyOtp {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = VerifyUserDto;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerVerifyOtpData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerResendOtp
   * @request POST:/auth/resend-otp
   * @response `201` `AuthControllerResendOtpData`
   * @response `default` `LoginResponse`
   */
  export namespace AuthControllerResendOtp {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = LoginDto;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerResendOtpData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerLogout
   * @request DELETE:/auth/logout
   * @secure
   * @response `200` `AuthControllerLogoutData`
   * @response `401` `void` Unauthorized
   */
  export namespace AuthControllerLogout {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerLogoutData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerCheck
   * @request GET:/auth/check
   * @secure
   * @response `200` `AuthControllerCheckData`
   * @response `401` `void` Unauthorized
   */
  export namespace AuthControllerCheck {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerCheckData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerGetCsrfToken
   * @request GET:/auth/token
   * @response `200` `AuthControllerGetCsrfTokenData`
   */
  export namespace AuthControllerGetCsrfToken {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerGetCsrfTokenData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerGoogleAuth
   * @request GET:/auth/google
   * @response `200` `AuthControllerGoogleAuthData`
   * @response `default` `void` Initiates Google OAuth authentication flow
   */
  export namespace AuthControllerGoogleAuth {
    export type RequestParams = {};
    export type RequestQuery = {
      referralCode: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerGoogleAuthData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerGoogleAuthCallback
   * @request GET:/auth/google/callback
   * @response `200` `AuthControllerGoogleAuthCallbackData`
   * @response `default` `void` Handles the callback from Google OAuth authentication
   */
  export namespace AuthControllerGoogleAuthCallback {
    export type RequestParams = {};
    export type RequestQuery = {
      redirect: string;
      state: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerGoogleAuthCallbackData;
  }

  /**
   * No description
   * @tags Auth
   * @name AuthControllerVerifyToken
   * @request GET:/auth/verify-token
   * @response `200` `AuthControllerVerifyTokenData`
   */
  export namespace AuthControllerVerifyToken {
    export type RequestParams = {};
    export type RequestQuery = {
      token: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = AuthControllerVerifyTokenData;
  }
}

export namespace Users {
  /**
   * No description
   * @tags Users
   * @name UserControllerGetBankDetails
   * @request GET:/users/get-bank-details
   * @secure
   * @response `200` `UserControllerGetBankDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetBankAccountDataResponse`
   */
  export namespace UserControllerGetBankDetails {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerGetBankDetailsData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerUpdateBankDetails
   * @request POST:/users/update-bank-details
   * @secure
   * @response `201` `UserControllerUpdateBankDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `boolean`
   */
  export namespace UserControllerUpdateBankDetails {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = UpdateBankAccountDto;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerUpdateBankDetailsData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerUpdateUpiId
   * @request PATCH:/users/update-upi
   * @secure
   * @response `200` `UserControllerUpdateUpiIdData`
   * @response `401` `void` Unauthorized
   * @response `default` `boolean`
   */
  export namespace UserControllerUpdateUpiId {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = UpdateUpiDto;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerUpdateUpiIdData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerGetProfileDetails
   * @request GET:/users/me
   * @secure
   * @response `200` `UserControllerGetProfileDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetUserProfileResponseItem`
   */
  export namespace UserControllerGetProfileDetails {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerGetProfileDetailsData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerGetUsersOverViewDetails
   * @request GET:/users/overview
   * @secure
   * @response `200` `UserControllerGetUsersOverViewDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `UserOverviewResponse`
   */
  export namespace UserControllerGetUsersOverViewDetails {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerGetUsersOverViewDetailsData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerGetUsersByReferralCode
   * @request GET:/users/referral-history
   * @secure
   * @response `200` `UserControllerGetUsersByReferralCodeData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetUsersByReferralCodeResponse`
   */
  export namespace UserControllerGetUsersByReferralCode {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @example "2021-10-10" */
      endDate?: string;
      /** @example "a" */
      searchParam?: string;
      sortType?: ReferralTypes;
      /** @example "2021-10-10" */
      startDate?: string;
      /** @default "active" */
      status?: UserControllerGetUsersByReferralCodeParams1StatusEnum;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerGetUsersByReferralCodeData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerGetCashbackHistory
   * @request GET:/users/cashback-history
   * @secure
   * @response `200` `UserControllerGetCashbackHistoryData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetCbHistoryResponse`
   */
  export namespace UserControllerGetCashbackHistory {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @default "2024-12-31" */
      endDate: string;
      /**
       * Maximum cashback amount filter
       * @example "1000.00"
       */
      maxAmount?: number;
      /**
       * Minimum cashback amount filter
       * @example "10.00"
       */
      minAmount?: number;
      /**
       * Filter by reward type (comma-separated values: cashback,rewardpoint)
       * @example "cashback,rewardpoint"
       */
      rewardType?: string;
      /** @example "a" */
      searchParam: string;
      sortType?: CashbackSortTypes;
      /** @default "2023-10-01" */
      startDate: string;
      /** @example "cancelled,pending,confirmed" */
      status?: UserControllerGetCashbackHistoryParams1StatusEnum;
      /** @example "1,2,3" */
      stores?: string;
      /**
       * Filter by earnings type (comma-separated values: click,missing,referral,share)
       * @example "click,missing,referral"
       */
      type?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerGetCashbackHistoryData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerUpdateProfile
   * @request POST:/users/update-profile
   * @secure
   * @response `201` `UserControllerUpdateProfileData`
   * @response `401` `void` Unauthorized
   * @response `default` `boolean`
   */
  export namespace UserControllerUpdateProfile {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = UpdateProfileWithImageDto;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerUpdateProfileData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerSendOtpToUpdateUserCredentials
   * @request POST:/users/update-credentials
   * @secure
   * @response `201` `UserControllerSendOtpToUpdateUserCredentialsData`
   * @response `401` `void` Unauthorized
   */
  export namespace UserControllerSendOtpToUpdateUserCredentials {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = UpdateCredentialsDto;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerSendOtpToUpdateUserCredentialsData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerVerifyOtpToUpdateCredentials
   * @request POST:/users/verify-credentials-otp
   * @secure
   * @response `201` `UserControllerVerifyOtpToUpdateCredentialsData`
   * @response `401` `void` Unauthorized
   */
  export namespace UserControllerVerifyOtpToUpdateCredentials {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = OtpDto;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerVerifyOtpToUpdateCredentialsData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerGetAllPersonalInterestData
   * @request GET:/users/get-personal-interest
   * @response `200` `UserControllerGetAllPersonalInterestDataData`
   */
  export namespace UserControllerGetAllPersonalInterestData {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerGetAllPersonalInterestDataData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerUpdateAllUsersReferralCode
   * @request POST:/users/update-referral-code
   * @response `201` `UserControllerUpdateAllUsersReferralCodeData`
   */
  export namespace UserControllerUpdateAllUsersReferralCode {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = UserControllerUpdateAllUsersReferralCodeData;
  }

  /**
   * No description
   * @tags Users
   * @name UserControllerListUsers
   * @request GET:/users/list
   * @response `default` `UserListResponseDto`
   */
  export namespace UserControllerListUsers {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = any;
  }
}

export namespace Campaign {
  /**
   * No description
   * @tags ReferralCampaign
   * @name ReferralCampaignControllerGetReferralEarningsLeaderboard
   * @request GET:/campaign/referral-earnings-leaderboard
   * @response `200` `ReferralCampaignControllerGetReferralEarningsLeaderboardData`
   * @response `default` `(GetReferralLeaderboardResponse)[]`
   */
  export namespace ReferralCampaignControllerGetReferralEarningsLeaderboard {
    export type RequestParams = {};
    export type RequestQuery = {
      timeFrame: ReferralCampaignControllerGetReferralEarningsLeaderboardParams1TimeFrameEnum;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody =
      ReferralCampaignControllerGetReferralEarningsLeaderboardData;
  }
}

export namespace Stores {
  /**
   * No description
   * @tags Stores
   * @name StoreControllerGetAllStores
   * @request GET:/stores
   * @secure
   * @response `200` `StoreControllerGetAllStoresData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetAllStoresResponse`
   */
  export namespace StoreControllerGetAllStores {
    export type RequestParams = {};
    export type RequestQuery = {
      /**
       * Filter stores by cashback percentage tier
       * @example 50
       */
      cbPercent?: StoreControllerGetAllStoresParams1CbPercentEnum;
      /** @example "60" */
      maxPercent: number;
      /** @example "10" */
      minPercent: number;
      saved: any;
      /** @example "and" */
      searchParam: string;
      sortType: SortTypes;
      /** @example "2,4,6,7" */
      subCategories?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = StoreControllerGetAllStoresData;
  }

  /**
   * No description
   * @tags Stores
   * @name StoreControllerGetStoreDetailsByName
   * @request GET:/stores/store-details{name}
   * @secure
   * @response `200` `StoreControllerGetStoreDetailsByNameData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetStoreDetailsResponse`
   */
  export namespace StoreControllerGetStoreDetailsByName {
    export type RequestParams = {
      name: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = StoreControllerGetStoreDetailsByNameData;
  }

  /**
   * No description
   * @tags Stores
   * @name StoreControllerGetCashbackRatesByStoreId
   * @request GET:/stores/cashback-rates-by-store{id}
   * @response `200` `StoreControllerGetCashbackRatesByStoreIdData`
   * @response `default` `GetCashbackRatesByStoreResponse`
   */
  export namespace StoreControllerGetCashbackRatesByStoreId {
    export type RequestParams = {
      id: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = StoreControllerGetCashbackRatesByStoreIdData;
  }

  /**
   * No description
   * @tags Stores
   * @name StoreControllerSaveItem
   * @request POST:/stores/save
   * @secure
   * @response `201` `StoreControllerSaveItemData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  export namespace StoreControllerSaveItem {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = SaveOfferDto;
    export type RequestHeaders = {};
    export type ResponseBody = StoreControllerSaveItemData;
  }

  /**
   * No description
   * @tags Stores
   * @name StoreControllerRemoveSavedItem
   * @request POST:/stores/remove
   * @secure
   * @response `201` `StoreControllerRemoveSavedItemData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  export namespace StoreControllerRemoveSavedItem {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = RemoveOfferDto;
    export type RequestHeaders = {};
    export type ResponseBody = StoreControllerRemoveSavedItemData;
  }

  /**
   * No description
   * @tags Review
   * @name ReviewControllerCreate
   * @request POST:/stores/review/add-review
   * @secure
   * @response `201` `ReviewControllerCreateData`
   * @response `401` `void` Unauthorized
   * @response `default` `CreateReviewResponse`
   */
  export namespace ReviewControllerCreate {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = CreateReviewDto;
    export type RequestHeaders = {};
    export type ResponseBody = ReviewControllerCreateData;
  }

  /**
   * No description
   * @tags Review
   * @name ReviewControllerGetAllGiftCards
   * @request GET:/stores/review
   * @response `200` `ReviewControllerGetAllGiftCardsData`
   * @response `default` `GetAllReviewsResponse`
   */
  export namespace ReviewControllerGetAllGiftCards {
    export type RequestParams = {};
    export type RequestQuery = {
      sortType: ReviewTypes;
      /** @example "65eef6a14d2e4417e86b9ce5" */
      storeId: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = ReviewControllerGetAllGiftCardsData;
  }
}

export namespace GiftCards {
  /**
   * No description
   * @tags GiftCards
   * @name GiftCardControllerGetAllGiftCards
   * @request GET:/gift-cards
   * @secure
   * @response `200` `GiftCardControllerGetAllGiftCardsData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetGiftCardListResponse`
   */
  export namespace GiftCardControllerGetAllGiftCards {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @example "a" */
      searchParam: string;
      sortType?: SortTypes;
      /** @example "2,4,6,7" */
      subCategories?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = GiftCardControllerGetAllGiftCardsData;
  }

  /**
   * No description
   * @tags GiftCards
   * @name GiftCardControllerGetAllGiftCardBanners
   * @request GET:/gift-cards/banners
   * @response `200` `GiftCardControllerGetAllGiftCardBannersData`
   * @response `default` `GiftCardBannersResponse`
   */
  export namespace GiftCardControllerGetAllGiftCardBanners {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = GiftCardControllerGetAllGiftCardBannersData;
  }

  /**
   * No description
   * @tags GiftCards
   * @name GiftCardControllerGetGiftCardDetails
   * @request GET:/gift-cards/gift-card{id}
   * @secure
   * @response `200` `GiftCardControllerGetGiftCardDetailsData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetGiftCardResponse`
   */
  export namespace GiftCardControllerGetGiftCardDetails {
    export type RequestParams = {
      id: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = GiftCardControllerGetGiftCardDetailsData;
  }

  /**
   * No description
   * @tags GiftCards
   * @name GiftCardControllerSaveItem
   * @request POST:/gift-cards/save
   * @secure
   * @response `201` `GiftCardControllerSaveItemData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  export namespace GiftCardControllerSaveItem {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = SaveOfferDto;
    export type RequestHeaders = {};
    export type ResponseBody = GiftCardControllerSaveItemData;
  }

  /**
   * No description
   * @tags GiftCards
   * @name GiftCardControllerRemoveSavedItem
   * @request POST:/gift-cards/remove
   * @secure
   * @response `201` `GiftCardControllerRemoveSavedItemData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  export namespace GiftCardControllerRemoveSavedItem {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = RemoveOfferDto;
    export type RequestHeaders = {};
    export type ResponseBody = GiftCardControllerRemoveSavedItemData;
  }

  /**
   * No description
   * @tags GiftCards
   * @name GiftCardControllerOrderGiftCard
   * @request POST:/gift-cards/create-order
   * @secure
   * @response `201` `GiftCardControllerOrderGiftCardData`
   * @response `401` `void` Unauthorized
   * @response `default` `OrderResponse`
   */
  export namespace GiftCardControllerOrderGiftCard {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = OrderGiftCardDto;
    export type RequestHeaders = {};
    export type ResponseBody = GiftCardControllerOrderGiftCardData;
  }

  /**
   * No description
   * @tags GiftCards
   * @name GiftCardControllerVerifyPayment
   * @request PUT:/gift-cards/verify-payment
   * @secure
   * @response `200` `GiftCardControllerVerifyPaymentData`
   * @response `401` `void` Unauthorized
   */
  export namespace GiftCardControllerVerifyPayment {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = PaymentVerifyDto;
    export type RequestHeaders = {};
    export type ResponseBody = GiftCardControllerVerifyPaymentData;
  }

  /**
   * No description
   * @tags GiftCards
   * @name GiftCardControllerRedeemIcbGiftCard
   * @request POST:/gift-cards/redeem
   * @secure
   * @response `201` `GiftCardControllerRedeemIcbGiftCardData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  export namespace GiftCardControllerRedeemIcbGiftCard {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = RedeemIcbGiftCardDto;
    export type RequestHeaders = {};
    export type ResponseBody = GiftCardControllerRedeemIcbGiftCardData;
  }

  /**
   * No description
   * @tags GiftCards
   * @name GiftCardControllerRedeemGiftCardHistory
   * @request GET:/gift-cards/redeem
   * @secure
   * @response `200` `GiftCardControllerRedeemGiftCardHistoryData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetRedeemGiftCardListResponse`
   */
  export namespace GiftCardControllerRedeemGiftCardHistory {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @default "2024-02-25T18:19:26.808Z" */
      date: string;
      /** @example "ICB123456" */
      searchParam: string;
      sortType?: RedeemHistoryTypes;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = GiftCardControllerRedeemGiftCardHistoryData;
  }

  /**
   * No description
   * @tags GiftCards
   * @name GiftCardControllerGetIcbCard
   * @request GET:/gift-cards/icb-card
   * @response `200` `GiftCardControllerGetIcbCardData`
   * @response `default` `IcbCardTypeResponse`
   */
  export namespace GiftCardControllerGetIcbCard {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = GiftCardControllerGetIcbCardData;
  }
}

export namespace Offers {
  /**
   * No description
   * @tags Offers
   * @name OfferControllerGetAllCategories
   * @request GET:/offers/deals-and-coupons
   * @secure
   * @response `200` `OfferControllerGetAllCategoriesData`
   * @response `401` `void` Unauthorized
   * @response `default` `DealAndCouponsResponse`
   */
  export namespace OfferControllerGetAllCategories {
    export type RequestParams = {};
    export type RequestQuery = {
      offerType?: OfferTypes;
      /** @example "and" */
      searchParam: string;
      sortType?: SortTypes;
      /** @example "2" */
      storeId?: string;
      /** @example "2,4,6,7" */
      subCategories?: string;
      userType?: UserTypes;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = OfferControllerGetAllCategoriesData;
  }

  /**
   * No description
   * @tags Offers
   * @name OfferControllerGetOngoingOffers
   * @request GET:/offers/ongoing-offers
   * @secure
   * @response `200` `OfferControllerGetOngoingOffersData`
   * @response `401` `void` Unauthorized
   * @response `default` `OngoingOffersResponse`
   */
  export namespace OfferControllerGetOngoingOffers {
    export type RequestParams = {};
    export type RequestQuery = {
      offerType?: OfferTypes;
      /** @example "2,4,6,7" */
      sales?: string;
      /** @example "and" */
      searchParam: string;
      sortType?: SortTypes;
      /** @example "2,4,6,7" */
      subCategories?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = OfferControllerGetOngoingOffersData;
  }

  /**
   * No description
   * @tags Offers
   * @name OfferControllerGetOfferById
   * @request GET:/offers/offer{uid}
   * @secure
   * @response `200` `OfferControllerGetOfferByIdData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetOfferByIdResponse`
   */
  export namespace OfferControllerGetOfferById {
    export type RequestParams = {
      uid: number;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = OfferControllerGetOfferByIdData;
  }

  /**
   * No description
   * @tags Offers
   * @name OfferControllerGetOfferByTitle
   * @request GET:/offers/offer/title/{title}
   * @secure
   * @response `200` `OfferControllerGetOfferByTitleData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetSimilarOffers`
   */
  export namespace OfferControllerGetOfferByTitle {
    export type RequestParams = {
      title: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = OfferControllerGetOfferByTitleData;
  }

  /**
   * No description
   * @tags Offers
   * @name OfferControllerSaveItem
   * @request POST:/offers/save
   * @secure
   * @response `201` `OfferControllerSaveItemData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  export namespace OfferControllerSaveItem {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = SaveOfferDto;
    export type RequestHeaders = {};
    export type ResponseBody = OfferControllerSaveItemData;
  }

  /**
   * No description
   * @tags Offers
   * @name OfferControllerRemoveSavedItem
   * @request POST:/offers/remove
   * @secure
   * @response `201` `OfferControllerRemoveSavedItemData`
   * @response `401` `void` Unauthorized
   * @response `default` `SaveItemResponse`
   */
  export namespace OfferControllerRemoveSavedItem {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = RemoveOfferDto;
    export type RequestHeaders = {};
    export type ResponseBody = OfferControllerRemoveSavedItemData;
  }
}

export namespace Click {
  /**
   * No description
   * @tags Click
   * @name ClickControllerCreate
   * @request POST:/click
   * @secure
   * @response `201` `ClickControllerCreateData`
   * @response `401` `void` Unauthorized
   * @response `default` `ClickCreateResponse`
   */
  export namespace ClickControllerCreate {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = CreateClickDto;
    export type RequestHeaders = {};
    export type ResponseBody = ClickControllerCreateData;
  }

  /**
   * No description
   * @tags Click
   * @name ClickControllerGetClicks
   * @request GET:/click
   * @secure
   * @response `200` `ClickControllerGetClicksData`
   * @response `401` `void` Unauthorized
   * @response `default` `ClicksResponse`
   */
  export namespace ClickControllerGetClicks {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @example "2021-10-10" */
      endDate?: string;
      /** @example "and" */
      searchParam?: string;
      sortType?: SortTypes;
      /** @example "2021-10-10" */
      startDate?: string;
      /** @example "Pending,Tracked,Confirmed,Cancelled,Report Missing CB" */
      status?: string;
      /** @example "1,2,3" */
      stores?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = ClickControllerGetClicksData;
  }

  /**
   * No description
   * @tags Click
   * @name ClickControllerCreateNoAuth
   * @request POST:/click/no-auth
   * @response `201` `ClickControllerCreateNoAuthData`
   * @response `default` `ClickCreateResponse`
   */
  export namespace ClickControllerCreateNoAuth {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = CreateClickDto;
    export type RequestHeaders = {};
    export type ResponseBody = ClickControllerCreateNoAuthData;
  }

  /**
   * No description
   * @tags Click
   * @name ClickControllerGetClicksByStores
   * @request GET:/click/stores
   * @secure
   * @response `200` `ClickControllerGetClicksByStoresData`
   * @response `401` `void` Unauthorized
   * @response `default` `ClicksByStoreResponse`
   */
  export namespace ClickControllerGetClicksByStores {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @example "2021-10-10" */
      date?: string;
      /** @example "and" */
      searchParam?: string;
      sortType?: SortTypes;
      /** @example "Pending,Tracked,Confirmed,Cancelled,Report Missing CB" */
      status?: string;
      /** @example "1,2,3" */
      stores?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = ClickControllerGetClicksByStoresData;
  }

  /**
   * No description
   * @tags Click
   * @name ClickControllerGetClickedStores
   * @request GET:/click/clicked_stores
   * @secure
   * @response `200` `ClickControllerGetClickedStoresData`
   * @response `401` `void` Unauthorized
   */
  export namespace ClickControllerGetClickedStores {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = ClickControllerGetClickedStoresData;
  }

  /**
   * No description
   * @tags Click
   * @name MissingCashbackControllerReportMissingCashback
   * @request POST:/click/missing-cashback/report
   * @secure
   * @response `201` `MissingCashbackControllerReportMissingCashbackData`
   * @response `401` `void` Unauthorized
   * @response `default` `string`
   */
  export namespace MissingCashbackControllerReportMissingCashback {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = ReportMissingCashbackDto;
    export type RequestHeaders = {};
    export type ResponseBody =
      MissingCashbackControllerReportMissingCashbackData;
  }

  /**
   * No description
   * @tags Click
   * @name MissingCashbackControllerListMissingCashback
   * @request GET:/click/missing-cashback/list
   * @secure
   * @response `200` `MissingCashbackControllerListMissingCashbackData`
   * @response `401` `void` Unauthorized
   * @response `default` `MissingCashbackResponse`
   */
  export namespace MissingCashbackControllerListMissingCashback {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @example "2021-10-10" */
      endDate?: string;
      /** @example "and" */
      searchParam?: string;
      sortType?: SortTypes;
      /** @example "2021-10-10" */
      startDate?: string;
      /** @example "not-solved,solved,rejected,forwarded" */
      status?: string;
      /** @example "1,2,3" */
      stores?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = MissingCashbackControllerListMissingCashbackData;
  }
}

export namespace Payment {
  /**
   * No description
   * @tags PaymentRequest
   * @name PaymentControllerRequestPayments
   * @request POST:/payment/withdraw
   * @secure
   * @response `201` `PaymentControllerRequestPaymentsData`
   * @response `401` `void` Unauthorized
   * @response `default` `boolean`
   */
  export namespace PaymentControllerRequestPayments {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = PaymentRequestDto;
    export type RequestHeaders = {};
    export type ResponseBody = PaymentControllerRequestPaymentsData;
  }

  /**
   * No description
   * @tags PaymentRequest
   * @name PaymentControllerGetAllPaymentRequestedUser
   * @request GET:/payment
   * @secure
   * @response `200` `PaymentControllerGetAllPaymentRequestedUserData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetPaymentListResponse`
   */
  export namespace PaymentControllerGetAllPaymentRequestedUser {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @default "2024-03-27T07:00:31.418Z" */
      endDate?: string;
      /** The type of payment */
      paymentType: PaymentTypes;
      /** @example "a" */
      searchParam?: string;
      sortType?: PaymentSortTypes;
      /** @default "2024-03-27T07:00:30.418Z" */
      startDate?: string;
      /** @example "Requested,Paid,Cancelled" */
      status?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = PaymentControllerGetAllPaymentRequestedUserData;
  }
}

export namespace SavedItems {
  /**
   * No description
   * @tags SavedItems
   * @name SavedItemControllerGetAllSavedDeals
   * @request GET:/saved-items/deals
   * @secure
   * @response `200` `SavedItemControllerGetAllSavedDealsData`
   * @response `401` `void` Unauthorized
   * @response `default` `SavedDealsResponse`
   */
  export namespace SavedItemControllerGetAllSavedDeals {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @example "and" */
      searchParam: string;
      sortType?: SortTypes;
      /** @example "2,4,6,7" */
      subCategories?: string;
      userType?: UserTypes;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = SavedItemControllerGetAllSavedDealsData;
  }

  /**
   * No description
   * @tags SavedItems
   * @name SavedItemControllerGetAllSavedOfferUids
   * @request GET:/saved-items/saved-offer-ids
   * @secure
   * @response `200` `SavedItemControllerGetAllSavedOfferUidsData`
   * @response `401` `void` Unauthorized
   * @response `default` `(number)[]`
   */
  export namespace SavedItemControllerGetAllSavedOfferUids {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = SavedItemControllerGetAllSavedOfferUidsData;
  }

  /**
   * No description
   * @tags SavedItems
   * @name SavedItemControllerGetAllSavedCoupons
   * @request GET:/saved-items/coupons
   * @secure
   * @response `200` `SavedItemControllerGetAllSavedCouponsData`
   * @response `401` `void` Unauthorized
   * @response `default` `SavedCouponsResponse`
   */
  export namespace SavedItemControllerGetAllSavedCoupons {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @example "and" */
      searchParam: string;
      sortType?: SortTypes;
      /** @example "2,4,6,7" */
      subCategories?: string;
      userType?: UserTypes;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = SavedItemControllerGetAllSavedCouponsData;
  }

  /**
   * No description
   * @tags SavedItems
   * @name SavedItemControllerGetAllSavedStores
   * @request GET:/saved-items/stores
   * @secure
   * @response `200` `SavedItemControllerGetAllSavedStoresData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetAllStoresResponse`
   */
  export namespace SavedItemControllerGetAllSavedStores {
    export type RequestParams = {};
    export type RequestQuery = {
      /**
       * Filter stores by cashback percentage tier
       * @example 50
       */
      cbPercent?: SavedItemControllerGetAllSavedStoresParams1CbPercentEnum;
      /** @example "60" */
      maxPercent: number;
      /** @example "10" */
      minPercent: number;
      saved: any;
      /** @example "and" */
      searchParam: string;
      sortType: SortTypes;
      /** @example "2,4,6,7" */
      subCategories?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = SavedItemControllerGetAllSavedStoresData;
  }

  /**
   * No description
   * @tags SavedItems
   * @name SavedItemControllerGetAllSavedGiftCards
   * @request GET:/saved-items/gift-cards
   * @secure
   * @response `200` `SavedItemControllerGetAllSavedGiftCardsData`
   * @response `401` `void` Unauthorized
   * @response `default` `GetGiftCardListResponse`
   */
  export namespace SavedItemControllerGetAllSavedGiftCards {
    export type RequestParams = {};
    export type RequestQuery = {
      /** @example "and" */
      searchParam: string;
      sortType?: SortTypes;
      /** @example "2,4,6,7" */
      subCategories?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = SavedItemControllerGetAllSavedGiftCardsData;
  }
}

export namespace Links {
  /**
   * No description
   * @tags Links
   * @name LinkControllerGenerateLink
   * @request POST:/links/generate
   * @secure
   * @response `201` `LinkControllerGenerateLinkData`
   * @response `401` `void` Unauthorized
   * @response `default` `LinkResponseDto`
   */
  export namespace LinkControllerGenerateLink {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = GenerateLinkDto;
    export type RequestHeaders = {};
    export type ResponseBody = LinkControllerGenerateLinkData;
  }

  /**
   * No description
   * @tags Links
   * @name LinkControllerProcessMultipleLinks
   * @request POST:/links/generate-multi
   * @secure
   * @response `201` `LinkControllerProcessMultipleLinksData`
   * @response `401` `void` Unauthorized
   * @response `default` `MultiLinkResponseDto`
   */
  export namespace LinkControllerProcessMultipleLinks {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = GenerateMultiLinkDto;
    export type RequestHeaders = {};
    export type ResponseBody = LinkControllerProcessMultipleLinksData;
  }

  /**
   * No description
   * @tags Links
   * @name LinkControllerGetUserLinks
   * @request GET:/links
   * @secure
   * @response `200` `LinkControllerGetUserLinksData`
   * @response `401` `void` Unauthorized
   * @response `default` `PaginationResponseDto`
   */
  export namespace LinkControllerGetUserLinks {
    export type RequestParams = {};
    export type RequestQuery = {
      /**
       * Filter by end date
       * @format date-time
       */
      endDate?: string;
      /**
       * Items per page
       * @default 10
       */
      limit?: number;
      /**
       * Page number
       * @default 1
       */
      page?: number;
      /**
       * Filter by start date
       * @format date-time
       */
      startDate?: string;
      /** Filter by store ID */
      storeId?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = LinkControllerGetUserLinksData;
  }

  /**
   * No description
   * @tags Links
   * @name LinkControllerGetUserAnalytics
   * @request GET:/links/analytics
   * @secure
   * @response `200` `LinkControllerGetUserAnalyticsData`
   * @response `401` `void` Unauthorized
   * @response `default` `UserAnalyticsResponseDto`
   */
  export namespace LinkControllerGetUserAnalytics {
    export type RequestParams = {};
    export type RequestQuery = {
      /**
       * End date for analysis period
       * @format date-time
       * @example "2023-12-31"
       */
      endDate?: string;
      /**
       * Period type for comparison (day, week, month, year)
       * @default "month"
       * @example "month"
       */
      periodType?: LinkControllerGetUserAnalyticsParams1PeriodTypeEnum;
      /**
       * Start date for analysis period
       * @format date-time
       * @example "2023-01-01"
       */
      startDate?: string;
      /**
       * Filter by store ID
       * @example "60a12d7c9f47e32b54e3c59a"
       */
      storeId?: string;
    };
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = LinkControllerGetUserAnalyticsData;
  }

  /**
   * No description
   * @tags Links
   * @name LinkControllerGetLinkDetails
   * @request GET:/links/{linkId}
   * @response `200` `LinkControllerGetLinkDetailsData`
   * @response `default` `void` Redirect to the original URL
   */
  export namespace LinkControllerGetLinkDetails {
    export type RequestParams = {
      linkId: string;
    };
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = LinkControllerGetLinkDetailsData;
  }

  /**
   * No description
   * @tags Links
   * @name LinkControllerGetShareAndEarnOffers
   * @request GET:/links/share-and-earn/offers
   * @secure
   * @response `200` `LinkControllerGetShareAndEarnOffersData`
   * @response `401` `void` Unauthorized
   * @response `default` `(ShareAndEarnOfferDto)[]` Get share and earn offers with short URLs
   */
  export namespace LinkControllerGetShareAndEarnOffers {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = LinkControllerGetShareAndEarnOffersData;
  }
}

export namespace Sitemap {
  /**
   * No description
   * @tags Sitemap
   * @name SitemapControllerGenerateSitemap
   * @request GET:/sitemap
   * @response `200` `SitemapControllerGenerateSitemapData`
   */
  export namespace SitemapControllerGenerateSitemap {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = SitemapControllerGenerateSitemapData;
  }
}

export namespace Bot {
  /**
   * No description
   * @tags Bot
   * @name BotControllerSendMessage
   * @request POST:/bot/send-message
   * @response `200` `BotControllerSendMessageData` Message sent successfully
   * @response `201` `SendMessageResponseDto`
   * @response `400` `void` Invalid request parameters
   */
  export namespace BotControllerSendMessage {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = SendMessageDto;
    export type RequestHeaders = {};
    export type ResponseBody = BotControllerSendMessageData;
  }
}

export namespace Chat {
  /**
   * No description
   * @tags Chat
   * @name ChatControllerChat
   * @summary Send a chat message and receive complete response
   * @request POST:/chat
   * @response `200` `ChatControllerChatData` Chat response
   * @response `201` `ChatResponseDto`
   * @response `400` `void` Invalid request
   * @response `500` `void` Internal server error
   */
  export namespace ChatControllerChat {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = ChatRequestDto;
    export type RequestHeaders = {};
    export type ResponseBody = ChatControllerChatData;
  }

  /**
   * No description
   * @tags Chat
   * @name ChatControllerEmbedFile
   * @summary Upload and embed a TXT file for context
   * @request POST:/chat/embed
   * @response `200` `ChatControllerEmbedFileData`
   * @response `201` `EmbedResponseDto`
   * @response `400` `void` Invalid file or processing error
   */
  export namespace ChatControllerEmbedFile {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = never;
    export type RequestHeaders = {};
    export type ResponseBody = ChatControllerEmbedFileData;
  }
}

export namespace Card {
  /**
   * No description
   * @tags WishList
   * @name WishListControllerJoinWishlist
   * @request POST:/card/wishlist/join
   * @response `201` `WishListControllerJoinWishlistData`
   * @response `default` `WishlistResponseDto`
   */
  export namespace WishListControllerJoinWishlist {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = WishlistEmailDto;
    export type RequestHeaders = {};
    export type ResponseBody = WishListControllerJoinWishlistData;
  }

  /**
   * No description
   * @tags Card Auth
   * @name CardAuthControllerCardLogin
   * @request POST:/card/auth/login
   * @response `201` `CardAuthControllerCardLoginData`
   * @response `default` `CardLoginResponseDto`
   */
  export namespace CardAuthControllerCardLogin {
    export type RequestParams = {};
    export type RequestQuery = {};
    export type RequestBody = CardLoginDto;
    export type RequestHeaders = {};
    export type ResponseBody = CardAuthControllerCardLoginData;
  }
}

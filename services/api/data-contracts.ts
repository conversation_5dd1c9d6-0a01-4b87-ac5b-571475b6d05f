/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export interface AllCategoriesResponse {
  name: string;
  subCategories: AllSubCategory[];
  uid: number;
}

export interface AllSubCategory {
  name: string;
  uid: number;
}

export type AppControllerGetHelloData = string;

export interface AppliedFiltersType {
  /** Subcategory IDs detected from search keywords */
  keywordBasedSubcategoryIds: number[];
  /** All subcategory IDs applied (explicit + keyword-based) */
  subCategoryIds: number[];
}

export type AuthControllerCheckData = any;

export type AuthControllerCreateData = LoginResponse;

export type AuthControllerGetCsrfTokenData = object;

export type AuthControllerGoogleAuthCallbackData = any;

export interface AuthControllerGoogleAuthCallbackParams {
  redirect: string;
  state: string;
}

export type AuthControllerGoogleAuthData = any;

export interface AuthControllerGoogleAuthParams {
  referralCode: string;
}

export type AuthControllerLoginData = LoginResponse;

export type AuthControllerLogoutData = any;

export type AuthControllerResendOtpData = LoginResponse;

export type AuthControllerVerifyOtpData = any;

export type AuthControllerVerifyTokenData = any;

export interface AuthControllerVerifyTokenParams {
  token: string;
}

export type BannerControllerGetBannersData = BannerResponse;

export interface BannerResponse {
  desktopBanners: string[];
  mobileBanners: string[];
}

export type BotControllerSendMessageData = SendMessageResponseDto;

export interface Card {
  /** @example 1000 */
  amount: number;
  /** @example 1 */
  quantity: number;
}

export type CardAuthControllerCardLoginData = any;

export interface CardLoginDto {
  /**
   * User number for login
   * @example "**********"
   */
  mobile?: string;
}

export interface CardLoginResponseDto {
  /**
   * Response message
   * @example "Logged in successfully"
   */
  message: string;
}

export interface CashbackRateType {
  description: string;
  name: string;
  newUserRate: number;
  oldUserRate: number;
  type: CashbackRateTypeTypeEnum;
  uid: number;
}

export enum CashbackRateTypeTypeEnum {
  Percent = "percent",
  Amount = "amount",
}

export enum CashbackSortTypes {
  Newest = "newest",
  Oldest = "oldest",
  CashbackAmount = "cashbackAmount",
}

export interface CategorizedOffers {
  expiredOffers: ContextMissedOfferType[];
  ongoingOffers: ContextOngoingOfferType[];
  trendingOffers: TrendingOfferResponseType;
}

export type CategoryControllerGetAllCategoriesData = any;

export type CategoryControllerGetAllCategoriesDetailsData =
  AllCategoriesResponse[];

export interface CategoryControllerGetAllCategoriesParams {
  trending: boolean;
}

export type CategoryControllerGetSubCategoryByCategoryIdData =
  SubCategoriesByCategoryResponse;

export type CategoryControllerGetSubCategorySearchData = any;

export interface CategoryResponse {
  iconUrl: string;
  id: string;
  name: string;
  trendingPriority?: number;
  uid: number;
}

export interface CategoryStoresResponse {
  categoryId: string;
  categoryName: string;
  stores: ContextStore[];
}

export interface CbItem {
  approxConfirmDate: string;
  cashbackAmount: number;
  earningsType: CbItemEarningsTypeEnum;
  isShareAndEarn: boolean;
  orderAmount: number;
  orderDate: string;
  referenceId: string;
  remarks: string;
  status: CbItemStatusEnum;
  storeBgColor?: string;
  storeLogo: string;
}

export enum CbItemEarningsTypeEnum {
  Click = "click",
  Missing = "missing",
  Referral = "referral",
}

export enum CbItemStatusEnum {
  Confirmed = "confirmed",
  Pending = "pending",
  Cancelled = "cancelled",
}

export type ChatControllerChatData = ChatResponseDto;

export type ChatControllerEmbedFileData = EmbedResponseDto;

export interface ChatRequestDto {
  message: string;
  sessionId?: string;
}

export interface ChatResponseDto {
  message: string;
  sessionId: string;
}

export interface ClickByStoreType {
  canReport: boolean;
  date: string;
  fromPreviousMonth: boolean;
  id: string;
  name: string;
  referenceId: string;
  status: ClickByStoreTypeStatusEnum;
  time: string;
  type: ClickByStoreTypeTypeEnum;
  uid: number;
}

export enum ClickByStoreTypeStatusEnum {
  Pending = "Pending",
  Confirmed = "Confirmed",
  Cancelled = "Cancelled",
  Tracked = "Tracked",
  ReportMissingCB = "Report Missing CB",
}

export enum ClickByStoreTypeTypeEnum {
  Offer = "offer",
  Express = "express",
  Rates = "rates",
}

export type ClickControllerCreateData = any;

export type ClickControllerCreateNoAuthData = any;

export type ClickControllerGetClickedStoresData = object[];

export type ClickControllerGetClicksByStoresData = any;

export interface ClickControllerGetClicksByStoresParams {
  /** @example "2021-10-10" */
  date?: string;
  /** @example "and" */
  searchParam?: string;
  sortType?: SortTypes;
  /** @example "Pending,Tracked,Confirmed,Cancelled,Report Missing CB" */
  status?: string;
  /** @example "1,2,3" */
  stores?: string;
}

export type ClickControllerGetClicksData = any;

export interface ClickControllerGetClicksParams {
  /** @example "2021-10-10" */
  endDate?: string;
  /** @example "and" */
  searchParam?: string;
  sortType?: SortTypes;
  /** @example "2021-10-10" */
  startDate?: string;
  /** @example "Pending,Tracked,Confirmed,Cancelled,Report Missing CB" */
  status?: string;
  /** @example "1,2,3" */
  stores?: string;
}

export interface ClickCreateResponse {
  isActive?: string;
  logo: string;
  offer?: string;
  referenceId: string;
  url: string;
}

export interface ClickType {
  canReport: boolean;
  createdAt: string;
  date: string;
  fromPreviousMonth: boolean;
  id: string;
  logo: string;
  name: string;
  referenceId: string;
  status: ClickTypeStatusEnum;
  time: string;
  type: ClickTypeTypeEnum;
  uid: number;
}

export enum ClickTypeStatusEnum {
  Pending = "Pending",
  Confirmed = "Confirmed",
  Cancelled = "Cancelled",
  Tracked = "Tracked",
  ReportMissingCB = "Report Missing CB",
}

export enum ClickTypeTypeEnum {
  Offer = "offer",
  Express = "express",
  Rates = "rates",
}

export interface ClicksByStoreResponse {
  pagination: PaginationResponseType;
  stores: StoreType[];
}

export interface ClicksResponse {
  clicks: ClickType[];
  pagination: PaginationResponseType;
}

export interface ContextMissedOfferType {
  currentAmount: number;
  offerTitle: string;
  productImage: string;
  storeBgColor: string;
  storeLogoUrl: string;
  storeName: string;
  uid: number;
}

export interface ContextOfferCouponsType {
  couponCode: string;
  endDate: string;
  hideCbTag?: boolean;
  isAutoGenerated?: boolean;
  offerCaption: string;
  offerTitle: string;
  offerUrl: string;
  productImage: string;
  salePrice: number;
  saved: boolean;
  storeBgColor: string;
  storeLogoUrl: string;
  storeName: string;
  uid: number;
}

export interface ContextOfferDealsType {
  endDate: string;
  hideCbTag?: boolean;
  isAutoGenerated?: boolean;
  offerCaption: string;
  offerTitle: string;
  offerUrl: string;
  productImage: string;
  salePrice: number;
  saved: boolean;
  storeBgColor: string;
  storeLogoUrl: string;
  storeName: string;
  uid: number;
}

export interface ContextOngoingOfferType {
  endDate: string;
  hideCbTag?: boolean;
  isAutoGenerated?: boolean;
  offerCaption: string;
  offerTitle: string;
  offerUrl: string;
  productImage: string;
  saleCaption: string;
  saleLogoUrl: string;
  salePrice: number;
  saved: boolean;
  storeBgColor: string;
  storeLogoUrl: string;
  storeName: string;
  uid: number;
}

export interface ContextStore {
  bgColor: string;
  caption: string;
  imageUrl: string;
  offerCount: number;
  saved: boolean;
  storeName: string;
  uid: number;
}

export interface CreateClickDto {
  type: CreateClickDtoTypeEnum;
  uid: number;
}

export enum CreateClickDtoTypeEnum {
  Offer = "offer",
  Express = "express",
  Rates = "rates",
}

export interface CreateReviewDto {
  /** @example 3 */
  rating: CreateReviewDtoRatingEnum;
  /** @example "This is a review" */
  review: string;
  /** @example 32 */
  storeUid: number;
}

/** @example 3 */
export enum CreateReviewDtoRatingEnum {
  Value1 = 1,
  Value2 = 2,
  Value3 = 3,
  Value4 = 4,
  Value5 = 5,
}

export interface CreateReviewResponse {
  message: string;
}

export interface CreateUserDto {
  /**
   * User Email
   * @example "<EMAIL>"
   */
  email: string;
  /**
   * Mobile Number
   * @example **********
   */
  mobile?: number;
  /**
   * User Name
   * @example "John Doe"
   */
  name: string;
  /**
   * Referral Code
   * @example "Some referral code"
   */
  referralCode?: string;
}

export interface DealAndCouponsResponse {
  appliedFilters?: AppliedFiltersType;
  offers: OfferCouponsType[];
  pagination: PaginationResponseType;
}

export interface EmbedResponseDto {
  chunksProcessed: number;
  documentsStored: number;
  message: string;
  metadata: {
    chunkOverlap: number;
    chunkSize: number;
    originalLength: number;
    /** @format date-time */
    processedAt: string;
  };
  processingTime: number;
  source: string;
}

export interface GenerateLinkDto {
  /**
   * The original URL to generate a link for
   * @example "https://www.flipkart.com/product/123456"
   */
  url: string;
}

export interface GetAllOnGoingOffersResponse {
  name: string;
  uid: number;
}

export interface GetAllReviewsResponse {
  pagination: PaginationResponseType;
  reviews: GetAllReviewsType[];
}

export interface GetAllReviewsType {
  avatar: string;
  createdDate: string;
  name: string;
  rating: number;
  review: string;
  uid: number;
}

export interface GetAllStoresResponse {
  pagination: PaginationResponseType;
  stores: StoreCard[];
}

export interface GetBankAccountDataResponse {
  accountNumber: string;
  address: string;
  bankName: string;
  branchName: string;
  holderName: string;
  ifsc: string;
  postcode: string;
  upi: string;
}

export interface GetCashbackRatesByStoreResponse {
  cashbackRates: CashbackRateType[];
}

export interface GetCbHistoryResponse {
  cbItems: CbItem[];
  pagination: PaginationResponseType;
}

export interface GetGiftCardListResponse {
  giftCards: GetGiftCardType[];
  pagination: PaginationResponseType;
}

export interface GetGiftCardResponse {
  giftCard: GiftCardDetails;
  similarGiftCards: GetGiftCardType[];
}

export interface GetGiftCardType {
  caption: string;
  id: string;
  imageUrl: string;
  name: string;
  saved: boolean;
  uid: number;
}

export interface GetOfferByIdResponse {
  offer: GetOfferType;
  similarOffers: OfferCouponsType[];
}

export interface GetOfferType {
  cashbackType: GetOfferTypeCashbackTypeEnum;
  confirmationTime: string;
  couponCode?: string;
  endDate: string;
  hideCbTag?: boolean;
  importantUpdate?: string;
  isAutoGenerated?: boolean;
  isExpired?: boolean;
  itemPrice?: number;
  keySpecs?: string;
  minimumAmount: number;
  missingAccepted?: boolean;
  newUserRate?: number;
  offerAmount: number;
  offerCaption?: string;
  offerDescription: string;
  offerPercent?: number;
  offerTitle: string;
  offerType: GetOfferTypeOfferTypeEnum;
  offerUrl?: string;
  offerWarning?: string;
  oldUserRate?: number;
  productImage: string;
  repeatBy?: string;
  saleCaption?: string;
  saleLogoUrl?: string;
  salePrice?: number | null;
  saved?: boolean;
  storeBgColor: string;
  storeId: ObjectId;
  storeLogoUrl: string;
  storeName: string;
  termsAndConditions: string;
  trackingTime: string;
  uid: number;
}

export enum GetOfferTypeCashbackTypeEnum {
  Cashback = "cashback",
  Reward = "reward",
}

export enum GetOfferTypeOfferTypeEnum {
  Upto = "upto",
  Flat = "flat",
}

export interface GetPaymentListResponse {
  pagination: PaginationResponseType;
  payments: PaymentType[];
}

export interface GetRedeemGiftCardListResponse {
  pagination: PaginationResponseType;
  redeemGiftCards: RedeemGiftCard[];
}

export interface GetReferralLeaderboardResponse {
  name: string;
  referralCount: number;
  totalReferralCommission: number;
}

export interface GetSimilarOffers {
  similarOffers: OfferCouponsType[];
}

export interface GetStoreDetails {
  active: boolean;
  cashbackAmount: number;
  cashbackPercent: number;
  cashbackType: GetStoreDetailsCashbackTypeEnum;
  confirmationTime: string;
  description: string;
  giftCard: StoreGiftCard;
  id: string;
  importantPoints: string;
  isAppSaleTrackable: boolean;
  logo: string;
  minimumAmount: number;
  missingAccepted: boolean;
  name: string;
  offerType: GetStoreDetailsOfferTypeEnum;
  offerWarning: string;
  ratingAverage: number;
  ratingsCount: number;
  storePopUpWarning: string;
  storeWarning: string;
  trackingTime: string;
  uid: number;
}

export enum GetStoreDetailsCashbackTypeEnum {
  Cashback = "cashback",
  Reward = "reward",
}

export enum GetStoreDetailsOfferTypeEnum {
  Flat = "flat",
  Upto = "upto",
}

export interface GetStoreDetailsResponse {
  similarStores: ContextStore[];
  store: GetStoreDetails;
}

export interface GetUserProfileResponseItem {
  avatar?: string;
  balance: number;
  cancelledCount: number;
  confirmedCount: number;
  email: string;
  mobile?: number;
  name: string;
  pendingCount: number;
  personalInterest: PersonalInterestTypes[];
  referralCode: string;
  sendNotification: boolean;
}

export interface GetUsersByReferralCodeResponse {
  pagination: PaginationResponseType;
  users: UserData[];
}

export interface GiftCardBannerType {
  imageUrl: string;
  redirectUrl: string;
  termsContent: string;
  termsTitle: string;
}

export interface GiftCardBannersResponse {
  desktopBanners: GiftCardBannerType[];
  mobileBanners: GiftCardBannerType[];
}

export type GiftCardControllerGetAllGiftCardBannersData =
  GiftCardBannersResponse;

export type GiftCardControllerGetAllGiftCardsData = GetGiftCardListResponse;

export interface GiftCardControllerGetAllGiftCardsParams {
  /** @example "a" */
  searchParam: string;
  sortType?: SortTypes;
  /** @example "2,4,6,7" */
  subCategories?: string;
}

export type GiftCardControllerGetGiftCardDetailsData = GetGiftCardResponse;

export type GiftCardControllerGetIcbCardData = any;

export type GiftCardControllerOrderGiftCardData = OrderResponse;

export type GiftCardControllerRedeemGiftCardHistoryData = any;

export interface GiftCardControllerRedeemGiftCardHistoryParams {
  /** @default "2024-02-25T18:19:26.808Z" */
  date: string;
  /** @example "ICB123456" */
  searchParam: string;
  sortType?: RedeemHistoryTypes;
}

export type GiftCardControllerRedeemIcbGiftCardData = boolean;

export type GiftCardControllerRemoveSavedItemData = any;

export type GiftCardControllerSaveItemData = any;

export type GiftCardControllerVerifyPaymentData = boolean;

export interface GiftCardDetails {
  cashbackGiving: number;
  denominations: number[];
  description: string;
  discountGetting: number;
  howToUse: string[];
  id: string;
  imageUrl: string;
  name: string;
  showCustomAmount: boolean;
  storeBgColor: string;
  storeLogo: string;
  storeName: string;
  storeUid: number;
  terms: string;
  uid: number;
}

export interface HeroResponseItem {
  iconUrl: string;
  redirectUrl: string;
  title: string;
}

export interface IcbCardTypeResponse {
  name: string;
  uid: string;
}

export type LinkControllerGenerateLinkData = LinkResponseDto;

export type LinkControllerGetLinkDetailsData = any;

export type LinkControllerGetUserAnalyticsData = UserAnalyticsResponseDto;

export interface LinkControllerGetUserAnalyticsParams {
  /**
   * End date for analysis period
   * @format date-time
   * @example "2023-12-31"
   */
  endDate?: string;
  /**
   * Period type for comparison (day, week, month, year)
   * @default "month"
   * @example "month"
   */
  periodType?: PeriodTypeEnum;
  /**
   * Start date for analysis period
   * @format date-time
   * @example "2023-01-01"
   */
  startDate?: string;
  /**
   * Filter by store ID
   * @example "60a12d7c9f47e32b54e3c59a"
   */
  storeId?: string;
}

/**
 * Period type for comparison (day, week, month, year)
 * @default "month"
 * @example "month"
 */
export enum LinkControllerGetUserAnalyticsParams1PeriodTypeEnum {
  Day = "day",
  Week = "week",
  Month = "month",
  Year = "year",
}

export type LinkControllerGetUserLinksData = any;

export interface LinkControllerGetUserLinksParams {
  /**
   * Filter by end date
   * @format date-time
   */
  endDate?: string;
  /**
   * Items per page
   * @default 10
   */
  limit?: number;
  /**
   * Page number
   * @default 1
   */
  page?: number;
  /**
   * Filter by start date
   * @format date-time
   */
  startDate?: string;
  /** Filter by store ID */
  storeId?: string;
}

export interface LinkResponseDto {
  /**
   * The creation date
   * @example "2023-07-01T12:00:00.000Z"
   */
  createdAt: string;
  /**
   * The generated affiliate URL
   * @example "https://dl.flipkart.com/dl/product/123456?affid=connectin"
   */
  generatedUrl: string;
  /**
   * The unique ID of the generated link
   * @example "ICBLABCD1234"
   */
  linkId: string;
  /**
   * The original URL
   * @example "https://www.flipkart.com/product/123456"
   */
  originalUrl: string;
  /**
   * The short URL
   * @example "https://icashbk.in/ICBLABCD1234"
   */
  shortUrl: string;
  /**
   * The store name
   * @example "Flipkart"
   */
  storeName: string;
}

export interface LoginDto {
  /** @example "<EMAIL>" */
  email?: string;
  /** @example "123456789" */
  mobile?: string;
}

export interface LoginResponse {
  email?: string;
  message: string;
  mobile?: string;
}

export interface MetricWithChangeDto {
  /** Percentage change from previous period */
  percentageChange: number;
  /** Current value */
  value: number;
}

export type MissingCashbackControllerListMissingCashbackData = any;

export interface MissingCashbackControllerListMissingCashbackParams {
  /** @example "2021-10-10" */
  endDate?: string;
  /** @example "and" */
  searchParam?: string;
  sortType?: SortTypes;
  /** @example "2021-10-10" */
  startDate?: string;
  /** @example "not-solved,solved,rejected,forwarded" */
  status?: string;
  /** @example "1,2,3" */
  stores?: string;
}

export type MissingCashbackControllerReportMissingCashbackData = string;

export interface MissingCashbackResponse {
  missingCashbacks: MissingCashbackType[];
  pagination: PaginationResponseType;
}

export interface MissingCashbackType {
  amount: number;
  /** @format date-time */
  clickedTime: string;
  /** @format date-time */
  createdAt: string;
  referenceId: string;
  status: string;
  storeBgColor: string;
  storeLogo: string;
  title: string;
  uid: number;
}

export interface MobileStoryResponse {
  buttonText: string;
  description: string;
  duration: number;
  imageUrl: string;
  redirectUrl: string;
  timestamp: number;
  title: string;
  type: MobileStoryResponseTypeEnum;
}

export enum MobileStoryResponseTypeEnum {
  Image = "image",
  Video = "video",
}

export type ObjectId = object;

export type OfferControllerGetAllCategoriesData = DealAndCouponsResponse;

export interface OfferControllerGetAllCategoriesParams {
  offerType?: OfferTypes;
  /** @example "and" */
  searchParam: string;
  sortType?: SortTypes;
  /** @example "2" */
  storeId?: string;
  /** @example "2,4,6,7" */
  subCategories?: string;
  userType?: UserTypes;
}

export type OfferControllerGetOfferByIdData = any;

export type OfferControllerGetOfferByTitleData = object;

export type OfferControllerGetOngoingOffersData = OngoingOffersResponse;

export interface OfferControllerGetOngoingOffersParams {
  offerType?: OfferTypes;
  /** @example "2,4,6,7" */
  sales?: string;
  /** @example "and" */
  searchParam: string;
  sortType?: SortTypes;
  /** @example "2,4,6,7" */
  subCategories?: string;
}

export type OfferControllerRemoveSavedItemData = any;

export type OfferControllerSaveItemData = any;

export interface OfferCouponsType {
  cashbackType?: string;
  couponCode: string;
  endDate: string;
  hideCbTag?: boolean;
  isAutoGenerated?: boolean;
  offerAmount?: number;
  offerCaption?: string;
  offerPercent?: number;
  offerTitle: string;
  offerType?: string;
  offerUrl?: string;
  productImage: string;
  repeatBy: string;
  salePrice: number;
  saved?: boolean;
  storeLogoUrl: string;
  storeName: string;
  uid: number;
}

export enum OfferTypes {
  Coupons = "coupons",
  Deals = "deals",
  Trending = "trending",
  Both = "both",
}

export type OffersControllerGetLandingOffersData = any;

export type OffersControllerGetOnGoingSaleOffersData =
  GetAllOnGoingOffersResponse[];

export interface OngoingOfferType {
  endDate: string;
  hideCbTag?: boolean;
  offerCaption?: string;
  offerTitle: string;
  offerUrl?: string;
  productImage: string;
  repeatBy?: string;
  saleCaption?: string;
  saleLogoUrl?: string;
  salePrice?: number | null;
  saved?: boolean;
  storeLogoUrl: string;
  storeName: string;
  uid: number;
}

export interface OngoingOffersResponse {
  offers: OngoingOfferType[];
  pagination: PaginationResponseType;
}

export interface OrderGiftCardDto {
  /** @example [{"amount":1000,"quantity":1},{"amount":100,"quantity":2},{"amount":4000,"quantity":3}] */
  cards: Card[];
  /** @example "<EMAIL>" */
  email: string;
  /** @example "60f5f7e8d3c9e8d5e4e3f3b1" */
  giftcardId: string;
  /** @example 9876543210 */
  mobile?: number;
  msg?: string;
  /** @example "John Doe" */
  name: string;
  /**
   * The types of payment
   * @example ["balance","razorpay"]
   */
  paymentMethods: OrderGiftCardDtoPaymentMethodsEnum[];
}

export enum OrderGiftCardDtoPaymentMethodsEnum {
  Balance = "balance",
  Razorpay = "razorpay",
}

export interface OrderResponse {
  amountToPay: number;
  currency: string;
  fullPaymentDone: boolean;
  orderId: string;
  paymentMethod: OrderResponsePaymentMethodEnum;
}

export enum OrderResponsePaymentMethodEnum {
  Balance = "balance",
  Razorpay = "razorpay",
}

export interface OtpDto {
  /**
   * user otp
   * @example 123413
   */
  otp: number;
}

export interface PaginationResponseDto {
  /** List of items */
  items: string[];
  /** Items per page */
  limit: number;
  /** Current page */
  page: number;
  /** Number of pages */
  pages: number;
  /** Total number of items */
  total: number;
}

export interface PaginationResponseType {
  /** @example 1 */
  page: number;
  pageSize: number;
  total: number;
}

export type PaymentControllerGetAllPaymentRequestedUserData =
  GetPaymentListResponse;

export interface PaymentControllerGetAllPaymentRequestedUserParams {
  /** @default "2024-03-27T07:00:31.418Z" */
  endDate?: string;
  /** The type of payment */
  paymentType: PaymentTypes;
  /** @example "a" */
  searchParam?: string;
  sortType?: PaymentSortTypes;
  /** @default "2024-03-27T07:00:30.418Z" */
  startDate?: string;
  /** @example "Requested,Paid,Cancelled" */
  status?: string;
}

export type PaymentControllerRequestPaymentsData = boolean;

export interface PaymentRequestDto {
  /**
   * The type of payment
   * @example "bank"
   */
  paymentType: PaymentTypes;
  /**
   * The amount to withdraw
   * @min 100
   */
  withdrawAmount: number;
}

export enum PaymentSortTypes {
  Newest = "newest",
  Oldest = "oldest",
  Amount = "amount",
}

export interface PaymentType {
  paymentDate: number;
  paymentType: PaymentTypePaymentTypeEnum;
  referenceId: string;
  status: PaymentTypeStatusEnum;
  withdrawAmount: number;
}

export enum PaymentTypePaymentTypeEnum {
  Bank = "Bank",
  UPI = "UPI",
  GiftVoucher = "Gift Voucher",
  Recharge = "Recharge",
  Wallet = "Wallet",
}

export enum PaymentTypeStatusEnum {
  Paid = "Paid",
  Requested = "Requested",
  Cancelled = "Cancelled",
}

/** The type of payment */
export enum PaymentTypes {
  Bank = "bank",
  Upi = "upi",
}

export interface PaymentVerifyDto {
  /** RazorPay order ID */
  orderId: string;
  /** RazorPay payment ID */
  paymentId: string;
  /** RazorPay payment signature */
  signature: string;
}

/**
 * Period type for comparison (day, week, month, year)
 * @default "month"
 * @example "month"
 */
export enum PeriodTypeEnum {
  Day = "day",
  Week = "week",
  Month = "month",
  Year = "year",
}

export interface PersonalInterestTypes {
  id: string;
  name: string;
}

export type QuickAccessControllerGetHeroQuickAccessesData = HeroResponseItem[];

export type QuickAccessControllerGetQuickAccessesData =
  QuickAccessResponseItem[];

export interface QuickAccessResponseItem {
  imageUrl: string;
  redirectUrl: string;
  title: string;
}

export interface RedeemGiftCard {
  amount: string;
  date: string;
  giftCardId: string;
  paid: boolean;
  time: string;
  uid: number;
}

export enum RedeemHistoryTypes {
  Date = "date",
  Amount = "amount",
}

export interface RedeemIcbGiftCardDto {
  giftCardNumber: number;
  giftCardPin: number;
}

export type ReferralCampaignControllerGetReferralEarningsLeaderboardData =
  GetReferralLeaderboardResponse[];

export interface ReferralCampaignControllerGetReferralEarningsLeaderboardParams {
  timeFrame: TimeFrameEnum;
}

export enum ReferralCampaignControllerGetReferralEarningsLeaderboardParams1TimeFrameEnum {
  Weekly = "weekly",
  Monthly = "monthly",
  All = "all",
}

export enum ReferralTypes {
  Newest = "newest",
  Oldest = "oldest",
  HighToLow = "highToLow",
  LowToHigh = "lowToHigh",
  NoOfOrders = "noOfOrders",
}

export interface RemoveOfferDto {
  /** @example 23 */
  itemUid: number;
}

export interface ReportMissingCashbackDto {
  /**
   * Invoice support only PNG, JPG, JPEG, WebP and PDF. File format with max size of 2MB
   * @format binary
   */
  invoice: File;
  reportData: ReportMissingCashbackType;
}

export interface ReportMissingCashbackType {
  /** @example "65eef72b4d2e4417e86ba45f" */
  click: ObjectId;
  coupon: boolean;
  message: string;
  orderId: string;
  paidAmount: number;
  platform: ReportMissingCashbackTypePlatformEnum;
  userType: ReportMissingCashbackTypeUserTypeEnum;
}

export enum ReportMissingCashbackTypePlatformEnum {
  Web = "web",
  Mobile = "mobile",
}

export enum ReportMissingCashbackTypeUserTypeEnum {
  New = "new",
  Old = "old",
}

export interface ResponseMobileStories {
  storeBgColor: string;
  storeLogo: string;
  storeName: string;
  stories: MobileStoryResponse[];
}

export type ReviewControllerCreateData = CreateReviewResponse;

export type ReviewControllerGetAllGiftCardsData = any;

export interface ReviewControllerGetAllGiftCardsParams {
  sortType: ReviewTypes;
  /** @example "65eef6a14d2e4417e86b9ce5" */
  storeId: string;
}

export enum ReviewTypes {
  Newest = "newest",
  Rating = "rating",
}

export interface SaveItemResponse {
  message: string;
}

export interface SaveOfferDto {
  /** @example 23 */
  itemUid: number;
}

export interface SavedCouponsResponse {
  coupons: ContextOfferCouponsType[];
  pagination: PaginationResponseType;
}

export interface SavedDealsResponse {
  deals: ContextOfferDealsType[];
  pagination: PaginationResponseType;
}

export type SavedItemControllerGetAllSavedCouponsData = SavedCouponsResponse;

export interface SavedItemControllerGetAllSavedCouponsParams {
  /** @example "and" */
  searchParam: string;
  sortType?: SortTypes;
  /** @example "2,4,6,7" */
  subCategories?: string;
  userType?: UserTypes;
}

export type SavedItemControllerGetAllSavedDealsData = SavedDealsResponse;

export interface SavedItemControllerGetAllSavedDealsParams {
  /** @example "and" */
  searchParam: string;
  sortType?: SortTypes;
  /** @example "2,4,6,7" */
  subCategories?: string;
  userType?: UserTypes;
}

export type SavedItemControllerGetAllSavedGiftCardsData =
  GetGiftCardListResponse;

export interface SavedItemControllerGetAllSavedGiftCardsParams {
  /** @example "and" */
  searchParam: string;
  sortType?: SortTypes;
  /** @example "2,4,6,7" */
  subCategories?: string;
}

export type SavedItemControllerGetAllSavedOfferUidsData = number[];

export type SavedItemControllerGetAllSavedStoresData = GetAllStoresResponse;

export interface SavedItemControllerGetAllSavedStoresParams {
  /** @example "60" */
  maxPercent: number;
  /** @example "10" */
  minPercent: number;
  saved: any;
  /** @example "and" */
  searchParam: string;
  sortType: SortTypes;
  /** @example "2,4,6,7" */
  subCategories?: string;
}

export type SearchControllerGetSearchResultsData = any;

export interface SearchControllerGetSearchResultsParams {
  text: string;
}

export interface SearchCouponResponse {
  couponList: SearchResponseType[];
  total: number;
}

export interface SearchDealResponse {
  dealList: SearchResponseType[];
  total: number;
}

export interface SearchGiftCardResponse {
  giftCardList: SearchResponseType[];
  total: number;
}

export interface SearchResponseItem {
  coupons: SearchCouponResponse;
  deals: SearchDealResponse;
  giftCards: SearchGiftCardResponse;
  stores: SearchStoreResponse;
  /** Spelling suggestions for misspelled search terms (supports multi-word corrections) */
  suggestions?: SearchSuggestion[];
}

export interface SearchResponseType {
  caption: string;
  count: number;
  couponCode: string;
  name: string;
  newUserOffer: string;
  oldUserOffer: string;
  title: string;
  uid: number;
  url: string;
}

export interface SearchStoreResponse {
  storeList: SearchResponseType[];
  total: number;
}

export interface SearchSuggestion {
  /** Confidence score between 0 and 1 (higher is better) */
  confidence: number;
  /** Suggested corrected search term (supports multi-word phrases) */
  suggestion: string;
}

export interface SendMessageDto {
  /**
   * The message content to send to the bot
   * @example "Hello, this is a test message from the API"
   */
  msg: string;
  /**
   * The unique identifier for the conversation thread
   * @example "1262"
   */
  threadId: string;
}

export interface SendMessageResponseDto {
  /**
   * Response message indicating success
   * @example "Message sent successfully"
   */
  message: string;
  /**
   * The thread ID where the message was sent
   * @example "1262"
   */
  threadId: string;
}

export type SitemapControllerGenerateSitemapData = object;

export enum SortTypes {
  Newest = "newest",
  Discount = "discount",
  HighestCbPercent = "highestCbPercent",
  HighestCbAmount = "highestCbAmount",
  Alphabetical = "alphabetical",
  Popular = "popular",
}

/** @default "active" */
export enum StatusEnum {
  Active = "active",
  Inactive = "inactive",
  Blocked = "blocked",
}

/** @example "cancelled,pending,confirmed" */
export enum StatusEnum1 {
  Pending = "pending",
  Confirmed = "confirmed",
  Cancelled = "cancelled",
}

export interface StoreCard {
  bgColor: string;
  caption: string;
  imageUrl: string;
  saved: boolean;
  storeName: string;
  uid: number;
}

export type StoreControllerGetAllStoresData = GetAllStoresResponse;

export interface StoreControllerGetAllStoresParams {
  /** @example "60" */
  maxPercent: number;
  /** @example "10" */
  minPercent: number;
  saved: any;
  /** @example "and" */
  searchParam: string;
  sortType: SortTypes;
  /** @example "2,4,6,7" */
  subCategories?: string;
}

export type StoreControllerGetCashbackRatesByStoreIdData =
  GetCashbackRatesByStoreResponse;

export type StoreControllerGetStoreDetailsByNameData = GetStoreDetailsResponse;

export type StoreControllerRemoveSavedItemData = any;

export type StoreControllerSaveItemData = any;

export interface StoreGiftCard {
  name: string;
  uid: number;
}

export interface StoreType {
  clicks: ClickByStoreType[];
  count: number;
  logo: string;
  name: string;
  uid: number;
}

export interface StoresByCbContextResponse {
  "100pc": ContextStore[];
  "25pc": ContextStore[];
  "50pc": ContextStore[];
}

export type StoresControllerGetAllCategoriesData = any;

export type StoresControllerGetContextStoresByCbData =
  StoresByCbContextResponse;

export type StoriesControllerGetStoriesData = ResponseMobileStories[];

export interface SubCategoriesByCategory {
  iconUrl: string;
  title: string;
  uid: number;
}

export interface SubCategoriesByCategoryResponse {
  latestOffer: SubCategoryOffer;
  stores: SubCategoryStore[];
  subCategories: SubCategoriesByCategory[];
}

export interface SubCategoryOffer {
  offerImage: string;
  offerTitle: string;
  offerUrl?: string;
  storeName: string;
  uid: number;
}

export interface SubCategoryStore {
  bgColor: string;
  caption: string;
  imageUrl: string;
  saved: boolean;
  storeName: string;
  uid: number;
}

export type TermsAndPrivacyControllerGetAllTermsAndConditionsData = string;

export interface TermsAndPrivacyControllerGetAllTermsAndConditionsParams {
  /** @example "terms" */
  type: string;
}

export interface TermsAndPrivacyResponseItem {
  termsAndPrivacy: TermsAndPrivacyResponseType;
}

export interface TermsAndPrivacyResponseType {
  content: string;
  type: string;
  uid: number;
}

export type TestimonialControllerGetAllTestimonialsData =
  TestimonialResponseType[];

export interface TestimonialResponseType {
  rating: number;
  review: string;
  reviewerAvatar: string;
  reviewerName: string;
}

export enum TimeFrameEnum {
  Weekly = "weekly",
  Monthly = "monthly",
  All = "all",
}

export interface TrendingOfferResponseType {
  coupons: ContextOfferCouponsType[];
  deals: ContextOfferDealsType[];
}

export interface UpdateBankAccountDto {
  /**
   * Account Number
   * @example "************** "
   */
  accountNumber: string;
  /**
   * Address of the bank account holder
   * @example "2393 15B Cross Road
   * 	 Building Sector 1
   * 	 Bengaluru
   * 	 Bangalore South
   * 	 Karnataka
   * 	 India "
   */
  address: string;
  /**
   * Bank Name
   * @example "HDFC"
   */
  bankName: string;
  /**
   * Branch Name
   * @example "HDFC Bank, Koramangala"
   */
  branchName: number;
  /**
   * Full name as per the bank account holder
   * @example "John Doe"
   */
  holderName: string;
  /**
   * Bank IFSC code
   * @example "Hdfc@0koramangala"
   */
  ifsc: string;
  /**
   * Postcode/Zipcode of the bank account holder
   * @example "560068"
   */
  postcode: number;
}

export interface UpdateCredentialsDto {
  /** @example "<EMAIL>" */
  email?: string;
  /**
   * Mobile Number
   * @example **********
   */
  mobile?: number;
  /**
   * Please add the type mobile or email
   * @example "email"
   */
  type: UpdateCredentialsDtoTypeEnum;
}

/**
 * Please add the type mobile or email
 * @example "email"
 */
export enum UpdateCredentialsDtoTypeEnum {
  Mobile = "mobile",
  Email = "email",
}

export interface UpdateProfileDto {
  /**
   * Name of the user
   * @example "John Doe"
   */
  name: string;
  /**
   * Select multiple categories you are interested from this list
   * @example "6f9d88f7d6f3e3b3f8f3f8f3"
   */
  personalInterest?: ObjectId;
  /**
   * select true or false
   * @example true
   */
  sendNotification?: boolean;
}

export interface UpdateProfileWithImageDto {
  profileData?: UpdateProfileDto;
  /** @format binary */
  profileImage?: File;
}

export interface UpdateUpiDto {
  /**
   * Upi id
   * @example "Doe1232@okhdfcJohn"
   */
  upi: string;
}

export interface UserAnalyticsResponseDto {
  /** Conversion rate in the current period */
  conversionRate: MetricWithChangeDto;
  /** Total cashback earned in the current period */
  totalCashbackEarned: MetricWithChangeDto;
  /** Total clicks in the current period */
  totalClicks: MetricWithChangeDto;
}

export type UserControllerGetAllPersonalInterestDataData = any;

export type UserControllerGetBankDetailsData = GetBankAccountDataResponse;

export type UserControllerGetCashbackHistoryData = GetCbHistoryResponse;

export interface UserControllerGetCashbackHistoryParams {
  /** @default "2024-12-31" */
  endDate: string;
  /** @example "a" */
  searchParam: string;
  sortType?: CashbackSortTypes;
  /** @default "2023-10-01" */
  startDate: string;
  /** @example "cancelled,pending,confirmed" */
  status?: StatusEnum1;
  /** @example "1,2,3" */
  stores?: string;
  /**
   * Filter by earnings type (comma-separated values: click,missing,referral,share)
   * @example "click,missing,referral"
   */
  type?: string;
}

/** @example "cancelled,pending,confirmed" */
export enum UserControllerGetCashbackHistoryParams1StatusEnum {
  Pending = "pending",
  Confirmed = "confirmed",
  Cancelled = "cancelled",
}

export type UserControllerGetProfileDetailsData = GetUserProfileResponseItem;

export type UserControllerGetUsersByReferralCodeData =
  GetUsersByReferralCodeResponse;

export interface UserControllerGetUsersByReferralCodeParams {
  /** @example "2021-10-10" */
  endDate?: string;
  /** @example "a" */
  searchParam?: string;
  sortType?: ReferralTypes;
  /** @example "2021-10-10" */
  startDate?: string;
  /** @default "active" */
  status?: StatusEnum;
}

/** @default "active" */
export enum UserControllerGetUsersByReferralCodeParams1StatusEnum {
  Active = "active",
  Inactive = "inactive",
  Blocked = "blocked",
}

export type UserControllerGetUsersOverViewDetailsData = any;

export type UserControllerSendOtpToUpdateUserCredentialsData = any;

export type UserControllerUpdateAllUsersReferralCodeData = any;

export type UserControllerUpdateBankDetailsData = boolean;

export type UserControllerUpdateProfileData = boolean;

export type UserControllerUpdateUpiIdData = boolean;

export type UserControllerVerifyOtpToUpdateCredentialsData = boolean;

export interface UserData {
  avatar: string;
  joined: string;
  name: string;
  status: UserDataStatusEnum;
  totalConfirmedReferralCommission: number;
  totalOrders: number;
  totalPendingReferralCommission: number;
  uid: number;
}

export enum UserDataStatusEnum {
  Active = "active",
  Inactive = "inactive",
  Blocked = "blocked",
}

export interface UserListItemDto {
  email: string;
  /** @format date-time */
  lastLoggedDate?: string;
  migrated: boolean;
  mobile?: number;
  mobileVerified: boolean;
  name: string;
  referralCode: string;
  referredByEmail?: string;
  status: string;
  uid: number;
  userNotes?: string;
}

export interface UserListResponseDto {
  users: UserListItemDto[];
}

export interface UserOverviewResponse {
  flipkartRewardPoints: number;
  readyToWithdraw: number;
  shareAndEarnCashback: number;
  shareAndEarnRewards: number;
  totalApprovedCount: number;
  totalCancelledCount: number;
  totalCashback: number[];
  totalCashbackEarned: number;
  totalClicks: number[];
  totalOrderAmount: number[];
  totalPendingCount: number;
  totalReferralCommission: number;
}

export interface UserResponse {
  credential?: string;
  message: string;
}

export enum UserTypes {
  New = "new",
  Existing = "existing",
  Both = "both",
}

export interface VerifyUserDto {
  /**
   * User Email
   * @example "<EMAIL>"
   */
  email?: string;
  /**
   * User's mobile
   * @example "9090888899"
   */
  mobile?: string;
  /**
   * User verification otp
   * @example 789876
   */
  otp: number;
}

export type WishListControllerJoinWishlistData = any;

export interface WishlistEmailDto {
  /**
   * User Email for wishlist
   * @example "<EMAIL>"
   */
  email?: string;
}

export interface WishlistResponseDto {
  /**
   * Response message
   * @example "Added to wishlist"
   */
  message: string;
}

export interface ShareAndEarnOfferDto {
  /** Offer UID */
  uid: number;
  /** Product image URL */
  productImage: string;
  /** Store logo URL */
  storeLogoUrl: string;
  /** Store background color */
  storeBgColor: string;
  /** Store name */
  storeName: string;
  /** Offer end date */
  endDate: string;
  /** Offer title */
  offerTitle: string;
  /** Offer caption */
  offerCaption: string;
  /** Sale price */
  salePrice: number;
  /** Coupon code if available */
  couponCode?: string;
  /** Short URL for sharing */
  shortUrl: string;
  /** Whether the offer is auto-generated */
  isAutoGenerated?: boolean;
  /** Whether to hide cashback tag */
  hideCbTag?: boolean;
}
